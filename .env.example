# Supabolt Environment Configuration
# Copy this file to .env and fill in your values

# =====================
# CORE CONFIGURATION
# =====================

# Required: Anthropic API key for compliance orchestrator
ANTHROPIC_API_KEY=sk-ant-api03-...

# Optional: LangSmith for workflow tracing (recommended for debugging)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGCHAIN_PROJECT=supabolt-compliance
LANGCHAIN_API_KEY=lsv2_pt_your_langsmith_key

# Environment: development, production, test
NODE_ENV=development
LOG_LEVEL=info

# =====================
# BITCOIN AGENT
# =====================

# Bitcoin Network (REQUIRED for safety - no dangerous defaults)
# Options: mainnet, testnet, regtest
# This setting prevents accidental mainnet operations
BITCOIN_NETWORK=testnet

# Bitcoin Core RPC
# Host is optional - defaults to network-specific port:
# - mainnet: 127.0.0.1:8332
# - testnet: 127.0.0.1:18332
# - regtest: 127.0.0.1:18443
# BITCOIN_RPC_HOST=127.0.0.1:18332
BITCOIN_RPC_USER=your_username
BITCOIN_RPC_PASSWORD=your_password

# =====================
# LIGHTNING AGENT
# =====================

# Lightning Network configuration (REQUIRED - must match Bitcoin network)
# Lightning operates on the Bitcoin network layer
# BITCOIN_NETWORK setting above applies to Lightning as well

# Lightning Network Daemon connection
LND_SOCKET=localhost:10009
LND_MACAROON_PATH=/path/to/admin.macaroon
# LND_TLS_CERT_PATH=/path/to/tls.cert
LND_CONFIG_PATH=.env.voltage
LND_USE_TOR=false

# Enable both stdio and HTTP when running lightning agent locally.
MCP_STDIO_ENABLED=true
MCP_HTTP_ENABLED=true
LOG_STDOUT_CLEAN=true

# For cloud nodes (e.g., Voltage), omit TLS cert path:
# LND_SOCKET=your-node.voltage.cloud:10009
# LND_MACAROON_PATH=/path/to/admin.macaroon

# =====================
# BITCOIN AWS AGENT
# =====================

# Uses same BITCOIN_NETWORK setting as above
# AWS automatically maps to internal network names:
# - mainnet → BITCOIN_MAINNET
# - testnet → BITCOIN_TESTNET

# AWS credentials (use IAM role or access keys)
AWS_REGION=us-east-1
# Option 1: IAM Role (recommended)
# AWS_ROLE_ARN=arn:aws:iam::123456789012:role/BlockchainQueryRole
# AWS_ROLE_SESSION_NAME=bitcoin-aws-agent
# Option 2: Access keys (less secure)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
MAX_OUTPUT_SIZE=10240
CACHE_TTL=300

# =====================
# SANCTIONS AGENT
# =====================

# OpenSanctions API key (required for sanctions screening)
OPENSANCTIONS_API_KEY=your_opensanctions_api_key

# =====================
# USDC AGENT
# =====================

# Ethereum Network (REQUIRED for safety)
# Options: mainnet, sepolia
# Note: Goerli was deprecated in April 2024
ETHEREUM_NETWORK=mainnet

# Ethereum RPC endpoint for USDC monitoring
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_project_id

# Future multi-chain support:
# POLYGON_RPC_URL=https://polygon-rpc.com
# ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc

# =====================
# BLOCKCHAIN AGENT
# =====================

# BitQuery API for natural language blockchain queries
BITQUERY_API_KEY=your_bitquery_api_key

# =====================
# SANDBOX / TOOLING
# =====================

# Expose local tunnel for demos (optional)
NGROK_AUTHTOKEN=your_ngrok_token

# npm token for accessing private packages (do not commit real token)
NPM_TOKEN=your_github_packages_token

# =====================
# PRODUCTION ONLY
# =====================

# Redis for workflow persistence (production only)
REDIS_URL=redis://localhost:6379

# =====================
# OPTIONAL SETTINGS
# =====================

# Model selection (defaults to Claude 3.5 Sonnet)
MODEL_NAME=claude-3-5-sonnet-20241022
MODEL_TEMPERATURE=0
MODEL_MAX_TOKENS=4096

# Agent timeouts (milliseconds)
AGENT_TIMEOUT=30000
MAX_RETRIES=3
RETRY_DELAY_MS=1000
