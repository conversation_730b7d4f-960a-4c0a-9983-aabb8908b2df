# Schema Unification Migration Plan

## Executive Summary

**Goal**: Standardize input schema across all 13 Supabolt MCP agents to eliminate drift, improve maintainability, and reduce bugs.

**Status**: Foundation complete, ready for phased rollout
**Timeline**: 2-3 weeks for full migration
**Risk**: Medium (breaking change, requires careful testing)

## Problem Statement

### Current State (Inconsistent)

```typescript
// bitcoin-aws-agent
{ entity: "bc1q...", entityType: "address", network: "bitcoin" }

// ofac-agent
{ entityId: "bc1q...", entityType: "person" }

// analytics-agent
{ entityId: "bc1q..." }
```

**Issues**:
- 3 different field names for same concept (`entity` vs `entityId`)
- Flat vs nested structures
- No standard tracing field (`workflowId`)
- Tool-specific params mixed with common params
- Each agent requires custom handling in `mcp_nodes.py`

### Target State (Unified)

```typescript
{
  entity: {
    entityId: "bc1q...",
    entityType: "bitcoin_address"
  },
  workflowId: "demo-001",
  network: "bitcoin",
  options: {
    // Tool-specific params here
    timeframe: { start: "2025-01-01", end: "2025-02-01" },
    maxResults: 100
  }
}
```

**Benefits**:
- ✅ Single schema for all agents
- ✅ Standard tracing via `workflowId`
- ✅ Clear separation: common params vs tool options
- ✅ Simplified `mcp_nodes.py` (no per-agent special casing)
- ✅ Better testability and introspection

## Implementation

### Phase 1: Foundation (COMPLETE ✅)

**Deliverables**:
- [x] `UnifiedToolInputSchema` in mcp-core
- [x] `migrateLegacyInput()` helper for backward compat
- [x] Helper functions (`extractEntity`, `getOption`)
- [x] Export from mcp-core v1.0.7
- [x] Documentation

**Files Modified**:
- `packages/mcp-core/src/schemas/unified-tool-input.ts` (NEW)
- `packages/mcp-core/src/schemas/index.ts` (NEW)
- `packages/mcp-core/src/index.ts` (updated exports)

### Phase 2: Proof of Concept (NEXT)

**Objective**: Migrate one agent end-to-end to validate approach

**Target Agent**: `ofac-agent` (simplest schema)

**Steps**:
1. Update `check-ofac-status.ts` to use `UnifiedToolInputSchema`
2. Add backward compatibility layer
3. Update `mcp_nodes.py` to send unified format to ofac-agent
4. Update tests
5. Verify E2E with golden dataset

**Success Criteria**:
- All ofac-agent tests passing
- Golden dataset tests passing
- No regression in other agents

**Estimated Time**: 2-3 days

### Phase 3: Core Agents Migration

**Target Agents** (in order):
1. `sanctions-agent` - Similar to OFAC, simple schema
2. `analytics-agent` - Currently minimal schema
3. `bitcoin-aws-agent` - More complex, has `options`
4. `lightning-agent` - Complex, channel-specific params
5. `usdc-agent` - Stablecoin-specific

**Per-Agent Process**:
1. Update tool schema to use/extend `UnifiedToolInputSchema`
2. Add backward compat via `migrateLegacyInput()`
3. Update `mcp_nodes.py` routing
4. Update agent tests
5. Update golden dataset tests
6. Deploy and verify

**Estimated Time**: 1 week (2 agents/day avg)

### Phase 4: Remaining Agents

**Target Agents**:
- `blockchain-agent`
- `bitcoin-aws-agent` (if not in Phase 3)
- `spark-agent`
- `internal-ai-agent`
- `synthesis-agent`

**Estimated Time**: 4-5 days

### Phase 5: Cleanup

**Tasks**:
- Remove backward compatibility helpers
- Remove per-agent special cases from `mcp_nodes.py`
- Update all documentation
- Add schema validation CI checks
- Publish final mcp-core v2.0.0

**Estimated Time**: 2-3 days

## Migration Guide (Per Agent)

### 1. Update Tool Schema

**Before** (`ofac-agent`):
```typescript
const checkOFACStatusSchema = z.object({
  entityId: z.string().min(1),
  entityType: z.enum(['person', 'company', ...]).optional(),
  threshold: z.number().optional(),
  limit: z.number().optional(),
});
```

**After**:
```typescript
import { UnifiedToolInputSchema, getOption } from '@supabolt/mcp-core';

// Extend for tool-specific validation
const checkOFACStatusSchema = UnifiedToolInputSchema.extend({
  options: z.object({
    threshold: z.number().min(0).max(100).optional(),
    limit: z.number().int().min(1).max(20).optional(),
  }).optional(),
});
```

### 2. Update Tool Implementation

**Before**:
```typescript
protected async execute(context: OFACContext, params: CheckOFACStatusParams) {
  const { entityId, entityType, threshold, limit } = params;
  // ...
}
```

**After**:
```typescript
import { extractEntity, getOption } from '@supabolt/mcp-core';

protected async execute(context: OFACContext, params: CheckOFACStatusParams) {
  const { entityId, entityType } = extractEntity(params);
  const threshold = getOption(params, 'threshold', 85);
  const limit = getOption(params, 'limit', 20);
  // ...
}
```

### 3. Update mcp_nodes.py

**Before** (per-agent custom logic):
```python
elif agent == "ofac-agent":
    tool_params.update({
        "entityId": entity_id,
        "entityType": agent_entity_type,
    })
```

**After** (unified for all agents):
```python
# Universal handler - works for ALL agents
tool_params = {
    "workflowId": workflow_id,
    "entity": {
        "entityId": entity_id,
        "entityType": agent_entity_type,
    },
    "network": runtime_config.get("network"),
}

# Tool-specific options from manifest
if "options" in agent_cfg.parameters:
    tool_params["options"] = agent_cfg.parameters["options"]
```

### 4. Update Tests

**Golden Dataset**:
```python
GOLDEN_RESPONSES = {
    "clean-bitcoin-address": {
        "ofac-agent": {
            "factualObservations": [...],
            # Agent receives unified format automatically
        }
    }
}
```

**Agent Tests**:
```typescript
const result = await tool.execute(mockContext, {
  entity: {
    entityId: "******************************************",
    entityType: "bitcoin_address",
  },
  workflowId: "test-001",
  network: "bitcoin",
  options: {
    maxResults: 50,
  },
});
```

## Backward Compatibility Strategy

### Option 1: Automatic Migration (Recommended)

Add to BaseTool in mcp-core:
```typescript
async handler(context: C, params: unknown): Promise<ToolResponse> {
  // Auto-migrate legacy format
  const migrated = migrateLegacyInput(params as Record<string, unknown>);
  const validated = this.schema.parse(migrated);
  return this.execute(context, validated);
}
```

**Pros**:
- Agents work with both old and new format
- No code changes needed immediately
- Gradual migration

**Cons**:
- Small performance overhead
- Complexity in base layer

### Option 2: Explicit Migration Layer

Each agent decides when to migrate:
```typescript
const schema = this.supportLegacy
  ? z.union([UnifiedToolInputSchema, LegacySchema]).transform(migrateLegacyInput)
  : UnifiedToolInputSchema;
```

**Pros**:
- Agents control migration timing
- Clear migration state

**Cons**:
- More per-agent code
- Longer migration period

**Recommendation**: Use Option 1 for initial rollout, then remove after all agents migrated.

## Risk Mitigation

### Testing Strategy

1. **Unit Tests**: Each agent's tool tests
2. **Integration Tests**: `test_golden_dataset.py` (10 scenarios)
3. **E2E Tests**: Full workflow with real supervisor
4. **Regression Tests**: Verify no behavior changes

### Rollback Plan

1. **mcp-core versioning**: Pin agents to v1.0.6 if issues
2. **Feature flags**: `USE_UNIFIED_SCHEMA` env var
3. **Git branches**: Separate branch per agent migration
4. **Monitoring**: Track errors per agent

### Validation Checkpoints

After each agent migration:
- [ ] All agent tests passing
- [ ] Golden dataset tests passing
- [ ] No increase in error rates
- [ ] Manual smoke test successful

## Success Metrics

**Technical**:
- 13/13 agents using unified schema
- `mcp_nodes.py` reduced from 50+ lines to 10 lines
- 0 schema-related bugs in Q1 2026
- Test coverage >90%

**Operational**:
- Onboarding new agent: 1 hour (down from 4 hours)
- Schema change propagation: 1 commit (down from 13 commits)
- Debug time for param issues: -75%

## Timeline

| Phase | Duration | Start | End | Status |
|-------|----------|-------|-----|--------|
| 1. Foundation | 1 day | 2025-11-03 | 2025-11-03 | ✅ Complete |
| 2. Proof of Concept | 3 days | 2025-11-04 | 2025-11-06 | Pending |
| 3. Core Agents | 7 days | 2025-11-07 | 2025-11-13 | Pending |
| 4. Remaining Agents | 5 days | 2025-11-14 | 2025-11-18 | Pending |
| 5. Cleanup | 3 days | 2025-11-19 | 2025-11-21 | Pending |

**Total**: 19 days (3.8 weeks)

## Next Steps

1. **Review this plan** with team
2. **Start Phase 2** with ofac-agent
3. **Schedule** agent migration slots
4. **Update** project board with migration tasks

---

**Document Owner**: Claude Code
**Last Updated**: 2025-11-03
**Version**: 1.0
**Status**: Draft - Awaiting Review
