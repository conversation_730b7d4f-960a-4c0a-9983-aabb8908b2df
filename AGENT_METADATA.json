{"analytics-agent": {"status": "active", "auditStatus": "passed", "hasArchitecture": true, "hasClaudeFile": true, "hasReadme": true, "packageVersion": "0.1.0", "toolCount": 6, "tools": ["analyzeCase", "analyzeComplianceData", "detectPatterns", "explainPattern", "generateNarrative", "getAgentStatus"]}, "bitcoin-agent": {"status": "active", "auditStatus": "passed", "hasArchitecture": true, "hasClaudeFile": true, "hasReadme": true, "packageVersion": "1.2.0", "toolCount": 7, "tools": ["getAddressInfo", "getAddressTransactions", "getAgentStatus", "getBlockchainInfo", "getMempoolInfo", "getRawMempool", "getTransaction"]}, "bitcoin-aws-agent": {"status": "active", "auditStatus": "passed", "hasArchitecture": true, "hasClaudeFile": true, "hasReadme": true, "packageVersion": "1.0.0", "toolCount": 5, "tools": ["analyzeFundsFlow", "analyzeLightningCompliance", "getAgentStatus", "investigateEntity", "queryBitcoinActivity"]}, "blockchain-agent": {"status": "active", "auditStatus": "passed", "hasArchitecture": true, "hasClaudeFile": true, "hasReadme": true, "packageVersion": "1.0.0", "toolCount": 2, "tools": ["getAgentStatus", "queryBlockchain"]}, "lightning-agent": {"status": "active", "auditStatus": "passed", "hasArchitecture": true, "hasClaudeFile": true, "hasReadme": true, "packageVersion": "1.0.0", "toolCount": 11, "tools": ["analyzeNetworkGraph", "getAgentStatus", "getChannelBalance", "getChannelInfo", "getLocalBalance", "getNetworkInfo", "getNodeInfo", "getRemoteNode", "getSchemaInfo", "getWalletBalance", "listChannels"]}, "sanctions-agent": {"status": "active", "auditStatus": "passed", "hasArchitecture": true, "hasClaudeFile": true, "hasReadme": true, "packageVersion": "1.0.0", "toolCount": 4, "tools": ["batchCheckSanctionsStatus", "checkSanctionsStatus", "getAgentStatus", "getToolInfo"]}, "ofac-agent": {"status": "active", "auditStatus": "passed", "hasArchitecture": true, "hasClaudeFile": true, "hasReadme": true, "packageVersion": "1.0.0", "toolCount": 5, "tools": ["getAgentStatus", "batchCheckOFACStatus", "checkOFACStatus", "getOFACListStatus", "updateOFACLists"], "auditNotes": "No test suite"}, "spark-agent": {"status": "active", "auditStatus": "passed", "hasArchitecture": true, "hasClaudeFile": true, "hasReadme": true, "packageVersion": "0.1.0", "toolCount": 2, "tools": ["getAgentStatus", "getSparkTransfers"]}, "usdc-agent": {"status": "active", "auditStatus": "passed", "hasArchitecture": true, "hasClaudeFile": true, "hasReadme": true, "packageVersion": "1.0.0", "toolCount": 2, "tools": ["getAgentStatus", "getUsdcBalance"]}, "supabolt-supervisor": {"status": "active", "auditStatus": "in-progress", "hasArchitecture": true, "hasClaudeFile": false, "hasReadme": true, "packageVersion": "0.1.0", "toolCount": 0, "tools": [], "note": "Python LangGraph 1.x supervisor coordinating MCP agents", "auditNotes": "Uses pytest; see supabolt-supervisor/README.md"}, "internal-ai-agent": {"status": "internal", "auditStatus": "passed", "hasArchitecture": true, "hasClaudeFile": true, "hasReadme": true, "packageVersion": "0.1.0", "toolCount": 8, "tools": ["getAgentStatus", "ap2<PERSON><PERSON><PERSON><PERSON>", "complianceStrategist", "deliveryLead", "gtmAnalyst", "langgraphArchitect", "siteReliabilityEngineer", "technicalProductManager"], "auditNotes": "Internal agent - no test suite by design"}, "synthesis-agent": {"status": "design-only", "auditStatus": "n/a", "hasArchitecture": true, "hasClaudeFile": true, "hasReadme": true, "packageVersion": "0.0.0", "toolCount": 0, "tools": [], "auditNotes": "Future agent - design phase only, no implementation yet"}}