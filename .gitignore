# Dependencies
node_modules/
*/node_modules/

# Build outputs
build/
dist/
*/build/
coverage/

# Prevent compiled files in src directories
src/**/*.js
src/**/*.js.map
src/**/*.d.ts
src/**/*.d.ts.map
**/src/**/*.js
**/src/**/*.js.map
**/src/**/*.d.ts
**/src/**/*.d.ts.map
# Exception for test files
!src/**/*.test.ts
!**/src/**/*.test.ts

# Environment
.env
.env.local
.env.*
!.env.example

# IDE
.vscode/
.idea/

# OS
.DS_Store

# Optional: External documentation
# Uncomment if you want to exclude external reference docs from git
.claude/docs/external/

# Test artifacts
*.log
test-results/
.nyc_output/

# MCP artifacts
.supabolt-context.json
.orchestrator.pid
# LangGraph API
.langgraph_api
langgraph.json
localstack-data/
scripts/venv/

# Claude Desktop config (should not be in project)
.claude.json

# Claude local settings (should not be in project)
.claude/settings.local.json

# TypeScript build info
*.tsbuildinfo
**/*.tsbuildinfo

# Independent repos (not submodules)
*-agent/
packages/
supabolt-supervisor/

# Local MCP configuration with credentials
mcp.json
