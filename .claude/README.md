# Claude Code Configuration

This directory contains Claude Code configuration, commands, and documentation for the Supabolt project.

## Available Slash Commands

### System Analysis

- **`/prime`** - Initialize deep understanding of the entire codebase
  - Dynamically discovers all agents (\*-agent directories)
  - Loads source code and documentation
  - Provides system architecture overview with LangG<PERSON>h supervisor
  - Analyzes MCP SDK facade implementation and version management
  - Reviews strategic next steps based on roadmap

### Agent-Specific Analysis

- **`/prime/supervisor`** - Deep dive into the Python supabolt-supervisor orchestration layer
- **`/prime/analytics`** - Deep dive into Analytics agent (proprietary intelligence engine)
- **`/prime/lightning`** - Deep dive into Lightning agent
- **`/prime/bitcoin`** - Deep dive into Bitcoin agent
- **`/prime/sanctions`** - Deep dive into Sanctions agent
- **`/prime/chainalysis`** - Deep dive into Chainalysis agent (BYOK)
- **`/prime/bitquery`** - Deep dive into BitQuery agent (Our Service)

### Business & Strategy Commands

- **`/investor-critique`** - Get a ruthless crypto/AI investor's perspective

  - Why Supabolt won't get funded
  - What would change their mind
  - Strategic pivots needed

- **`/architecture-review`** - Strategic architecture assessment

  - Scalability analysis
  - Security architecture
  - Technical debt and risks
  - `packages/mcp-core` integration
  - LangGraph implementation

- **`/compliance-strategy`** - Compliance market positioning
  - Regulatory coverage
  - Competitive analysis
  - Go-to-market strategy

### Reference Commands

- **`/reference/lnd`** - Lightning Network Daemon reference
- **`/reference/mcp`** - Model Context Protocol reference

## Natural Language Alternative

You don't need to use slash commands for basic operations. Simply ask:

- "Analyze the lightning agent" - Loads code and docs
- "Show me bitcoin agent source code" - Loads only source
- "What MCP tools does sanctions agent have?" - Focused analysis

## Directory Structure

```shell
.claude/
├── commands/         # Slash command definitions
│   ├── prime.md
│   ├── prime/
│   │   ├── lightning.md
│   │   ├── bitcoin.md
│   │   └── sanctions.md
│   ├── investor-critique.md
│   ├── architecture-review.md
│   ├── compliance-strategy.md
│   └── reference/
│       ├── lnd.md
│       └── mcp.md
├── docs/            # Reference documentation
│   ├── external/    # External specs and guides
│   └── internal/    # Project-specific docs
└── settings.local.json  # Local permissions
```

## Key Patterns & Principles

### MCP SDK Management

- **Centralized Facade**: All agents use `@supabolt/mcp-core` for MCP SDK imports
- **Peer Dependencies**: MCP SDK installed as peer dependency to ensure version consistency
- **Version Control**: Single source of truth for MCP SDK version (1.15.0)
- **Migration Guide**: `MCP_SDK_MIGRATION.md` provides comprehensive upgrade instructions

### LangGraph Supervisor Architecture

- **supabolt-supervisor**: Python LangGraph 1.x orchestrator coordinating MCP agents
- **Specialist Agents**: Domain experts that provide focused capabilities via MCP protocol
- **MCP Communication**: All inter-agent communication flows through Model Context Protocol

### Development Principles

1. **Dynamic Discovery** - New agents are automatically included
2. **Efficient Loading** - Only loads src/ files, excludes build artifacts
3. **Business Focus** - Commands that provide strategic value
4. **Architecture First** - MCP SDK facade and LangGraph patterns documented comprehensively
5. **KISS** - Simple, maintainable, and scalable
