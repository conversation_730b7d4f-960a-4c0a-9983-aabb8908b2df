# Supabolt System Overview

## 1. Architecture Summary

Su<PERSON><PERSON> implements Unix philosophy for digital asset compliance through a **LangGraph + MCP multi-agent architecture**. The Python-based `supabolt-supervisor` acts as the intelligent shell that coordinates specialized MCP agents, each doing ONE thing well. All agents communicate via Model Context Protocol, with standardization enforced through the @supabolt/mcp-core facade that manages MCP SDK version consistency across the platform.

The architecture transforms compliance from manual processes to automated workflows - like Unix pipes, data flows through specialized agents that filter, transform, and enrich it. The orchestrator uses LangGraph's StateGraph for complex workflow management, conditional routing, and state persistence (Redis in production). This design enables sub-second compliance checks that previously took hours, with complete audit trails for regulatory requirements.

## 2. Available Capabilities

| Agent                       | Purpose                                    | Tools | Primary Function                         | Key Integrations     |
| --------------------------- | ------------------------------------------ | ----- | ---------------------------------------- | -------------------- |
| **supabolt-supervisor**     | Python LangGraph 1.x workflow orchestration | REST  | Workflow orchestration                   | All agents via MCP   |
| **analytics-agent**         | Proprietary compliance intelligence engine | 4     | Pattern detection & narrative generation | Orchestrator         |
| **bitcoin-agent**           | Pure Bitcoin blockchain data provider      | 8     | Bitcoin data retrieval                   | Lightning, Sanctions |
| **lightning-agent**         | Production Lightning Network access        | 12    | Lightning Network operations             | Bitcoin, Sanctions   |
| **sanctions-agent**         | Sanctions screening across 250+ lists      | 4     | Entity screening                         | All agents           |
| **usdc-agent**              | USDC compliance across multiple chains     | 1     | Stablecoin tracking                      | Orchestrator         |
| **blockchain-agent**        | Natural language blockchain queries        | 2     | Customer blockchain queries              | BitQuery API         |

These agents compose following Unix philosophy - small, focused tools that combine to solve complex compliance challenges. For example: `lightning-agent get-channel-info | bitcoin-agent trace-funding | sanctions-agent screen` becomes a complete channel assessment when orchestrated by `supabolt-supervisor`.

## 3. Quick Start Guide

**To analyze a specific agent:**

```bash
# Natural language works best
"Analyze the bitcoin agent"
"Show me lightning agent architecture"
"What tools does sanctions agent have?"

# Or use slash commands
/prime:bitcoin
/prime:lightning
```

**To find compliance workflows:**

- Supervisor workflows live in Python: `supabolt-supervisor/workflow.py`
- MCP process orchestration: `supabolt-supervisor/mcp/process_client.py`
- LangGraph manifests: `supabolt-supervisor/manifests/`

**Key files for understanding the system:**

- `CLAUDE.md` - Core principles and agent boundaries
- `supabolt-supervisor/README.md` - Supervisor overview & commands
- `supabolt-supervisor/workflow.py` - Orchestration logic
- `supabolt-supervisor/mcp/process_client.py` - MCP stdio transport
- `packages/mcp-core/` - Standardized MCP foundation
- Each agent's `ARCHITECTURE.md` - Domain-specific design

## 4. What Makes This Special

- **Unix Philosophy Applied**: Each agent masters one domain completely. Complex compliance emerges from simple composition.
- **First Lightning Native**: Deep Lightning Network compliance with 12 specialized tools for channel analysis.
- **Platform Architecture**: @supabolt/mcp-core facade ensures all agents work together seamlessly.
- **$190B+ Opportunity**: USDC agent targets massive stablecoin compliance market.
- **Facts Only**: Agents return factual observations, not subjective risk scores - defensible compliance.
- **Workflow Automation**: Proactive 24/7 compliance vs reactive manual checks.

The system is designed for the future where compliance workflows run like code - automated, auditable, and scalable.

---

_Generated from the `/prime` command - Last updated: 2025-07-22_
