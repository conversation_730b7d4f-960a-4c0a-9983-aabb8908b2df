#!/bin/bash
# Tool counting hook for automatic documentation updates
# Maintains accurate tool counts in documentation

# Read input
INPUT=$(cat)
TOOL_NAME=$(echo "$INPUT" | jq -r '.tool_name')
FILE_PATH=$(echo "$INPUT" | jq -r '.tool_input.file_path')

# Only process tool file changes
if [[ ! "$FILE_PATH" =~ src/mcp/tools/.*\.ts$ ]]; then
    exit 0
fi

# Extract agent from path
AGENT=""
if [[ "$FILE_PATH" =~ ([^/]+-agent)/ ]]; then
    AGENT="${BASH_REMATCH[1]}"
fi

if [ -z "$AGENT" ]; then
    exit 0
fi

# Count tools in the agent
TOOL_COUNT=$(find "$AGENT/src/mcp/tools" -name "*.ts" -type f 2>/dev/null | wc -l | tr -d ' ')

# Check if count is mentioned in docs
README_COUNT=$(grep -E "tools?.*\($TOOL_COUNT" "$AGENT/README.md" 2>/dev/null | wc -l)
CLAUDE_COUNT=$(grep -E "tools?.*\($TOOL_COUNT" "$AGENT/CLAUDE.md" 2>/dev/null | wc -l)

if [[ $README_COUNT -eq 0 ]] || [[ $CLAUDE_COUNT -eq 0 ]]; then
    cat <<EOF
Tool count may be outdated in $AGENT documentation.

Current tool count: $TOOL_COUNT
Files to check:
- $AGENT/README.md
- $AGENT/CLAUDE.md

Update with: /maintain-docs --scope $AGENT
EOF
fi

exit 0
