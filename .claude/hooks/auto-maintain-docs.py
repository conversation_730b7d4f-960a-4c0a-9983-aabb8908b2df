#!/usr/bin/env python3
"""
Auto-maintenance hook for Supabolt documentation.
Triggers documentation updates when source files change.
"""

import json
import sys
import re
import os
from pathlib import Path


def get_affected_agent(file_path):
    """Determine which agent was affected by the file change."""
    path_parts = Path(file_path).parts
    
    # Check if it's an agent file
    for part in path_parts:
        if part.endswith('-agent'):
            return part
        if part == 'compliance-orchestrator':
            return None  # legacy project, skip automation hooks
    
    # Check if it's mcp-core
    if 'packages' in path_parts and 'mcp-core' in path_parts:
        return 'mcp-core'
    
    return None


def should_update_docs(tool_name, file_path):
    """Determine if documentation needs updating based on the change."""
    
    # Skip test files, node_modules, build artifacts
    skip_patterns = [
        r'\.spec\.ts$',
        r'\.test\.ts$',
        r'node_modules',
        r'dist/',
        r'build/',
        r'coverage/',
        r'\.git/'
    ]
    
    for pattern in skip_patterns:
        if re.search(pattern, file_path):
            return False, None
    
    # Documentation updates needed for these changes
    update_triggers = {
        # Tool changes require README and CLAUDE.md updates
        r'src/mcp/tools/.*\.ts$': ['tool-change', 'Update tool documentation'],
        
        # Context changes affect CLAUDE.md patterns
        r'src/mcp/context\.ts$': ['context-change', 'Update context patterns'],
        
        # Package.json changes affect installation docs
        r'package\.json$': ['package-change', 'Update installation instructions'],
        
        # Service changes might affect architecture docs
        r'src/application/services/.*\.ts$': ['service-change', 'Check architecture alignment'],
        
        # Infrastructure changes affect integration docs
        r'src/infrastructure/.*\.ts$': ['infra-change', 'Update integration documentation'],
        
        # Direct doc edits need cross-validation
        r'CLAUDE\.md$': ['claude-doc-change', 'Validate CLAUDE.md accuracy'],
        r'README\.md$': ['readme-change', 'Cross-reference with CLAUDE.md'],
        r'ARCHITECTURE\.md$': ['arch-change', 'Verify architecture accuracy']
    }
    
    for pattern, (change_type, reason) in update_triggers.items():
        if re.search(pattern, file_path):
            return True, (change_type, reason)
    
    return False, None


def main():
    try:
        # Read hook input
        input_data = json.load(sys.stdin)
        tool_name = input_data.get('tool_name', '')
        tool_input = input_data.get('tool_input', {})
        
        # Only process file modification tools
        if tool_name not in ['Write', 'Edit', 'MultiEdit']:
            sys.exit(0)
        
        # Get the file path
        file_path = tool_input.get('file_path', '')
        if not file_path:
            sys.exit(0)
        
        # Check if docs need updating
        needs_update, update_info = should_update_docs(tool_name, file_path)
        if not needs_update:
            sys.exit(0)
        
        change_type, reason = update_info
        agent = get_affected_agent(file_path)
        
        # Generate appropriate Claude command based on change type
        if change_type == 'tool-change' and agent:
            message = f"""
Documentation may need updating after tool modification in {agent}.

Suggested action:
1. Count tools in {agent}/src/mcp/tools/
2. Update tool list in {agent}/README.md
3. Update tool documentation in {agent}/CLAUDE.md
4. Verify tool patterns are documented correctly

Run: /maintain-docs --scope {agent}
"""
        
        elif change_type == 'package-change' and agent:
            message = f"""
Package.json changed in {agent} - documentation may be outdated.

Check:
1. Installation commands in README.md
2. Dependency versions in CLAUDE.md
3. Build/test commands accuracy

Run: /maintain-docs --scope {agent}
"""
        
        elif change_type in ['claude-doc-change', 'readme-change']:
            message = f"""
Documentation file edited directly - cross-validation recommended.

Verify:
1. No contradictions between README.md and CLAUDE.md
2. Commands still work as documented
3. Architecture descriptions match code

Run: /maintain-claude-docs
"""
        
        else:
            message = f"{reason} after changes to {file_path}"
        
        # Output as JSON for structured handling
        output = {
            "decision": "approve",  # Don't block the change
            "suppressOutput": False,
            "feedback": message.strip()
        }
        
        print(json.dumps(output))
        
    except Exception as e:
        # Log error but don't block operations
        print(f"Hook error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
