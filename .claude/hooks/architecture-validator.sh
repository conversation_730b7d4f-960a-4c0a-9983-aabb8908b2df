#!/bin/bash
# Architecture validation hook for Supabolt
# Ensures consistent structure and patterns across agents

# Read input from stdin
INPUT=$(cat)
TOOL_NAME=$(echo "$INPUT" | jq -r '.tool_name')
FILE_PATH=$(echo "$INPUT" | jq -r '.tool_input.file_path')

# Only validate TypeScript source files
if [[ ! "$FILE_PATH" =~ \.ts$ ]] || [[ "$FILE_PATH" =~ (node_modules|dist|build|\.spec\.|\.test\.) ]]; then
    exit 0
fi

# Extract agent name from path
AGENT=""
if [[ "$FILE_PATH" =~ ([^/]+-agent)/ ]]; then
    AGENT="${BASH_REMATCH[1]}"
elif [[ "$FILE_PATH" =~ compliance-orchestrator/ ]]; then
    # Legacy TypeScript orchestrator; skip architecture enforcement
    exit 0
elif [[ "$FILE_PATH" =~ packages/mcp-core/ ]]; then
    AGENT="mcp-core"
fi

if [ -z "$AGENT" ]; then
    exit 0
fi

# Check for architecture violations
ISSUES=""

# 1. Check for direct MCP SDK imports (should use mcp-core)
if [[ "$AGENT" != "mcp-core" ]] && grep -q "@modelcontextprotocol/sdk" "$FILE_PATH" 2>/dev/null; then
    ISSUES="${ISSUES}• Direct MCP SDK import detected - use @supabolt/mcp-core instead\n"
fi

# 2. Check for mixed responsibilities
case "$AGENT" in
    "bitcoin-agent")
        if grep -qE "(sanction|risk|compliance|score)" "$FILE_PATH" 2>/dev/null; then
            ISSUES="${ISSUES}• Bitcoin agent should only provide data, not analysis\n"
        fi
        ;;
    "lightning-agent")
        if grep -qE "(sanction|risk|compliance)" "$FILE_PATH" 2>/dev/null; then
            ISSUES="${ISSUES}• Lightning agent should focus on Lightning Network only\n"
        fi
        # Note: "blockchain" is acceptable in Lightning context (nodes sync with blockchain)
        ;;
    "sanctions-agent")
        if grep -qE "(bitcoin|lightning|blockchain|transaction)" "$FILE_PATH" 2>/dev/null; then
            # Check if it's just in types/examples
            if ! grep -qE "(entityType.*bitcoin|example.*bitcoin)" "$FILE_PATH" 2>/dev/null; then
                ISSUES="${ISSUES}• Sanctions agent should be blockchain-agnostic\n"
            fi
        fi
        ;;
    "usdc-agent")
        if grep -qE "(sanction|risk|compliance|score)" "$FILE_PATH" 2>/dev/null; then
            ISSUES="${ISSUES}• USDC agent should only provide data, not analysis\n"
        fi
        ;;
    "analytics-agent")
        if grep -qE "(bitcoin-core|lnd|blockchain-rpc|direct.*api)" "$FILE_PATH" 2>/dev/null; then
            ISSUES="${ISSUES}• Analytics agent should only score data, not fetch it directly\n"
        fi
        ;;
esac

# 3. Check for proper file location
if [[ "$FILE_PATH" =~ src/domain/ ]] && [[ "$FILE_PATH" =~ (Client|Service|Tool)\.ts$ ]]; then
    ISSUES="${ISSUES}• Domain layer should not contain infrastructure concerns\n"
fi

# Output issues if found
if [ -n "$ISSUES" ]; then
    echo -e "Architecture violations in $FILE_PATH:\n\n$ISSUES" >&2
    echo "Maintain clean architecture and Unix philosophy - each agent does ONE thing." >&2
    exit 2
fi

exit 0
