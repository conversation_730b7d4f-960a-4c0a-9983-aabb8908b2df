# Analytics Agent Prime

Load and analyze the analytics-agent codebase for deep understanding of <PERSON><PERSON><PERSON>'s intelligence layer.

## Usage

```shell
/prime/analytics
```

## Overview

This command provides comprehensive analysis of the Analytics Agent, <PERSON><PERSON><PERSON>'s proprietary intelligence engine that:

1. Detects regulatory patterns (FATF, MiCA, FinCEN)
2. Generates factual compliance narratives
3. Provides cross-domain intelligence synthesis
4. Maintains FACTS ONLY principle (no risk scores)

## Analysis Scope

### 1. Architecture Analysis

- **Intelligence Layer**: Proprietary pattern detection algorithms
- **Service Architecture**: Clean separation of concerns
- **MCP Integration**: Tool implementation and context management
- **Domain Modeling**: Compliance types and patterns

### 2. Pattern Detection

- **Regulatory Patterns**: STRUCTURING, RAPID_MOVEMENT, ROUND_AMOUNTS
- **Statistical Analysis**: Transaction velocity, volume metrics
- **Evidence Collection**: Factual observations with source attribution

### 3. MCP Tools (4 total)

- `analyzeComplianceData`: Cross-domain intelligence synthesis
- `detectPatterns`: Regulatory pattern matching
- `generateNarrative`: Fact-based report generation
- `getAgentStatus`: Health and capability reporting

### 4. Commercial Features

- Subscription management (`SUPABOLT_INTELLIGENCE_KEY`)
- Proprietary algorithm protection
- Development mode bypass

### 5. Integration Points

- Receives data from all other agents via orchestrator
- Returns ONLY factual observations
- Never makes compliance decisions
- Never generates risk scores

## Key Files Loaded

```
analytics-agent/
├── src/
│   ├── intelligence/         # Proprietary algorithms
│   │   ├── pattern-detector.ts
│   │   └── narrative-builder.ts
│   ├── application/services/ # Service layer
│   │   └── AnalyticsService.ts
│   ├── mcp/                  # MCP implementation
│   │   ├── context.ts
│   │   └── tools/
│   │       ├── analyze-compliance-data.ts
│   │       ├── detect-patterns.ts
│   │       ├── generate-narrative.ts
│   │       └── get-agent-status.ts
│   └── domain/               # Types and models
│       └── types.ts
├── README.md
├── ARCHITECTURE.md
└── package.json
```

## Output Format

The command provides:

1. **Purpose & Capabilities**: Role in the compliance ecosystem
2. **Architecture Overview**: Design patterns and structure
3. **MCP Tools Analysis**: Each tool's purpose and implementation
4. **Pattern Detection Logic**: Regulatory patterns implemented
5. **Integration Strategy**: How it connects with other agents
6. **Commercial Licensing**: Subscription and IP protection
7. **Current State**: MVP completeness and production readiness

## Example Insights

```typescript
// FACTS ONLY Implementation
{
  "observations": [
    "Transaction velocity: 45 per hour",
    "Pattern detected: FATF_STRUCTURING_INDICATOR"
  ],
  "regulatoryPatterns": [{
    "pattern": "HIGH_VELOCITY",
    "regulation": "FATF Recommendation 16",
    "observed": 150
  }]
}
// NEVER: riskScore, riskLevel, suspicious flags
```

## Related Commands

- `/prime` - Analyze entire Supabolt system
- `/prime/supervisor` - Analyze supabolt-supervisor orchestrator
- `/prime/bitcoin` - Analyze Bitcoin agent
- `/prime/sanctions` - Analyze Sanctions agent
