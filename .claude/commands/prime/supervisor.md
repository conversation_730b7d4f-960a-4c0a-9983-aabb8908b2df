# `/prime/supervisor`

Focus Area: Python-based supabolt-supervisor (LangGraph 1.x orchestrator)

1. Load supervisor architecture docs
   - `supabolt-supervisor/README.md`
   - `supabolt-supervisor/docs/adr/0001-high-level-architecture.md`
   - `supabolt-supervisor/docs/adr/0002-redis-stack.md`

2. Inspect orchestration pipeline
   - `supabolt-supervisor/mcp/process_client.py`
   - `supabolt-supervisor/workflow.py`
   - `supabolt-supervisor/mcp_nodes.py`
   - `supabolt-supervisor/routing.py`
   - `supabolt-supervisor/fact_sheet.py`

3. Review API surface and settings
   - `supabolt-supervisor/api/server.py`
   - `supabolt-supervisor/settings.py`
   - `supabolt-supervisor/redis_manager.py`

4. Validate manifests & tests
   - `supabolt-supervisor/manifests/toolsets.yaml`
   - `supabolt-supervisor/manifests/mcp/servers.yaml`
   - `supabolt-supervisor/tests/test_golden_dataset.py`

5. Summarize
   - Current routing & HITL flow
   - MCP tool invocation + validation chain
   - Redis / Prometheus integration
   - Test coverage status and remaining TODOs
