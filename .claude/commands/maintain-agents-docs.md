---
allowed-tools: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Edit, TodoWrite
description: Maintain and validate all agent documentation for completeness and accuracy
---

# Maintain Agent Documentation

Ensures all agents are properly documented across CLAUDE.md, README.md, AGENTS.md, and AGENT_METADATA.json.

## Purpose

Validates that:
1. All agent directories are documented in root CLAUDE.md
2. Tool counts are accurate across all documentation
3. README.md, AGENTS.md, and CLAUDE.md are consistent per agent
4. AGENT_METADATA.json matches actual implementation
5. No agents are missing from the inventory

## Quick Validation

Check for missing agents manually:

```bash
# List actual agent directories
ls -d *-agent 2>/dev/null | sort

# List agents in root CLAUDE.md
grep -E "^\d+\. \*\*.*-agent\*\*" CLAUDE.md

# List agents in AGENT_METADATA.json
jq -r 'keys[]' AGENT_METADATA.json | sort
```

Compare the three lists - they should match (except synthesis-agent may be design-only).

## Phase 1: Agent Inventory

### Step 1: Discover All Agents

Find all agent directories:

```bash
ls -d *-agent 2>/dev/null | sort
```

Expected agents:
- analytics-agent
- bitcoin-agent
- bitcoin-aws-agent
- blockchain-agent
- internal-ai-agent
- lightning-agent
- ofac-agent
- sanctions-agent
- spark-agent
- synthesis-agent (design-only)
- usdc-agent
- supabolt-supervisor (Python orchestrator, tracked separately)

### Step 2: Count Actual Tools

For each agent, count actual tool implementations:

```bash
find analytics-agent/src -type f -name "*.ts" -path "*/tools/*" ! -name "*.test.ts" ! -name "index.ts" 2>/dev/null | wc -l
find bitcoin-agent/src -type f -name "*.ts" -path "*/tools/*" ! -name "*.test.ts" ! -name "index.ts" 2>/dev/null | wc -l
find lightning-agent/src -type f -name "*.ts" -path "*/tools/*" ! -name "*.test.ts" ! -name "index.ts" 2>/dev/null | wc -l
# ... repeat for all agents
```

## Phase 2: Documentation Consistency Check

### Check Root CLAUDE.md

Verify all agents are listed in root CLAUDE.md:

```bash
grep -E "^\d+\. \*\*.*-agent\*\*" CLAUDE.md
```

**Required format:**
```
X. **agent-name**: Description - N tools
   - Feature 1
   - Feature 2
   - NO subjective assessments
```

### Check AGENT_METADATA.json

Verify all agents are in metadata file:

```bash
jq -r 'keys[]' AGENT_METADATA.json | sort
```

For each agent, verify:
- `toolCount` matches actual implementation
- `tools` array lists all actual tools
- `status` is accurate (active, internal, design-only)

### Check Individual Agent Documentation

For each agent, verify all four documentation files exist and are consistent:

**1. agent-name/CLAUDE.md**
- Lists exact tool count
- Shows all tool names
- Includes code examples using @supabolt/mcp-core
- No `any` types in examples

**2. agent-name/README.md**
- States tool count in first paragraph
- Lists all tools in table or list
- User-facing language

**3. agent-name/AGENTS.md**
- Quick reference format
- Tool count stated clearly
- Concise tool descriptions

**4. AGENT_METADATA.json entry**
- Exact tool count
- Complete tools array
- Accurate status

## Phase 3: Fix Discrepancies

### Update Root CLAUDE.md

If agents are missing from root CLAUDE.md, add them in this order:

1. bitcoin-agent
2. lightning-agent
3. sanctions-agent
4. usdc-agent
5. analytics-agent
6. blockchain-agent
7. bitcoin-aws-agent
8. ofac-agent
9. spark-agent
10. internal-ai-agent
11. synthesis-agent (design-only)

Document the Python `supabolt-supervisor` separately in architecture sections rather than the agent list.

### Update AGENT_METADATA.json

Fix tool counts and tool arrays to match actual implementation.

For missing tools, find them with:
```bash
ls -1 agent-name/src/mcp/tools/*.ts | grep -v test | grep -v index
```

### Update Individual Agent Docs

**Pattern for README.md:**
```markdown
# Agent Name

Description. Provides N MCP tools for purpose.

## Available Tools (N)

1. **toolName** - Description
2. **toolName2** - Description
```

**Pattern for AGENTS.md:**
```markdown
# Agent Name Guide

## Purpose
Brief purpose statement with N MCP tools.

## Tools
- **Category**: tool1, tool2, tool3
- **Category2**: tool4, tool5
```

## Phase 4: Validation

### Manual Verification

**1. Tool Count Accuracy**
```bash
echo "analytics-agent: Documented=$(grep -i 'provides.*tool\|MCP tool' analytics-agent/README.md | head -1), Actual=$(find analytics-agent/src/mcp/tools -type f -name "*.ts" ! -name "*.test.ts" ! -name "index.ts" | wc -l | tr -d ' ')"
# Repeat for each agent
```

**2. Root CLAUDE.md Completeness**
```bash
# Count agents in directories
ACTUAL=$(ls -d *-agent 2>/dev/null | wc -l | tr -d ' ')
# Count agents in CLAUDE.md
DOCUMENTED=$(grep -E "^\d+\. \*\*.*-agent\*\*" CLAUDE.md | wc -l | tr -d ' ')
echo "Actual: $ACTUAL, Documented: $DOCUMENTED"
```

**3. AGENT_METADATA.json Completeness**
```bash
# All agents should be in metadata (except synthesis-agent is optional)
jq -r 'keys[] | select(endswith("-agent"))' AGENT_METADATA.json | wc -l
```

## Common Issues and Fixes

### Issue 1: Missing Agent in Root CLAUDE.md

**Detection:** Validation script fails with missing agent

**Fix:** Add agent to root CLAUDE.md with proper format:
```markdown
N. **agent-name**: Brief description - X tools

   - Key feature 1
   - Key feature 2
   - NO subjective assessments
```

### Issue 2: Tool Count Mismatch

**Detection:**
```bash
find agent/src/mcp/tools -name "*.ts" ! -name "*.test.ts" ! -name "index.ts" | wc -l
# Returns different number than documented
```

**Fix:** Update all documentation files with correct count:
- Root CLAUDE.md
- agent/README.md (first paragraph)
- agent/CLAUDE.md (Available Tools section)
- AGENT_METADATA.json (toolCount field)

### Issue 3: Missing Tools in AGENT_METADATA.json

**Detection:** `tools` array is shorter than `toolCount`

**Fix:**
```bash
# List actual tools
ls -1 agent/src/mcp/tools/*.ts | grep -v test | grep -v index | xargs -n1 basename -s .ts

# Update AGENT_METADATA.json tools array with camelCase tool names
```

### Issue 4: Inconsistent Documentation

**Detection:** README.md says X tools but CLAUDE.md says Y tools

**Fix:** Always use actual implementation as source of truth:
```bash
# Count actual
find agent/src -path "*/tools/*.ts" ! -name "*.test.ts" ! -name "index.ts" | wc -l

# Update all docs to match
```

## Documentation Standards

### Tool Count Format

**Root CLAUDE.md:**
```markdown
- **agent-name**: Description - 7 tools
```

**Agent README.md:**
```markdown
Provides 7 MCP tools for purpose.
```

**Agent CLAUDE.md:**
```markdown
## Available Tools (7)
```

**AGENT_METADATA.json:**
```json
"toolCount": 7,
"tools": ["tool1", "tool2", "tool3", "tool4", "tool5", "tool6", "tool7"]
```

### Consistency Rules

1. **Always count actual implementation:** Exclude test files and index files
2. **Update all locations:** Root CLAUDE.md, agent docs, and metadata
3. **List all tools:** AGENT_METADATA.json must include complete tools array
4. **Mark design-only agents:** Use status "design-only" for future agents

## Success Criteria

- [ ] All agent directories documented in root CLAUDE.md
- [ ] All agents in AGENT_METADATA.json (except design-only optional)
- [ ] Tool counts match actual implementation across all docs
- [ ] Each agent has consistent CLAUDE.md, README.md, AGENTS.md
- [ ] No discrepancies between documentation sources

## Notes

- **Design-only agents:** synthesis-agent is tracked but has no implementation yet
- **Internal agents:** internal-ai-agent is for internal use, not customer-facing
- **Supervisor:** supabolt-supervisor is a Python application, not an MCP server; document it in architecture sections instead of agent tables
- **Monorepo:** Some agents have separate git repos but docs are in monorepo root

---

Remember: Accurate agent documentation is critical for AI assistant understanding and developer onboarding.
