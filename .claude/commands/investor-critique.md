---
allowed-tools: Read, Glob
description: Honest crypto/AI investor critique - why <PERSON><PERSON><PERSON> may not get funded and how to fix it
---

## Technical Context (Know What You're Evaluating)

**Supabolt** = Unix philosophy for compliance workflows

- **Architecture**: LangGraph orchestrates specialized MCP agents that each do one thing well
- **Business Model**: Platform for workflow automation, not just another data provider
- **Target**: VASPs/exchanges drowning in manual compliance processes
- **Moat**: Composable architecture creates network effects

## Due Diligence Deep Dive

### 1. Business & Market Reality

- Strategic position: Read the file reports/GTM-STRATEGY.md
- High-level overview: Read the file README.md
- Multi-agent differentiation: Read the file reports/LANGGRAPH_PATTERNS.md
- Technical differentiation: Read the file supabolt-supervisor/workflow.py (LangGraph orchestration)

### 2. Technical Moat Assessment

- How modular is "modular"?: Read the file packages/mcp-core/src/server/base-server.ts
- Agent ecosystem size: Use Glob with pattern \*-agent/package.json (count integrations)
- Orchestration sophistication: Read the file supabolt-supervisor/workflow.py
- Code quality indicator: Read the file packages/mcp-core/src/types/common.ts (core types + validation discipline)

### 3. Execution Capability

- Are they shipping or planning?: Use Glob with pattern \*-agent/src/mcp/tools/\*.ts (count actual tools)
- Shipping cadence: Read reports/TECHNICAL_DEBT_REPORT.md (track debt vs. delivery)

## Your Role (Investor Mindset)

You are an honest crypto/AI investor who has seen 1000+ pitches. You're skeptical of compliance plays and need to see:

1. **10x market opportunity** - not incremental improvements
2. **Defensible moat** - what stops Chainalysis from crushing you
3. **Network effects** - how does this get stronger with scale
4. **AI differentiation** - not just wrapper around APIs
5. **Platform potential** - can this become the compliance OS for crypto?

## Your Task

Deliver a candid investor memo:

### 1. **Why I May Not Invest** (be honest)

- Market size concerns
- Competition from incumbents
- Competition from other crypto/AI startups
- Regulatory risks
- Technical debt
- Team/execution risks

### 2. **What Would Change My Mind**

- Specific pivots or additions
- Metrics that would impress
- Partnerships that matter
- Technical moats to build

### 3. **The One Thing**

What single change would make this a must-invest?

Be specific, quantitative where possible, and Be candid and constructive.
