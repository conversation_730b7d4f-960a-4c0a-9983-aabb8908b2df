---
allowed-tools: Task, Glob, Read, Grep, <PERSON>do<PERSON><PERSON>, <PERSON><PERSON>(find:*), <PERSON><PERSON>(grep:*)
description: Verify architectural consistency and Unix philosophy across all agents
---

## Architecture Consistency Check

Ensures all Supabolt agents follow consistent architectural patterns and maintain Unix philosophy of doing one thing well.

## Core Architectural Principles

1. **Unix Philosophy**: Each agent does ONE thing well
2. **Clean Architecture**: Clear separation of layers
3. **MCP Standardization**: All agents use @supabolt/mcp-core
4. **Dependency Injection**: Context pattern for services
5. **Type Safety**: No `any` types, strict TypeScript

## Critical Failure Early Termination

If ANY of these critical issues are detected, STOP immediately and report:

```typescript
Task({
  description: 'Critical failure detection',
  prompt: `
    Check for these CRITICAL issues first. If found, STOP all other checks:
    
    1. Direct imports from @modelcontextprotocol/sdk (should use @supabolt/mcp-core)
    2. Any use of 'any' type in production code (not tests)
    3. Missing ARCHITECTURE.md files for any agent
    4. Cross-contamination of agent responsibilities:
       - Bitcoin agent mentioning sanctions/compliance
       - Sanctions agent with blockchain-specific logic
       - Any agent doing another agent's job
    5. Missing core directories (src/domain, src/mcp, src/infrastructure)
    
    If any critical issues found:
    - Report them immediately
    - Do not continue with other checks
    - Provide specific fix instructions
    
    This saves time by not running subsequent checks when fundamental issues exist.
  `,
});
```

## Phase 1: Agent Inventory

### Discover All Agents

Find all agents using Glob:

- Pattern: \*-agent/package.json

Check for hidden configuration files:

!`find . -name ".mcp.json" -o -name ".env.example" -o -name ".eslintrc.json" 2>/dev/null | grep -v node_modules`

Verify each agent has proper .env.example and .gitignore. Document the Python `supabolt-supervisor` separately in architecture reviews.

## Phase 2: Structural Consistency

### Required Directory Structure

Each agent MUST have:

```
agent-name/
├── src/
│   ├── domain/          # Domain logic and types
│   │   ├── types/       # Domain type definitions
│   │   └── errors.ts    # Domain-specific errors
│   ├── application/     # Application services
│   │   └── services/    # Business logic services
│   ├── infrastructure/  # External integrations
│   │   ├── clients/     # API clients
│   │   └── utils/       # Utilities (logger, sanitizer)
│   ├── mcp/            # MCP implementation
│   │   ├── context.ts   # Agent context (extends BaseContext)
│   │   └── tools/       # MCP tools (extend BaseTool)
│   └── server.ts       # Main entry point
├── package.json
├── tsconfig.json
├── README.md
├── CLAUDE.md
├── .env.example        # Environment variable template
├── .gitignore          # Git ignore configuration
├── .eslintrc.json      # Linting configuration (optional)
├── .prettierrc.cjs     # Formatting configuration (optional)
└── .mcp.json           # MCP metadata (optional)

# Hidden directories (optional):
├── .claude/            # Agent-specific Claude documentation
└── .git/               # Local git configuration
```

### Verify Structure

```typescript
Task({
  description: 'Verify agent structure',
  prompt: `
    For each agent in [${agents.join(', ')}]:
    1. Check required directories exist
    2. Verify separation of concerns
    3. Ensure no business logic in infrastructure
    4. Confirm MCP layer is separate
    
    Report any deviations from standard structure.
  `,
});
```

## Phase 3: MCP-Core Usage

### Verify Base Class Usage

```typescript
// Check all tools extend BaseTool
Grep({
  pattern: 'extends BaseTool',
  path: '.',
  glob: '**/src/mcp/tools/*.ts',
  output_mode: 'files_with_matches',
});

// Check all contexts extend BaseContext
Grep({
  pattern: 'extends BaseContext',
  path: '.',
  glob: '**/src/mcp/context.ts',
  output_mode: 'content',
});
```

### Import Verification

```typescript
// Ensure imports come from @supabolt/mcp-core
Grep({
  pattern: "from ['\""]@supabolt/mcp-core",
  path: ".",
  glob: "**/src/**/*.ts",
  output_mode: "count"
});

// BAD: Direct MCP SDK imports
Grep({
  pattern: "from ['\""]@modelcontextprotocol/sdk",
  path: ".",
  glob: "**/src/**/*.ts",
  output_mode: "files_with_matches"
});
```

## Phase 3.5: Parallel Validation for Efficiency

### Parallel Agent Validation

```typescript
Task({
  description: 'Parallel validation of all agents',
  prompt: `
    Execute the following checks in PARALLEL for ALL agents simultaneously
    to reduce overall execution time:
    
    For each agent in [bitcoin-agent, lightning-agent, sanctions-agent, usdc-agent, analytics-agent, blockchain-agent]:
    
    Parallel checks:
    1. Verify required directories exist (domain/, infrastructure/, mcp/)
    2. Check that all tools extend BaseTool
    3. Verify context extends BaseContext
    4. Count tool files (excluding tests)
    5. Check for any usage of 'any' type
    6. Verify imports from @supabolt/mcp-core only
    7. Check tsconfig.json has strict: true
    
    Use Promise.all() conceptually to gather all results at once.
    Report any violations found across all agents.
    
    This parallel approach should be significantly faster than sequential checks.
  `,
});
```

## Phase 4: Unix Philosophy Compliance

### Single Responsibility Check

```typescript
Task({
  description: 'Verify single responsibility',
  prompt: `
    Review each agent's tools and verify:
    
    1. bitcoin-agent: ONLY Bitcoin data retrieval
       - No risk analysis
       - No compliance decisions
       - No sanctions checks
    
    2. lightning-agent: ONLY Lightning Network data
       - No blockchain analysis
       - No compliance logic
       - No risk scoring
    
    3. sanctions-agent: ONLY entity screening
       - No blockchain knowledge
       - No compliance decisions
       - Just matching and scores
    
    4. usdc-agent: ONLY USDC balance data
       - No transaction analysis
       - No compliance logic
       - Just balance retrieval
    
    Report any violations of single responsibility.
  `,
});
```

### No Mixed Concerns

```typescript
// Bitcoin agent shouldn't know about sanctions
Grep({
  pattern: 'sanction|Sanction',
  path: 'bitcoin-agent/src',
  output_mode: 'count',
});

// Sanctions agent shouldn't have blockchain logic
Grep({
  pattern: 'bitcoin|Bitcoin|lightning|Lightning',
  path: 'sanctions-agent/src',
  glob: '**/*.ts',
  output_mode: 'files_with_matches',
});
```

## Phase 5: Type Safety Audit

### No Any Types

```typescript
// Find any usage of 'any' type
Grep({
  pattern: ': any|<any>|as any',
  path: '.',
  glob: '**/src/**/*.ts',
  output_mode: 'content',
  '-n': true,
});

// Exceptions allowed only in:
// - Test files
// - Type assertions for external libraries
```

### Strict TypeScript Config

```typescript
// Verify tsconfig.json settings
Task({
  description: 'Check TypeScript strictness',
  prompt: `
    For each agent's tsconfig.json, verify:
    1. "strict": true
    2. "noImplicitAny": true
    3. "strictNullChecks": true
    4. "noImplicitReturns": true
    5. "noUnusedLocals": true
  `,
});
```

## Phase 6: Service Pattern Consistency

### Context Pattern

All agents must implement:

```typescript
export class AgentContext extends BaseContext {
  constructor(options: {
    logger: Logger;
    sanitizer?: Sanitizer;
    // Agent-specific services
    agentClient: AgentClient;
  }) {
    super({ logger: options.logger, sanitizer: options.sanitizer });
    this.agentClient = options.agentClient;
  }

  getAgentClient(): AgentClient {
    return this.agentClient;
  }
}
```

### Tool Pattern

All tools must follow:

```typescript
export class MyTool extends BaseTool<InputType, ToolResponse, AgentContext> {
  constructor() {
    super({
      name: 'toolName',
      description: 'Clear description',
      schema: InputSchema,
    });
  }

  protected async execute(context: AgentContext, params: InputType): Promise<ToolResponse> {
    // Implementation
  }
}
```

## Phase 7: Dependency Consistency

### Shared Dependencies

All agents should use same versions of:

```typescript
Task({
  description: 'Check dependency versions',
  prompt: `
    Verify all agents use identical versions of:
    1. @modelcontextprotocol/sdk (peer dep)
    2. zod
    3. dotenv
    4. axios (if used)
    5. Development dependencies
    
    Report any version mismatches.
  `,
});
```

### MCP-Core Dependency

```typescript
// All agents must depend on local mcp-core
Grep({
  pattern: '"@supabolt/mcp-core": "file:../packages/mcp-core"',
  path: '.',
  glob: '*-agent/package.json',
  output_mode: 'files_with_matches',
});
```

## Phase 8: Error Handling Patterns

### Consistent Error Types

Each agent should have:

```typescript
// domain/errors.ts
export class AgentError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: unknown
  ) {
    super(message);
    this.name = 'AgentError';
  }
}
```

### Error Sanitization

All errors must be sanitized:

```typescript
// Required pattern
catch (error) {
  const sanitized = context.sanitizeError(error);
  throw sanitized;
}
```

## Architecture Compliance Report

Generate comprehensive report:

```typescript
const architectureChecks = [
  { id: '1', content: 'Verify directory structure consistency', priority: 'high' },
  { id: '2', content: 'Check mcp-core usage patterns', priority: 'high' },
  { id: '3', content: 'Validate Unix philosophy compliance', priority: 'high' },
  { id: '4', content: 'Audit type safety (no any)', priority: 'high' },
  { id: '5', content: 'Verify service patterns', priority: 'medium' },
  { id: '6', content: 'Check dependency consistency', priority: 'medium' },
  { id: '7', content: 'Validate error handling', priority: 'medium' },
];

TodoWrite({ todos: architectureChecks });
```

## Common Violations and Fixes

### Violation 1: Mixed Responsibilities

**Problem**: Agent doing another agent's job
**Fix**: Move logic to orchestrator

### Violation 2: Direct MCP SDK Usage

**Problem**: Importing from @modelcontextprotocol/sdk
**Fix**: Import from @supabolt/mcp-core

### Violation 3: Inconsistent Structure

**Problem**: Missing required directories
**Fix**: Reorganize to match standard

### Violation 4: Type Safety Issues

**Problem**: Using `any` type
**Fix**: Define proper types

## Final Checklist

- [ ] All agents follow standard directory structure
- [ ] All tools extend BaseTool from mcp-core
- [ ] All contexts extend BaseContext
- [ ] No agent violates single responsibility
- [ ] No `any` types in production code
- [ ] Consistent error handling patterns
- [ ] Same dependency versions across agents
- [ ] TypeScript strict mode enabled
- [ ] Clean architecture layers maintained
- [ ] Unix philosophy preserved

Remember: Architectural consistency enables:

- Easier maintenance
- Faster onboarding
- Reliable composition
- Predictable behavior
- Scalable growth
