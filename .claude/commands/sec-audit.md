---
allowed-tools: Task, Glob, Read, <PERSON>re<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Bash, Edit, MultiEdit
description: Security audit for mission-critical compliance infrastructure (temporary replacement)
---

## Security Audit Command

Performs systematic security review of the Supabolt codebase. This is mission-critical infrastructure handling financial compliance - security is non-negotiable.

## Critical Security Requirements

- **No console.log** in production code
- **All errors sanitized** before client exposure
- **No hardcoded secrets** or credentials
- **Input validation** on all external data
- **Authentication** properly implemented
- **Audit trails** for compliance

## Phase 0: Hidden Files and Directories Audit

### Discover All Hidden Configuration

Find all hidden files and directories:

Use Bash to run: find . -name "._" -type f -o -name "._" -type d | grep -v "node_modules" | grep -v ".git/" | sort

Pay special attention to:

- .env files (may contain secrets)
- .claude directories (may have sensitive docs)
- .mcp.json files (MCP configuration)
- .gitignore files (security boundaries)

## Phase 1: Console.log Detection

### Find All console.log Instances

Search for console.log usage across the codebase:

Use Grep to find console logging:

- Pattern: console\\.(log|error|warn|info)
- Path: .
- Glob: src/\*_/_.ts
- Show line numbers

## Phase 2: Error Sanitization Audit

### Check Error Handling Patterns

Find all catch blocks in the codebase:

Use Grep tool:

- Pattern: catch.\*\\{
- Path: .
- Glob: **/src/**/\*.ts
- Show 5 lines after match to verify context.sanitizeError() usage

## Phase 3: Secrets and Hardcoded Values

### Search for Potential Secrets

Common patterns to search:

- api*key.*=.\_[\'"]\\w+['"]
- password._=._[\'"]\\w+['"]
- secret._=._[\'"]\\w+['"]
- private*key.*=.\_[\'"]\\w+['"]
- bearer.\*[\'"]\\w+['"]

### Check Hidden Configuration Files

Find and check all .env files:

Use Bash to run: find . -name ".env\*" -type f | grep -v node_modules

## Phase 4: Dependency Vulnerabilities

### Check for Known Vulnerabilities

Run npm audit for each component individually:

Use Bash to run: cd bitcoin-agent && npm audit --json && cd ..
Use Bash to run: cd lightning-agent && npm audit --json && cd ..
Use Bash to run: cd sanctions-agent && npm audit --json && cd ..
Use Bash to run: cd usdc-agent && npm audit --json && cd ..
Use Bash to run: cd blockchain-agent && npm audit --json && cd ..
Use Bash to run: cd analytics-agent && npm audit --json && cd ..
Use Bash to run: cd packages/mcp-core && npm audit --json && cd ..
Use Bash to run: cd supabolt-supervisor && poetry run pip-audit && cd ..

## Security Checklist

Create comprehensive security checklist using TodoWrite with these items:

- Remove all console.log from production code (high priority)
- Verify error sanitization in all catch blocks (high priority)
- Check for hardcoded secrets (high priority)
- Audit input validation (high priority)
- Review authentication patterns (high priority)
- Check logging for sensitive data (medium priority)
- Run dependency vulnerability scan (medium priority)
- Verify MCP permission model (medium priority)

Remember: This is financial compliance infrastructure. A single security vulnerability could compromise millions in value and destroy trust. Zero tolerance for security issues.
