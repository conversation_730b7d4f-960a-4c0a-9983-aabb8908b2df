---
allowed-tools: Read, Glob
description: Deep architecture review - scalability, security, and strategic positioning
---

## Architecture Context (LangGraph + Model Context Protocol (MCP) Orchestration)

First, understand the system:

- **What is Supabolt?** A Bitcoin-native Compliance OS applying Unix philosophy to digital asset compliance
- **Core insight**: LangGraph orchestrates specialized MCP agents that each do one thing well
- **Key differentiator**: First Lightning Network native compliance solution with composable workflows
- **Architecture philosophy**: Unix-style pipes and filters for compliance automation

### 1. Orchestration Architecture (Start Here)

- LangGraph supervisor design: Read the file supabolt-supervisor/workflow.py
- MCP process orchestration: Read the file supabolt-supervisor/mcp/process_client.py
- API surface and auth: Read the file supabolt-supervisor/api/server.py

### 2. Agent Standardization (@supabolt/mcp-core)

All agents inherit from this shared foundation:

- Base server pattern: Read the file packages/mcp-core/src/server/base-server.ts
- Tool abstraction: Read the file packages/mcp-core/src/tools/base-tool.ts
- Error handling: Read the file packages/mcp-core/src/errors/mcp-error.ts

### 3. Key Agent Capabilities (Sample 2-3, not all)

Pick representative agents to understand Unix-style patterns:

- Lightning compliance: Read the file lightning-agent/src/mcp/tools/audit-lightning-compliance.ts
- Sanctions screening: Read the file sanctions-agent/src/mcp/tools/checkSanctionsStatus.ts
- Bitcoin analysis: Read the file bitcoin-agent/src/mcp/tools/get-transaction.ts

### 4. Critical Integration Points

- How agents find each other: Read the file supabolt-supervisor/manifests/toolsets.yaml
- MCP server spawn configuration: Read the file supabolt-supervisor/manifests/mcp/servers.yaml
- Agent discovery pattern: Use Glob to find all \*-agent/package.json files (just count them)

### 5. Architecture Decisions

Before reviewing, understand these choices:

- **Why LangGraph?** Stateful workflows, built-in persistence, production-ready orchestration
- **Why MCP?** Anthropic standard, tool interoperability, ecosystem play
- **Why separate agents?** Unix philosophy - each does one thing well, compose for complex workflows
- **Why mcp-core?** Standardization across agents, rapid development, consistent security

## Your Task

Provide a strategic architecture review focusing on:

### 1. **Scalability Assessment**

- Can this handle 1000x growth?
- Where are the bottlenecks?
- Database/storage strategy gaps
- Real-time processing limitations

### 2. **Security Architecture**

- Attack surface analysis
- Data isolation between VASPs
- Key management approach
- Audit trail integrity

### 3. **Strategic Positioning**

- How Unix philosophy creates a defensible moat (compose vs monolith)
- Why MCP protocol choice matters for ecosystem play
- First Lightning Network native compliance advantage
- Benefits of standardized mcp-core architecture for rapid agent development
- Platform effects from composable agent ecosystem

### 4. **Technical Debt & Risk**

- Single points of failure in orchestration
- External dependencies (OpenSanctions API, Bitcoin Core RPC)
- Agent version management complexity
- Performance under high-volume workflows
- LangGraph state management at scale

### 5. **Recommendations**

- Security hardening priorities
- Strategic technical investments
- Build vs buy decisions
- Steps for `mcp-core` completeness
- Steps for `LangGraph` implemenation

Focus on what matters for a platform play, not just current implementation.
