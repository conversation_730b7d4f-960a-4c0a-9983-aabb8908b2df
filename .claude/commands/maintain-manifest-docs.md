---
allowed-tools: Read, <PERSON>, Ba<PERSON>, Glob, TodoWrite
description: Keep supabolt-supervisor manifest documentation synchronized with actual YAML manifests
---

# Maintain Manifest-Driven Orchestration Documentation

Ensures `docs/MANIFEST-DRIVEN-ORCHESTRATION.md` and related supervisor docs reflect the live manifests in
`supabolt-supervisor/manifests/toolsets.yaml` and `supabolt-supervisor/manifests/mcp/servers.yaml`.

## Purpose

The manifests define:
- Which MCP agents participate in each toolset/workflow
- The tool invoked per agent and runtime metadata
- Spawn commands, working directories, and timeouts for each server

Documentation must stay in lockstep so auditors and developers understand the production configuration.

## Validation Steps

1. **Inventory Toolsets**
   ```bash
   cat supabolt-supervisor/manifests/toolsets.yaml
   ```
   - Ensure every documented toolset matches the YAML.
   - Verify agent names and tool names are correct (check agent README files).

2. **Review MCP Server Definitions**
   ```bash
   cat supabolt-supervisor/manifests/mcp/servers.yaml
   ```
   - Confirm command, cwd, and timeout values match documentation.
   - Check health URLs and notes are accurate.

3. **Cross-Reference Documentation**
   - Update `supabolt-supervisor/README.md` and `docs/MANIFEST-DRIVEN-ORCHESTRATION.md` with:
     - Current toolset list + agent participation
     - Default tools invoked per agent
     - Timeout/health requirements

4. **Run Supervisor Tests**
   ```bash
   cd supabolt-supervisor
   poetry run pytest tests/test_golden_dataset.py -k manifest
   ```
   - Confirms manifests load and produce expected fan-out plans.

5. **Update History**
   - Record significant manifest changes in `docs/CHANGELOG.md` or ADRs.

## Update Checklist

- [ ] Toolsets YAML matches documented workflows
- [ ] MCP server YAML matches documented spawn commands
- [ ] README/ADR sections updated with latest manifests
- [ ] pytest suite passes
- [ ] Change noted in changelog/ADR if material

Maintain FACTS ONLY: describe actual agents/tools, no speculative workflows.
