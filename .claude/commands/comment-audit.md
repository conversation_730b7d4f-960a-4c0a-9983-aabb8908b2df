---
allowed-tools: Glob, Read, Edit, MultiEdit, Grep, TodoWrite
description: Expert TypeScript comment auditor following Google Style Guide for useful documentation
---

# Expert TypeScript Comment Auditor

Analyzes TypeScript codebases to identify and fix non-useful comments according to Google TypeScript Style Guide. This is critical for mission-critical compliance infrastructure where code clarity directly impacts system reliability.

## Core Principles

1. **Comments must be useful** - No restating variable names or obvious code
2. **Explain WHY, not WHAT** - Code shows what, comments explain why
3. **Google Style Guide adherence** - JSDoc for public APIs, consistent formatting
4. **Domain expertise matters** - Lightning, MCP, and compliance context

## Phase 1: Discovery and Analysis

### Find All TypeScript Files

Discover TypeScript files across the codebase:

Use Glob to find all TypeScript source files:

- Pattern: **/src/**/\*.ts
- Exclude: **/\*.test.ts, **/\*.spec.ts

Focus on:

- Agent implementations: `*-agent/src/**/*.ts`
- Core libraries: `packages/mcp-core/src/**/*.ts`

## Phase 2: Comment Pattern Detection

### Identify Non-Useful Comments

Search for problematic comment patterns:

1. **Redundant type comments**:
   Use Grep to find JSDoc with redundant @param types:

   - Pattern: @param\s*\{[^}]+\}\s*\w+\s*-?\s*[A-Z]\w+
   - Look for comments that just restate the parameter name

2. **Obvious comments**:
   Use Grep to find trivial comments:

   - Pattern: //\s\*(Get|Set|Return|Initialize|Create)\s+\w+
   - Pattern: //\s\*Constructor
   - Pattern: //\s*TODO\s*$

3. **Commented-out code**:
   Use Grep to find unexplained dead code:
   - Pattern: //\s\*(const|let|var|function|class|import|export)
   - Pattern: /\*[\s\S]_?(const|let|var|function|class)[\s\S]_?\*/

## Phase 3: Comment Quality Assessment

### Evaluate Existing Documentation

For each file with comments, assess:

1. **Function/Method Documentation**:

   - Does it explain the purpose beyond the function name?
   - Are preconditions and postconditions documented?
   - Are side effects mentioned?
   - For async functions, is error handling documented?

2. **Class Documentation**:

   - Is the class's responsibility clear?
   - Are invariants documented?
   - Is the lifecycle explained?
   - For MCP classes, is protocol behavior documented?

3. **Interface Documentation**:
   - Is the contract clear?
   - Are implementation expectations documented?
   - For tool interfaces, are MCP requirements specified?

## Phase 4: Domain-Specific Requirements

### Lightning Node Daemon Comments

For Lightning-specific code, ensure comments cover:

- Security implications of macaroon handling
- Channel state transitions
- Network protocol details
- Error recovery strategies

### MCP Protocol Comments

For MCP implementations, verify:

- Tool parameter validation logic explained
- Error sanitization requirements documented
- Protocol version compatibility noted
- Context injection patterns explained

### Compliance Domain Comments

For compliance logic, ensure:

- FACTS ONLY principle adherence explained
- Regulatory pattern matching logic documented
- Data flow between agents clarified
- Audit trail requirements noted

## Phase 5: Comment Rewriting Guidelines

### JSDoc Format for Public APIs

```typescript
/**
 * Validates a Bitcoin address and retrieves associated transaction history.
 *
 * This method performs both syntactic validation (address format) and
 * semantic validation (exists on blockchain). Results are cached for 5 minutes
 * to reduce RPC load during compliance workflows.
 *
 * @param address - Bitcoin address in any supported format (P2PKH, P2SH, Bech32)
 * @param options - Configuration for validation depth and caching behavior
 * @returns Transaction history with compliance-relevant metadata
 * @throws {InvalidAddressError} When address format is invalid
 * @throws {RPCError} When Bitcoin Core is unreachable
 *
 * @example
 * const history = await validateAndGetHistory('******************************************', {
 *   maxDepth: 6,
 *   includeMempool: true
 * });
 */
```

### Implementation Comments

```typescript
// Exponential backoff prevents overwhelming Bitcoin Core during high load.
// Start at 100ms, double up to 3.2s, matching LND's retry behavior.
const delay = Math.min(100 * Math.pow(2, attempt), 3200);

// Pre-allocate result array to avoid resizing during large transaction batches.
// Compliance workflows often process 1000+ transactions.
const results = new Array<Transaction>(estimatedSize);
```

## Phase 6: Adding Missing Documentation

### Identify Undocumented Public APIs

Find public methods/functions without documentation:

- Pattern: ^export\s+(async\s+)?function\s+\w+
- Pattern: ^export\s+class\s+\w+
- Pattern: public\s+(async\s+)?\w+\s\*\(

### Document Based on Context

When adding new documentation:

1. Analyze the function's role in the larger system
2. Consider error paths and edge cases
3. Document integration points with other agents
4. Explain performance characteristics if relevant

## Phase 7: Automated Fixes

### Common Rewrites

1. **Redundant parameter descriptions**:

   - Before: `@param address - The address`
   - After: `@param address - Bitcoin address to validate against current UTXO set`

2. **Obvious comments**:

   - Before: `// Get transaction`
   - After: `// Fetch from mempool first to catch zero-conf transactions in flight`

3. **Missing error documentation**:
   - Add: `@throws {McpError} When tool execution exceeds 30s timeout`

## Phase 8: Quality Checklist

Create a TodoWrite list for comment improvements:

- Remove redundant type information from JSDoc
- Add missing @throws documentation
- Document async error handling patterns
- Explain security implications in auth code
- Document performance characteristics
- Add examples for complex APIs
- Explain domain-specific decisions

## Example Analysis

### Bad Comment Example

```typescript
// Get balance
function getBalance(address: string): number {
  // Return balance
  return this.balances[address] || 0;
}
```

### Good Comment Example

```typescript
/**
 * Retrieves the current balance for a Bitcoin address.
 *
 * Returns cached balance from last block to ensure consistency during
 * compliance workflows. Cache invalidates on new block arrival via ZMQ.
 * Zero is returned for unknown addresses to match Bitcoin Core behavior.
 *
 * @param address - Bitcoin address in any supported format
 * @returns Current balance in satoshis, or 0 if address not found
 */
function getBalance(address: string): number {
  // Default to 0 matches Bitcoin Core's behavior for unknown addresses,
  // preventing special-case handling in compliance workflows
  return this.balances[address] || 0;
}
```

## Reporting Format

Generate a comprehensive comment audit report:

1. **Statistics**:

   - Total files analyzed
   - Comments removed/rewritten/added
   - Compliance with style guide

2. **Critical Issues**:

   - Misleading documentation
   - Missing error documentation
   - Undocumented public APIs

3. **Improvements Made**:

   - Category breakdown
   - Before/after examples
   - Reasoning for changes

4. **Remaining Work**:
   - Complex areas needing manual review
   - Architectural documentation gaps
   - Domain-specific clarifications needed

Remember: In mission-critical compliance infrastructure, clear documentation can prevent costly errors. Every comment should earn its place by adding genuine value beyond what the code already expresses.
