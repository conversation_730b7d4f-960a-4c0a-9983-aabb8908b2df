---
allowed-tools: Task, Glob, Read, Edit, MultiEdit, Write, TodoWrite, Grep, Bash
description: Maintain and update all CLAUDE.md files for accuracy and consistency
---

## CLAUDE.md Maintenance Command

Ensures all CLAUDE.md files remain accurate, consistent, and useful for Claude Code development. This is critical infrastructure documentation.

## Phase 1: Discovery & Fix Critical Issues

### Find All CLAUDE.md Files

Discover all CLAUDE.md files in the project:

!`find . -name "CLAUDE.md" -type f 2>/dev/null | grep -v node_modules | sort`

Expected locations:

- `/CLAUDE.md` - Root project documentation
- `/*-agent/CLAUDE.md` - Each agent's specific documentation
- `/packages/mcp-core/CLAUDE.md` - Shared library documentation

### CRITICAL: Architecture Compliance Fixes

#### Issue 1: Update Tool Counts in ALL CLAUDE.md Files

Count actual tools for EVERY agent and UPDATE the documentation:
!`for agent in analytics-agent bitcoin-agent blockchain-agent lightning-agent sanctions-agent usdc-agent; do echo -n "$agent: "; find $agent/src/mcp/tools -name "*.ts" -not -name "*.test.ts" 2>/dev/null | wc -l; done`

Update EVERY CLAUDE.md to show EXACT tool counts. NO APPROXIMATIONS.

#### Issue 2: Remove ALL Mixed Responsibilities

Search for and REMOVE any cross-contamination:

- Bitcoin agent MUST NOT mention: risk, compliance, sanctions, analysis
- Sanctions agent MUST NOT mention: bitcoin, lightning, blockchain
- Lightning agent MUST NOT mention: compliance logic, risk scoring
- USDC agent MUST NOT mention: compliance decisions, risk analysis

#### Issue 3: Fix ALL Type Safety Examples

Replace EVERY instance of `any` type in code examples with proper types.
Find them with: !`grep -n ": any" */CLAUDE.md`

## Phase 2: Systematic Review

### Step 1: Create Review Checklist

Use TodoWrite to track review of each file:

```typescript
const claudeFiles = [
  'Root CLAUDE.md',
  'bitcoin-agent/CLAUDE.md',
  'lightning-agent/CLAUDE.md',
  'sanctions-agent/CLAUDE.md',
  'usdc-agent/CLAUDE.md',
  'analytics-agent/CLAUDE.md',
  'packages/mcp-core/CLAUDE.md',
];

const todos = claudeFiles.map((file, index) => ({
  id: String(index + 1),
  content: `Review and update ${file}`,
  status: 'pending',
  priority: 'high',
}));
```

### Step 2: Review Each File

For each CLAUDE.md file, check:

#### A. Accuracy Checks

1. **Commands Match package.json**

   ```typescript
   // Read package.json scripts
   const packageJson = await Read({ file_path: `${agent}/package.json` });
   // Verify all commands in CLAUDE.md exist in scripts
   ```

2. **Tool Lists Are Current**

   ```typescript
   // Count actual tools
   const toolCount = await Glob({ pattern: `${agent}/src/mcp/tools/*.ts` });
   // Verify CLAUDE.md reflects accurate count and names
   ```

3. **Architecture Descriptions**

   - Verify folder structure matches reality
   - Check that patterns described are implemented
   - Ensure no deprecated patterns referenced

4. **Configuration Examples**
   - Environment variables are current
   - Default values are accurate
   - Required vs optional clearly marked

#### B. Consistency Checks

1. **MANDATORY: MCP-Core Import Pattern**
   ALL CLAUDE.md files MUST show:

   ```typescript
   // CORRECT - ONLY THIS PATTERN
   import { BaseTool, BaseContext, ToolResponse } from '@supabolt/mcp-core';

   // WRONG - NEVER SHOW DIRECT IMPORTS
   import { anything } from '@modelcontextprotocol/sdk';
   ```

2. **MANDATORY: Context Pattern**
   EVERY agent CLAUDE.md MUST show this EXACT pattern:

   ```typescript
   export class [Agent]Context extends BaseContext {
     constructor(options: {
       logger: Logger;
       [agent]Client: [Agent]Client;
       sanitizer?: Sanitizer;
     }) {
       super({ logger: options.logger, sanitizer: options.sanitizer });
       this.[agent]Client = options.[agent]Client;
     }
   }
   ```

3. **MANDATORY: Tool Pattern**
   EVERY tool example MUST follow:

   ```typescript
   export class MyTool extends BaseTool<InputType, ToolResponse, [Agent]Context> {
     constructor() {
       super({
         name: 'toolName',
         description: 'Clear description',
         schema: InputSchema,
       });
     }

     protected async execute(context: [Agent]Context, params: InputType): Promise<ToolResponse> {
       // Implementation
     }
   }
   ```

#### C. Completeness Checks

1. **Required Sections**

   - Project Overview
   - Quick Start / Common Commands
   - Architecture
   - Development Guidelines
   - Configuration
   - Important Notes

2. **Security Documentation**
   - Error sanitization mentioned
   - Authentication patterns
   - No console.log warnings
   - Sensitive data handling

## Phase 3: Common Updates

### Update Pattern 1: ENFORCE Single Responsibility

For EVERY agent, REMOVE these forbidden terms:

```typescript
// Bitcoin agent - REMOVE ALL OF THESE
MultiEdit({
  file_path: 'bitcoin-agent/CLAUDE.md',
  edits: [
    { old_string: 'risk analysis', new_string: 'data retrieval', replace_all: true },
    { old_string: 'compliance', new_string: 'blockchain data', replace_all: true },
    { old_string: 'sanctions', new_string: 'transaction data', replace_all: true },
    { old_string: 'scoring', new_string: 'retrieval', replace_all: true },
  ],
});

// Sanctions agent - REMOVE ALL OF THESE
MultiEdit({
  file_path: 'sanctions-agent/CLAUDE.md',
  edits: [
    { old_string: 'bitcoin', new_string: 'entity', replace_all: true },
    { old_string: 'blockchain', new_string: 'screening', replace_all: true },
    { old_string: 'lightning', new_string: 'matching', replace_all: true },
  ],
});
```

### Update Pattern 2: EXACT Tool Documentation

For EVERY agent, update tool counts to EXACT numbers:

````typescript
// Step 1: Get EXACT tool count (excluding test files)
const toolFiles = await Glob({
  pattern: `${agent}/src/mcp/tools/*.ts`,
});
const tools = toolFiles.filter((f) => !f.includes('.test.ts'));

// Step 2: Get tool names
const toolNames = tools.map((path) => {
  const filename = path.split('/').pop().replace('.ts', '');
  return filename.replace(/-/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
});

### Update Pattern 3: Fix Dates and Timelines

```typescript
// Get current date
const today = new Date().toISOString().split('T')[0];

// Update outdated future dates
if (content.includes('Q1 2025') && today > '2025-03-31') {
  // Update to realistic future quarters
}
````

### Update Pattern 4: Add Missing Sections

```typescript
// If missing mcp-core documentation
Write({
  file_path: 'packages/mcp-core/CLAUDE.md',
  content: `# CLAUDE.md - @supabolt/mcp-core
  
## Overview
...comprehensive documentation...
`,
});
```

## Phase 4: Validation

### 1. No Contradictions

```typescript
Task({
  description: 'Check for contradictions',
  prompt: `
    Review all CLAUDE.md files and verify:
    1. No agent claims to do another agent's job
    2. Architecture descriptions match actual code
    3. Examples work with current codebase
    4. No conflicting security guidelines
  `,
});
```

### 2. Practical Usability

For each CLAUDE.md, verify:

- A new developer can understand the component
- Commands can be copy-pasted and work
- Examples are realistic and helpful
- Security warnings are clear

### 3. Completeness

Ensure coverage of:

- All major functionality
- Security requirements
- Testing guidelines
- Common pitfalls
- Integration patterns

## Common Issues and Fixes

### Issue 1: Stale NPM Scripts

**Detection**:

```typescript
Grep({ pattern: 'npm run', path: '*/CLAUDE.md' });
// Compare with actual package.json scripts
```

**Fix**: Update to match package.json exactly

### Issue 2: Incorrect Tool Counts

**Detection**:

```bash
!`for agent in *-agent; do echo -n "$agent: "; find "$agent/src/mcp/tools" -name "*.ts" 2>/dev/null | wc -l; done`
```

**Fix**: Update counts in documentation

### Issue 3: Mixed Responsibilities

**Detection**:

```typescript
Grep({
  pattern: 'risk|analysis|scoring|narrative',
  path: 'bitcoin-agent/CLAUDE.md',
  output_mode: 'count',
});
```

**Fix**: Clarify that analysis happens in orchestrator

### Issue 4: Outdated Architecture

**Detection**: Compare described vs actual folder structure

**Fix**: Update to reflect current architecture

## Best Practices

### 1. Preserve Critical Information

Never remove:

- Security warnings
- Authentication requirements
- Data handling guidelines
- Production considerations

### 2. Keep Examples Practical

- Use real tool names
- Show actual import paths
- Include error handling
- Make copy-pasteable

### 3. Maintain Professional Tone

- No emojis
- No marketing language
- Factual descriptions only
- Clear and concise

### 4. Version-Specific Information

When documenting version-specific features:

```typescript
// Good
'Requires @modelcontextprotocol/sdk version 1.15.0';

// Bad
'Uses latest MCP SDK';
```

## Automated Checks

### Weekly Maintenance Tasks

1. **Tool Count Verification**

   - Count tools in each agent
   - Update documentation

2. **Command Verification**

   - Test all npm commands
   - Update any that changed

3. **Pattern Consistency**

   - Verify BaseTool usage
   - Check error handling
   - Confirm context patterns

4. **Security Audit**
   - Check for console.log
   - Verify sanitization
   - Review auth patterns

## Final Validation

### MANDATORY FIXES (Must Complete)

- [ ] ALL tool counts match EXACT file counts (no test files)
- [ ] NO cross-contamination of agent responsibilities
- [ ] ALL code examples use @supabolt/mcp-core imports ONLY
- [ ] ALL contexts extend BaseContext with EXACT pattern shown
- [ ] ALL tools extend BaseTool with EXACT pattern shown
- [ ] NO `any` types in ANY code examples

### Standard Updates

- [ ] Commands tested and working
- [ ] Architecture descriptions current
- [ ] Security guidelines consistent
- [ ] Examples practical and correct
- [ ] No outdated information
- [ ] Professional tone maintained
- [ ] Unix philosophy preserved

### Verification Commands

# Verify no cross-contamination

grep -l "risk\|compliance\|sanctions" bitcoin-agent/CLAUDE.md && echo "FAIL: Bitcoin agent has forbidden terms"
grep -l "bitcoin\|lightning\|blockchain" sanctions-agent/CLAUDE.md && echo "FAIL: Sanctions agent has forbidden terms"

# Verify correct imports

grep "@modelcontextprotocol/sdk" \*/CLAUDE.md && echo "FAIL: Direct MCP SDK imports found" || echo "PASS: No direct imports"

```plaintext
Remember: CLAUDE.md files are critical infrastructure documentation. They must be accurate, useful, and maintain the high standards required for mission-critical compliance systems.
```
