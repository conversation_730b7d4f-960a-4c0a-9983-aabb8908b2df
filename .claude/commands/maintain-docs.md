---
allowed-tools: Task, Glob, Read, Edit, MultiEdit, Write, TodoWrite, Grep, Bash(*)
description: Maintain README.md and ARCHITECTURE.md files across the project
---

## Documentation Maintenance Command

Ensures all README.md and ARCHITECTURE.md files remain accurate, comprehensive, and aligned with the actual implementation. These are the first files developers read - they must be trustworthy.

## Phase 1: Discovery & Documentation Inventory

### Find All Documentation Files

Find all README.md files (including in hidden directories):
Use Bash: find . -name "README.md" -type f | grep -v node_modules | sort

Find all ARCHITECTURE.md files:
Use Bash: find . -name "ARCHITECTURE.md" -type f | grep -v node_modules | sort

Check for documentation in hidden directories:
Use Bash: find .claude -name "_.md" -type f | sort
Use Bash: find . -path "_/._" -name "_.md" -type f | grep -v node_modules | grep -v ".git/"

### Dynamic Agent Discovery for Documentation Maintenance

```typescript
Task({
  description: 'Discover agents and verify documentation status',
  prompt: `
    Use dynamic discovery instead of hardcoded agent list:
    
    1. Use Glob pattern "*-agent" to find all current agent directories
    2. For each discovered agent:
       - Verify package.json exists and extract version
       - Count actual MCP tools: Glob "{agent}/src/mcp/tools/*.ts" excluding tests
       - Check documentation files: README.md, ARCHITECTURE.md, CLAUDE.md
       - Compare tool count in README vs actual files (detect stale docs)
       - Verify installation commands work (package.json scripts)
    
    3. Generate documentation health report:
       - Agents with outdated tool counts in README
       - Missing documentation files
       - Broken installation commands
       - Version mismatches
    
    This makes documentation maintenance data-driven from actual codebase state.
  `,
});
```

### Documentation Location Check

Expected structure (all in root directories):

```
/ARCHITECTURE.md                          # System ARCHITECTURE
/CLAUDE.md                                # System Claude guidance
/README.md                                # System README
/*-agent/ARCHITECTURE.md                  # Agent ARCHITECTURE
/*-agent/CLAUDE.md                        # Agent CLAUDE guidance
/*-agent/README.md                        # Agent README
/supabolt-supervisor/README.md            # Supervisor README (Python)
/supabolt-supervisor/docs/ADR/*.md        # Supervisor ADRs
/packages/mcp-core/ARCHITECTURE.md        # MCP Core ARCHITECTURE
/packages/mcp-core/CLAUDE.md              # MCP Core CLAUDE
/packages/mcp-core/README.md              # MCP Core README
```

### Create Documentation Inventory

### Create Documentation Task List

Use TodoWrite to track documentation updates:

- Check and update root README.md
- Verify each agent's documentation is current
- Remove any outdated information
- Ensure FACTS ONLY principle is clear

## Phase 2: README.md Validation

### A. Root README.md Checks

The main README must include:

1. **Project Overview**

   - Clear value proposition
   - Unix philosophy explanation
   - Architecture diagram (mermaid)

2. **Quick Start**

```bash
# Verify these commands actually work
git clone https://github.com/supabolt/supabolt.git
cd supabolt
make install
make build
make supervisor-serve
```

````

3. **Agent Inventory**

   ```typescript
   // Verify agent list matches reality
   const agents = await Glob({ pattern: '*-agent/package.json' });
   // Update README with accurate agent list
   ```

4. **Market Positioning**
   - Accurate market size claims
   - No unsubstantiated metrics
   - Realistic roadmap dates

### B. Agent README.md Requirements

Each agent README must have:

1. **Purpose Statement**
   - Single responsibility (Unix philosophy)
   - What it does and doesn't do
   - Integration with other agents

2. **Installation & Setup**

   ```typescript
   // Verify all commands in README
   Task({
     description: 'Test README commands',
     prompt: `
       For each agent README, verify:
       1. npm install works
       2. npm run build succeeds
       3. npm start instructions are accurate
       4. Environment variables documented
     `,
   });
   ```

3. **Tool Documentation**

   ```typescript
   // Count actual tools
   const toolCount = await Glob({
     pattern: `${agent}/src/mcp/tools/*.ts`,
   });

   // Verify README lists all tools with:
   // - Name
   // - Description
   // - Parameters
   // - Example usage
   ```

4. **Configuration**
   - All environment variables
   - Required vs optional
   - Example .env file

## Phase 3: ARCHITECTURE.md Standards

### Required Sections

1. **System Design**
   - Design principles
   - Architectural patterns
   - Technology choices rationale

2. **Component Structure**

   ```
   src/
   ├── domain/          # Explain domain layer
   ├── application/     # Explain service layer
   ├── infrastructure/  # Explain external integrations
   ├── mcp/            # Explain MCP implementation
   └── server.ts       # Entry point explanation
   ```

3. **Data Flow**
   - How data moves through layers
   - Dependency injection patterns
   - Error handling flow

4. **Integration Points**
   - How agent connects with others
   - MCP protocol usage
   - External API integrations

### Architecture Diagram Updates

```typescript
// Ensure diagrams reflect reality
Task({
  description: 'Update architecture diagrams',
  prompt: `
    Review all mermaid diagrams in documentation:
    1. Component relationships accurate
    2. Data flow arrows correct
    3. All agents represented
    4. Integration points shown

    Update any outdated diagrams.
  `,
});
```

## Phase 4: Common Updates

### Update 1: Agent Capabilities

```typescript
// Get current tool list
const tools = await Glob({ pattern: `${agent}/src/mcp/tools/*.ts` });
const toolNames = tools.map((path) => path.split('/').pop().replace('.ts', ''));

// Update README
MultiEdit({
  file_path: `${agent}/README.md`,
  edits: [
    {
      old_string: '## Available Tools',
      new_string: `## Available Tools (${tools.length})\n\n${toolNames.map((t) => `- ${t}`).join('\n')}`,
    },
  ],
});
```

### Update 2: Installation Instructions

```typescript
// Standardize installation across all READMEs
const standardInstall = `
## Installation

\`\`\`bash
# Clone the repository
git clone https://github.com/supabolt/supabolt.git
cd supabolt/${agentName}

# Install dependencies
npm install

# Build the agent
npm run build

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Run the agent
npm start
\`\`\`
`;
```

### Update 3: Architecture Descriptions

```typescript
// Ensure architecture matches code
Task({
  description: 'Verify architecture descriptions',
  prompt: `
    For each ARCHITECTURE.md:
    1. Check folder structure matches code
    2. Verify design patterns are implemented
    3. Update any deprecated patterns
    4. Add missing architectural decisions
  `,
});
```

### Update 4: Remove Marketing Language

```typescript
// Find and fix unsubstantiated claims
Grep({
  pattern: '\\d+%|reduces|faster|better|revolutionary',
  path: '.',
  glob: '**/README.md',
  output_mode: 'files_with_matches',
  '-n': true,
});

// Replace with factual statements
// BAD: "Reduces false positives by 75%"
// GOOD: "Designed to reduce false positives"
```

## Phase 5: Quality Checks

### Accuracy Verification

Use Task to verify documentation accuracy:
- Commands can be copy-pasted and work
- Tool counts match actual files
- No outdated information
- Environment variables are current

### Consistency Check

Ensure all documentation:
- Uses same terminology
- Follows same structure
- Maintains professional tone (no emojis)
- Aligns with Unix philosophy and FACTS ONLY principle

## Final Validation Checklist

- [ ] All README.md files updated with current information
- [ ] All ARCHITECTURE.md files in root directories
- [ ] All Mermaid diagrams are accurate and up to date
- [ ] Tool counts accurate in all agent READMEs
- [ ] No unsubstantiated performance claims
- [ ] FACTS ONLY principle clearly documented
- [ ] Installation instructions tested and working
- [ ] Configuration documented with examples
- [ ] No references to old patterns or structures
- [ ] Professional tone maintained (no emojis)
- [ ] Unix philosophy preserved (single responsibility)
- [ ] All documentation files are linted and formatted correctly

Remember: Documentation must be accurate, complete, reflect the current state of the code, and be surgically precise.
````
