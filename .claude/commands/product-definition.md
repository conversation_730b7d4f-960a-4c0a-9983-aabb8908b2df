---
allowed-tools: <PERSON><PERSON><PERSON>, Read, Bash, Task, TodoWrite
description: Dynamically generate a current, factual product definition based on available agents and capabilities, adhering to Supabolt's "FACTS ONLY" principle.
---

## Generate Supabolt Product Definition

This command dynamically generates a concise, one-page product definition document, drawing solely from the current state of the Supabolt codebase and internal documentation. All presented information must be factual, verifiable, and free of subjective assessments, risk scores, or opinions.

## Phase 1: Comprehensive Data Discovery & Factual Extraction

### 1. Identify Core Product Messaging

Extract the foundational value proposition and mission directly from key strategic documents.

```typescript
Task({
  description: 'Extract core product messaging and mission statement.',
  prompt: `
    Read the following files to identify Supabolt's core value proposition, mission, and strategic positioning:
    - README.md
    - ARCHITECTURE.md
    - reports/STRATEGY.md

    Focus on declarative statements about what the product *is* and *does*, not what it *might be* or *should do*.
  `,
});
```

### 2. Discover & Inventory All Agents and Their Capabilities

Use dynamic code inspection to precisely catalogue current agent functionalities and integrations.

```typescript
Task({
  description: 'Dynamically discover all agents and their factual capabilities',
  prompt: `
    Execute comprehensive agent discovery from live codebase:
    
    1. Use Glob pattern "*-agent" to find all agent directories
    2. Include "supabolt-supervisor" as the supervisor
    3. For each agent found:
       - Read package.json for JS agents (supabolt-supervisor uses pyproject.toml)
       - Count MCP tools: Glob "{agent}/src/mcp/tools/*.ts" excluding *.test.ts
       - For supabolt-supervisor, inspect manifests and pytest coverage
       - Verify documentation: Check README.md, ARCHITECTURE.md, CLAUDE.md existence
       - Note version and dependency status from package.json
    
    4. Generate factual capability matrix with current, verified data:
       - Agent name | Version | Description | Tool count | Integrations | Doc status
    
    This ensures product definition reflects actual implemented capabilities, not planned features.
  `,
});
```

### 3. Analyze Core Compliance Workflows

Use dynamic code inspection to map actual implemented workflows and agent composition patterns.

```typescript
Task({
  description: 'Analyze actual compliance workflows from live orchestrator code',
  prompt: `
    Inspect supabolt-supervisor for current workflow implementations:
    
    1. Review workflow orchestration: "supabolt-supervisor/workflow.py"
    2. Read the MCP transport: "supabolt-supervisor/mcp/process_client.py"
    3. Inspect manifests: "supabolt-supervisor/manifests/toolsets.yaml" and "manifests/mcp/servers.yaml"
    4. Examine fact sheet output: "supabolt-supervisor/fact_sheet.py"
    
    Generate factual analysis:
    - Current workflow count and names (from actual files)
    - Agent composition patterns (how supervisor routes to workers)
    - Input types supported (extracted from interfaces/schemas)
    - Output formats produced (from actual return types)
    - Audit trail mechanisms (from actual logging/state code)
    
    Focus on what is currently implemented, not what documentation claims.
  `,
});
```

### 4. Extract Factual Performance & Business Metrics

Scrutinize relevant documentation for quantifiable, verifiable performance data and concrete business success indicators. Avoid any subjective claims or unbacked assertions.

```typescript
Task({
  description:
    'Extract all factual performance metrics and verifiable business success indicators.',
  prompt: `
    Search the following files for quantifiable performance claims and measurable business metrics:
    - README.md (look for sections on performance, throughput)
    - ARCHITECTURE.md (sections on scalability, specific numbers)
    - PROJECT_UPDATE.md (if exists, look for factual progress reports, achieved milestones)
    - reports/STRATEGY.md (look for factual market positioning, addressable market by verifiable numbers, growth metrics).

    Prioritize numbers, percentages, speeds, and verifiable outcomes. If a metric is a target but not yet achieved, state it as such (e.g., "Target: X, Current: Y").
  `,
});
```

## Phase 2: Dynamic Product Definition Generation

Construct the one-page product definition, populating each section with the extracted factual information.

### What Supabolt Does (Factual, Concise Statement)

[Generate based on README.md tagline or a factual summary from discovered capabilities. e.g., "Supabolt provides auditable, automated compliance solutions for blockchain transactions using a modular agent architecture."]

### The Core Compliance Workflow (Factual Input & Output)

```
Input: [Factual primary input types, e.g., 'Raw Bitcoin transaction data', 'Lightning Network channel updates']
Output: [Factual, auditable outputs, e.g., 'Sanctions screening reports', 'Fact-based compliance assessments', 'Immutable audit trails of agent decisions']
```

### Current Capabilities (Factual & Verified)

[Dynamically list based on discovered agents and their *verified* capabilities]:

- **[Agent Name]**: [Factual description from package.json/CLAUDE.md] ([X] verified tools)
  - **Key Integrations**: [List factual integration points, e.g., "Interacts with LND via gRPC", "Queries Bitcoin Core RPC for transaction details", "Fetches data from OpenSanctions API"].
  - **Factual Capabilities**: [List specific, verifiable functions, e.g., "Performs UTXO set analysis", "Screens addresses against OFAC list", "Monitors Lightning channel liquidity"].
- **Supabolt Supervisor**: Coordinates [Y] agents to execute [Z] distinct compliance workflows and emits FACTS ONLY fact sheets (JSON + Markdown) for downstream evidence systems.

### Why This Matters (Factual Impact & Market Need)

[Extract factual statements from business strategy and README.md that articulate the verifiable impact of Supabolt's capabilities on market problems. Focus on quantifiable benefits, regulatory adherence, and operational efficiency gains, avoiding subjective "why it's great" statements.]

### MVP Scope (Factual, Currently Implemented)

[Based purely on currently implemented and deployed tools, agents, and workflows. State what is functional *today*, not what is planned. Use factual descriptions.]

### Technical Stack (Factual Integrations & Status)

[Extract from discovered integrations and code analysis]:

- **Core Architecture**: LangGraph for multi-agent orchestration, Model Context Protocol (MCP) for agent communication.
- **Lightning Network**: [Factual LND/CLN integration status and capabilities, e.g., "Connected to LND node via gRPC, retrieves channel state and transaction data."].
- **Bitcoin**: [Factual Bitcoin Core RPC integration status, e.g., "Direct RPC calls to Bitcoin Core for UTXO and transaction history analysis."].
- **Sanctions Screening**: [Factual OpenSanctions/other sanctions list integration status, e.g., "API integration with OpenSanctions for real-time entity screening."].
- **Stablecoins**: [Factual USDC/multi-chain support, e.g., "Monitors USDC on Ethereum (ERC-20) and Solana via RPC calls."].
- **Analytics**: [Factual pattern detection capabilities, e.g., "Identifies structuring patterns in transaction graphs", "Detects known illicit addresses from public datasets."].

### Performance Metrics (Strictly Factual & Quantifiable)

[Extract only actual, measured metrics from documentation. If no direct measurement is documented, state "Not yet measured" or "To be defined". Do NOT invent numbers or use subjective terms like "fast" or "efficient" without concrete data.]:

- **Workflow Execution Time**: [e.g., "Average 2.5 seconds per standard KYC workflow (measured on staging)."]
- **Supported Throughput**: [e.g., "Processes 500 transactions per minute on a single orchestrator instance."]
- **Agent Response Times**: [e.g., "Sanctions agent average response: 200ms.", "Bitcoin agent average response: 500ms (for single address history)."]
- **Data Processing Volume**: [e.g., "Indexed 1.5 TB of historical blockchain data."]

### Pricing Model (Factual, If Documented)

[Search for explicitly documented pricing information in internal documentation. If not found, state "Pricing model is not currently documented in accessible files." Avoid speculation.]

### What We DON'T Do (Yet) (Factual Gaps & Roadmapped Items)

[Based on roadmap sections in ARCHITECTURE.md, and `TodoWrite` items. Clearly state factual limitations or features that are explicitly planned but not yet implemented. This section demonstrates awareness of gaps, not weaknesses.]

### Success Metrics (Factual & Measurable)

[Extract from documentation or generate based on current capabilities and strategic goals. These must be quantifiable and auditable measures of success.]:

- **Audit Pass Rate**: [e.g., "Achieve 100% pass rate on internal compliance audits."]
- **Regulatory Compliance Coverage**: [e.g., "Cover 85% of FATF Travel Rule requirements as implemented by agents."]
- **Frictionless Integration**: [e.g., "New agent integration time: under 3 days."]
- **Transaction Volume Processed**: [e.g., "Total USD value of transactions processed: $X million/month."]
- **Incident Resolution Time**: [e.g., "Mean time to resolution for critical compliance incidents: < 2 hours."]

Remember: This document must be dynamically generated each time to reflect the absolute current, factual state of the codebase. It serves as a truth source, not a marketing pitch.
