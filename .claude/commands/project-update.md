---
allowed-tools: Task, Glob, Read, Edit, MultiEdit, Write, TodoWrite, <PERSON><PERSON>(find:*), <PERSON><PERSON>(grep:*), <PERSON>sh(sort:*)
description: Perform systematic project-wide updates with surgical precision, incorporating advanced analysis.
---

## Project-Wide Update Command

This command performs systematic updates across the entire Supabolt codebase with the same precision used for CLAUDE.md maintenance. Mission-critical infrastructure requires surgical accuracy and intelligent adaptation.

## Phase 1: Discovery and Analysis

### 1. Identify Update Scope

First, dynamically discover all files requiring updates based on the nature of the project-wide change. Prioritize patterns that yield relevant files.

```bash
# Examples of comprehensive patterns to discover relevant files:
# - All Markdown documentation: Glob({ pattern: "**/*.md" })
# - All JSON configuration files: Glob({ pattern: "**/*.json" })
# - All TypeScript source files: Glob({ pattern: "**/src/**/*.ts" })
# - All test files: Glob({ pattern: "**/*.test.ts" })
# - Recursively find all .env.example files: Task({ description: 'Find .env.example files', prompt: 'Use Bash: find . -name ".env.example" -type f -not -path "*/node_modules/*"' })
```

### 2. Create Update Inventory

Prioritize and create a comprehensive todo list for all identified changes. Each item should be specific, verifiable, and tied to a clear objective.

```typescript
// Create comprehensive todo list dynamically based on discovered scope and analysis
TodoWrite({
  todos: [
    {
      id: '1',
      content:
        'Update MCP SDK version in all relevant package.json files to latest stable release and verify compatibility.',
      status: 'pending',
      priority: 'high',
    },
    {
      id: '2',
      content:
        'Refactor all instances of deprecated "risk analysis" terminology into factual, auditable compliance patterns, ensuring no functionality loss.',
      status: 'pending',
      priority: 'critical',
    },
    {
      id: '3',
      content:
        'Synchronize agent configuration files (.env.example, .mcp.json) across all agents to reflect current architectural standards.',
      status: 'pending',
      priority: 'high',
    },
    {
      id: '4',
      content:
        'Ensure all CLAUDE.md and ARCHITECTURE.md files are accurate, consistent, and reflect current codebase structure and responsibilities, without mixed agent responsibilities.',
      status: 'pending',
      priority: 'medium',
    },
    {
      id: '5',
      content:
        'Review and update error handling patterns across all mission-critical transaction paths to ensure robust sanitization and consistent logging.',
      status: 'pending',
      priority: 'high',
    },
    // ... dynamically add more comprehensive and specific changes as identified during analysis
  ],
});
```

## Phase 2: Systematic Updates

### Pattern 1: Documentation Consistency

When updating documentation across all agents, prioritize accuracy, clarity, and adherence to the Unix philosophy.

```typescript
Task({
  description:
    'Update and verify documentation consistency across all CLAUDE.md and related files.',
  prompt: `
    1. Systematically discover all documentation files (e.g., CLAUDE.md, README.md, ARCHITECTURE.md) across all agent directories, including hidden ones.
    2. For each file, analyze its current content for:
       - Outdated references or terminology.
       - Architectural descriptions that no longer match the implementation.
       - Inconsistencies with the Unix philosophy (e.g., agents with mixed responsibilities).
       - Inaccurate tool lists or dependency versions.
       - Missing or unclear configuration examples in .env.example files.
    3. Apply precise updates using Edit/MultiEdit to ensure:
       - All facts are current and verifiable.
       - Terminology is consistent project-wide.
       - Agent responsibilities are strictly separated and documented as such.
       - Tool lists accurately reflect allowed-tools.
    4. Proactively identify and suggest any *new* documentation sections or files that might be missing for recently added features or architectural changes.
  `,
});
```

### Pattern 2: Code Reference Updates

When updating code references or deprecating concepts, ensure surgical removal and contextual refactoring to prevent regressions and maintain clarity.

```typescript
Task({
  description:
    'Identify and update/remove all code references related to a deprecated concept (e.g., "risk analysis").',
  prompt: `
    1. Use Grep to perform an exhaustive search for all instances of the deprecated concept (e.g., "risk analysis", "risk scoring", "suspicious transaction score") across all source code, documentation, tests, and comments.
    2. For each identified instance, analyze its context to determine the appropriate action:
       - If it's a direct reference to be removed, use MultiEdit for precise deletion.
       - If it's a conceptual reference to be replaced, refactor it into factual, auditable compliance patterns (e.g., replacing "risk score: 85" with "patterns_detected: ['rapid_movement', 'mixing_interaction']").
       - Ensure that any removal or refactoring aligns with the Unix philosophy of pure data retrieval for core agents.
    3. Verify that the changes do not introduce compilation errors or alter intended (non-deprecated) functionality.
    4. Update related documentation (CLAUDE.md, README.md) to reflect the removal or change in concept, ensuring no lingering misleading information.
  `,
});
```

### Pattern 3: Configuration Synchronization

Maintain strict synchronization of configuration and core dependencies across all Supabolt agents to ensure a unified and secure operational environment.

```typescript
Task({
  description: 'Synchronize configuration files and core dependencies across all agents.',
  prompt: `
    1. Dynamically discover all active agents by globbing for agent directories (e.g., '*-agent', 'packages/mcp-core'). Document the Python supabolt-supervisor separately.
    2. For each agent, locate all relevant configuration files:
       - package.json (for dependency versions, especially @modelcontextprotocol/sdk)
       - .env.example (for environment variable consistency)
       - .mcp.json (for MCP server/client configurations)
       - .eslintrc.json (for consistent linting rules)
    3. Perform precise MultiEdit operations to:
       - Update core dependencies (e.g., "@modelcontextprotocol/sdk" to "1.15.0") ensuring compatibility is verified beforehand.
       - Standardize environment variable naming conventions and default values across .env.example files.
       - Synchronize MCP connection parameters if applicable.
    4. Proactively identify any new configuration parameters or dependencies that have been introduced in some agents but are missing in others, and suggest their consistent application.
  `,
});
```

## Phase 3: Validation

### 1. Cross-Reference Verification

After updates, meticulously verify cross-component consistency and architectural adherence.

```typescript
Task({
  description: 'Verify architectural consistency and cross-reference integrity across all agents.',
  prompt: `
    1. Systematically check that all agents:
       - Consistently extend BaseTool from @supabolt/mcp-core where applicable.
       - Properly implement the BaseContext pattern for standardized data flow.
       - Follow the standardized error handling procedures, using context.sanitizeError().
       - Adhere to the defined CLAUDE.md and ARCHITECTURE.md structure and content guidelines.
       - Do not exhibit mixed responsibilities that violate the Unix philosophy.
    2. Report any deviations as high-priority TodoWrite items for immediate remediation, including the specific file and line number.
  `,
});
```

### 2. Build Verification

Crucially, ensure that all components compile and build successfully after updates. This is a non-negotiable step.

```bash

# Run builds for all components. This command ensures each agent's build process completes without errors.
# Use Bash to run: find . -maxdepth 2 -type d -name "*-agent" -print0 | xargs -0 -I {} bash -c "cd {} && echo 'Building {}...' && npm run build || (echo 'Build failed for {}!' && exit 1)"
# Also build agents and supabolt-supervisor explicitly:
# Use Bash to run: make build && make supervisor-test
# Use Bash to run: cd packages/mcp-core && npm run build && cd ../..
```

## Update Categories

### A. Documentation Updates

**Scope**: CLAUDE.md, README.md, ARCHITECTURE.md files, and other project-level Markdown documents.

**Common Tasks**:

- Precisely remove outdated references and deprecated terminology
- Update architecture descriptions to reflect current state and future roadmap
- Sync all documentation with the actual implementation and code structure
- Correct dates, timelines, and version numbers
- Ensure strict compliance with the Unix philosophy in all agent descriptions

**Validation**:

- Zero contradictions between documentation and live code
- Uniform terminology and consistent writing style
- Accurate and verifiable tool lists and agent responsibilities

### B. Security Updates

**Scope**: Error handling, logging, authentication, secrets management, input validation.

**Common Tasks**:

- Verify all error paths use context.sanitizeError() for sensitive data
- Confirm complete absence of console.log statements in production-facing code
- Conduct rigorous checks for hardcoded secrets or sensitive information
- Update and verify adherence to the latest authentication and authorization patterns

**Validation**:

- Automated Grep for console.log and banned sensitive patterns
- Comprehensive review of error handling logic in critical paths
- Audit of authentication and authorization flows for vulnerabilities

### C. Dependency Updates

**Scope**: package.json files, tsconfig.json, and import statements across the codebase.

**Common Tasks**:

- Update core dependencies like MCP SDK versions to the latest stable release
- Synchronize @supabolt/mcp-core usage across all agents
- Update and manage peer dependencies to ensure compatibility
- Identify and remove any unused or redundant dependencies to reduce attack surface and bundle size

**Validation**:

- All agents consistently use approved versions of core dependencies
- No version conflicts detected during npm install or yarn install
- All components build and pass tests after dependency updates

### D. Pattern Standardization

**Scope**: Agent tool implementations, service patterns, file structures, and coding conventions.

**Common Tasks**:

- Migrate all existing tools to strictly adhere to the BaseTool pattern
- Standardize context object usage (BaseContext) for consistent data flow
- Unify error handling mechanisms and reporting across the entire system
- Enforce consistent file and directory structures for all agents and shared modules

**Validation**:

- Automated checks confirm all tools extend BaseTool
- Uniform application of context and error handling patterns
- Consistent folder structure verified via Glob and Bash commands

### E. Architecture Updates (NEW)

**Scope**: LangGraph integration, MCP client/server architecture, state management patterns.

**Common Tasks**:

- Review supabolt-supervisor manifests and ensure documentation matches current toolsets
- Integrate LangGraph StateGraph for workflow orchestration
- Implement MultiServerMCPClient for agent communication
- Standardize agent MCP server implementations
- Update documentation to reflect client/server architecture

**Validation**:

- Compliance orchestrator successfully connects to all agent MCP servers
- LangGraph workflows execute without state management errors
- All agents properly implement MCP server protocol

## Execution Workflow

### Step 1: Analyze Current State with Deep Dive

```typescript
Task({
  description: "Perform a comprehensive analysis of the project's current state.",
  prompt: `
    1. Systematically list all deployed agents, their documented responsibilities, and actual code state.
    2. Proactively identify inconsistencies, outdated patterns, or potential architectural regressions between agents and against architectural principles.
    3. Conduct an exhaustive search for security anti-patterns (e.g., unsanitized errors, hardcoded secrets, problematic logging).
    4. Verify absolute compliance with the Unix philosophy: ensure each agent performs precisely ONE, well-defined task without mixed responsibilities.
    5. Propose a prioritized list of update objectives based on impact and urgency.
  `,
});
```

### Step 2: Plan Updates with Surgical Precision

Create a granular TodoWrite list that serves as an execution blueprint, ensuring traceability and verifiability.

- Provide a clear, unambiguous description for each change
- Assign a precise priority (critical/high/medium/low) based on impact
- Explicitly list all affected files and components
- Define specific, measurable validation criteria for each completed task

### Step 3: Execute Updates Incrementally and Precisely

Execute updates one by one, adhering to the principle of "surgical precision."

- Mark the TodoWrite item as "in_progress"
- Apply changes using Edit or MultiEdit, making the smallest possible modification
- Immediately validate the specific change locally to ensure correctness and prevent regressions
- Mark the TodoWrite item as "completed" only after successful validation
- Update any related documentation that is directly affected by the change

### Step 4: Final Comprehensive Validation

```typescript
Task({
  description: 'Perform a rigorous, final validation of all updates across the entire system.',
  prompt: `
    1. Verify that every item in the comprehensive TodoWrite list has been marked "completed."
    2. Confirm that no broken references or unresolved dependencies exist anywhere in the codebase.
    3. Systematically re-verify consistent patterns and architectural adherence across all agents and shared modules.
    4. Run all comprehensive test suites to ensure 100% test pass rates.
    5. Conduct a final audit of all CLAUDE.md and ARCHITECTURE.md files to confirm they are absolutely accurate, useful, and reflect the current state.
    6. Critically assess if the updates have introduced any unintended consequences or new forms of technical debt.
  `,
});
```

## Best Practices

### 0. Production Ready MVP

- **MVP ≠ Prototype**: This system handles real money. It must operate with production-grade reliability from day one
- **Production standards from day one**: Implement robust security, comprehensive error handling, and meticulous logging from the very first line of code
- **No shortcuts on critical paths**: Payment processing flows, compliance checks, and cryptographic operations must be absolutely bulletproof
- **Test like it's live**: Assume every test is a live transaction; test edge cases, error conditions, and high-volume scenarios
- **"Move fast and break things" does not apply**: Our mandate is to secure millions in Bitcoin and ensure compliance; stability and correctness are paramount

### 1. KISS - Keep It Simple, Stupid

- **Simplest solution first**: Always seek the simplest, most direct solution that meets the requirements. Avoid over-engineering
- **Remove complexity**: Actively identify and eliminate unnecessary layers of abstraction, features, or code paths
- **Clear over clever**: Prioritize code readability and maintainability over clever, but complex, implementations
- **Do less**: Each change or new feature should solve precisely ONE problem, clearly and efficiently
- **Question additions**: Rigorously challenge every proposed addition: "Does this simplify or complicate? Is it truly essential for the current problem?"

### 2. YAGNI - You Aren't Gonna Need It

- **Delete speculative features**: If a feature isn't explicitly required for current functionality or a confirmed roadmap item, remove it
- **No "might need later"**: Avoid implementing functionality based on speculative future needs. Build only what is needed NOW
- **Remove unused code**: Dead code is a liability. Regularly identify and delete unreachable or unused code
- **Challenge every addition**: Ask: "Who specifically needs this? When exactly will it be used? What problem does it solve right now?"
- **Prefer deletion**: When in doubt about whether code is needed, err on the side of removing it

### 3. DRY - Don't Repeat Yourself

- **Extract common patterns**: If code segments are copied and pasted, it's a strong indicator to abstract them into shared utilities
- **Centralize shared logic**: Utilize @supabolt/mcp-core for all common, reusable functionality across agents
- **Single source of truth**: Ensure that each piece of information or logic has one, and only one, authoritative representation in the system
- **But don't over-abstract**: Recognize when some duplication (e.g., two identical instances) is more pragmatic than a premature or incorrect abstraction
- **Pragmatic DRY**: Apply DRY principles judiciously. Avoid abstractions that introduce more complexity than they solve

### 4. Fail Fast

- **Validate inputs immediately**: Do not proceed with processing if input data is invalid or incomplete
- **Throw clear, explicit errors**: Errors should immediately indicate what went wrong, where, and why, providing actionable information
- **No silent failures**: If a process fails or encounters an issue, it must loudly and visibly report it. Avoid hidden errors
- **Exit early**: Terminate execution pathways as soon as an error or invalid state is detected. Do not continue processing corrupted or problematic data
- **Make problems visible**: Utilize logging and monitoring to ensure that any failure or anomaly is immediately apparent to operators

### 5. No Premature Optimization

- **Make it work first**: Prioritize correct and robust functionality over speculative performance gains
- **Profile before optimizing**: Always use performance profiling tools to identify actual bottlenecks. Optimize based on data, not assumptions
- **Optimize the bottleneck**: Focus optimization efforts exclusively on the identified critical performance bottlenecks
- **Simple code is often fast**: Clear, straightforward code is typically easier to optimize once bottlenecks are identified
- **Document why**: If a complex optimization is implemented, thoroughly document its necessity, the profiling evidence, and the trade-offs involved

### 6. Precision Over Speed

- **Read before editing**: Always fully understand the context of the code before making any changes
- **Make minimal changes**: Strive for the smallest possible change that achieves the desired outcome, reducing the surface area for new bugs
- **Preserve existing functionality**: Ensure that changes do not inadvertently alter or break unrelated, existing functionality
- **Test after each major change**: Execute relevant tests immediately after each significant modification to catch issues early

### 7. Maintain Audit Trail

- **Use descriptive todo items**: Ensure TodoWrite entries clearly articulate the purpose and scope of each change
- **Document why changes were made**: Provide context and reasoning for significant modifications, especially design decisions
- **Keep old/new comparisons clear**: Use version control effectively to track changes and facilitate easy comparison
- **Track validation results**: Log or note the outcomes of all validation steps for accountability

### 8. Security First

- **Never introduce new vulnerabilities**: Every change must be reviewed through a security lens to ensure no new attack vectors are created
- **Maintain error sanitization**: Ensure sensitive information is never exposed in error messages or logs
- **Preserve authentication patterns**: Verify that all authentication and authorization mechanisms remain robust and correctly implemented
- **Keep secrets management intact**: Do not hardcode secrets or bypass secure credential management practices

### 9. Unix Philosophy

- **Each agent does ONE thing**: Strict adherence to the single responsibility principle for every agent
- **No mixed responsibilities**: Agents must not overlap in core functions or perform tasks outside their defined scope
- **Clear separation of concerns**: Distinct boundaries between different parts of the system, minimizing coupling
- **Composable architecture**: Design agents to be easily combined and reused to build more complex workflows

### 10. FACTS ONLY - Critical Project Requirement

- **NO SCORES**: Prohibit any risk scores, numerical ratings, or subjective assessments in outputs
- **NO OPINIONS**: Only factual observations, verifiable data points, and direct regulatory pattern matches are allowed
- **NO EXCEPTIONS**: This applies to ALL agents (except specific test suite outputs for debugging)
- **Validation**: Systematically search for banned terms like: score, risk, suspicious, high, medium, low (in descriptive contexts)

- **Replacement**: Actively convert subjective assessments to objective facts:
  - "risk_score: 85" → "patterns_detected: ['rapid_movement_anomaly', 'mixing_service_interaction']"
  - "risk: high" → "transaction_velocity_exceeds_threshold: 450_transactions_per_hour"
  - "suspicious: true" → "matches_fatf_pattern: structuring_over_threshold"
- **Return Types**: Ensure API return types and data models strictly remove any fields designed for subjective scores or opinions
- **Verification**: Final verification MUST include: `grep -r "score\|risk\|suspicious" --include="*.ts" src/` to confirm all subjective terms have been removed from the production codebase

## Common Update Scenarios

### Scenario 1: Remove Concept from Agent

When removing a concept (e.g., "risk analysis") from a specific agent:

1. Perform an exhaustive Grep for all references to the concept (e.g., in code, documentation, tests, comments, API definitions)
2. Systematically update all related documentation (CLAUDE.md, README.md, ARCHITECTURE.md) to reflect the removal
3. Verify through code review and static analysis that no code actually implements the removed concept
4. Update related agent documentation (if external agents referenced this concept)
5. Ensure the compliance orchestrator or other coordinating logic handles the removal gracefully and adapts its expectations

### Scenario 2: Add New Shared Pattern

When introducing a new shared pattern (e.g., a new utility in mcp-core):

1. Implement the new pattern within the mcp-core package, adhering to its best practices
2. Thoroughly update mcp-core's CLAUDE.md and ARCHITECTURE.md to document the new pattern
3. Strategically identify and migrate each relevant agent to utilize the new shared pattern
4. Update affected agent documentation to reflect the adoption of the new pattern
5. Verify consistent and correct usage across all migrated agents

### Scenario 3: Fix Dates/Timelines

When project dates, deadlines, or timelines become outdated:

1. Obtain the most current and accurate date context for the entire project
2. Search all relevant documentation (README.md, ARCHITECTURE.md, CLAUDE.md, strategy documents) for date references
3. Update all references with precise, realistic, and consistent timelines or dates
4. Ensure consistency of dates and timelines across all related documents

### Scenario 4: Standardize Structure

When enforcing a consistent file or directory structure:

1. Clearly define the standard architectural pattern for files, directories, and modules
2. Analyze current variations across the codebase to identify all deviations from the standard
3. Create a detailed migration plan, possibly broken down by agent or module
4. Systematically update each instance to conform to the new standard using Edit/MultiEdit for precise movements
5. Thoroughly document the new standard in ARCHITECTURE.md and relevant CLAUDE.md files

### Scenario 5: Remove Scores/Opinions (CRITICAL)

This is a paramount task for Supabolt's compliance integrity.

1. Perform an exhaustive, case-insensitive grep across the entire src/ directory for banned terms: `grep -r -i "score\|risk\|suspicious\|high\|medium\|low" --include="*.ts" src/`

2. For every instance found, replace the subjective assessment with verifiable, factual observations or direct matches to regulatory patterns:

   - Example: "risk_score: 85" → "patterns_detected: ['rapid_movement', 'mixing_interaction']"
   - Example: "risk: high" → "velocity: 450_transactions_per_hour"
   - Example: "suspicious: true" → "matches_fatf_pattern: structuring"

3. Review and update all affected data models and return types to ensure that score fields are removed and only factual data is transmitted

4. Verify rigorously that the agent's output and internal state are solely based on facts and regulatory patterns, without any subjective scoring or opinions

5. Update any affected tests to expect factual data rather than subjective scores

## Error Recovery

If updates cause unexpected issues, implement a robust recovery plan:

- **Revert Strategy**: Maintain a clear and easy-to-use reversion strategy. All changes must be atomic or easily reversible
- **Incremental Updates**: Prioritize updating one agent or module at a time. Never attempt large, monolithic changes without checkpoints
- **Validation Checkpoints**: Implement validation steps after each major logical change, not just at the end of the entire process
- **Fallback Options**: For critical updates, have predefined fallback plans or emergency hotfixes in place

## Final Checklist

Before considering any project-wide update complete, this rigorous checklist MUST be satisfied:

[x] All TodoWrite items related to the update are marked as "completed."

[x] All agents and shared modules build successfully without any errors.

[x] All documentation (CLAUDE.md, README.md, ARCHITECTURE.md) precisely matches the current code implementation.

[x] Absolutely no new security vulnerabilities have been introduced.

[x] Strict adherence to the Unix philosophy is maintained across all agents.

[x] Consistent patterns are enforced across the entire codebase.

[x] No hardcoded values, magic strings, or unsanitized sensitive data are present.

[x] Robust error handling mechanisms are preserved and functioning correctly.

[x] All relevant tests are updated and pass successfully.

[x] All CLAUDE.md files are accurate, comprehensive, and provide clear guidance.

[x] CRITICAL: NO SCORES OR OPINIONS are present in the final output or codebase. Only verifiable facts and regulatory patterns remain.

[x] All subjective assessments have been rigorously removed (except in designated test assertions if necessary for mocking).

[x] Verified with: grep -r -i "score\|risk\|suspicious" --include="\*.ts" src/ confirms no banned terms remain in production code.

Remember: This is mission-critical infrastructure handling real value. Surgical precision is required. Millions in value are at stake. Security is non-negotiable.
