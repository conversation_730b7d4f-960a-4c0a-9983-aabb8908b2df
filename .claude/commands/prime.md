---
allowed-tools: Glob, Read, Task
description: Quick system orientation - understand <PERSON><PERSON><PERSON>'s architecture without loading entire codebase
---

## What is <PERSON><PERSON><PERSON>?

**Supabolt** is a Bitcoin-native Compliance OS that applies Unix philosophy to digital asset compliance. We transform complex compliance requirements into simple, composable workflows by chaining specialized agents that each do one thing well.

- **LangGraph** orchestrates intelligent compliance workflows
- **MCP agents** provide modular, single-purpose tools (7 specialized agents)
- **@supabolt/mcp-core** ensures standardized agent development
- **Unix Philosophy** - compose simple tools into powerful workflows

Key components include: bitcoin-agent, lightning-agent, sanctions-agent, usdc-agent, analytics-agent, blockchain-agent, with the Python-based supabolt-supervisor coordinating workflows.

## System Orientation (Start Here)

### 0. Business & Strategy

- Business strategy: @reports/STRATEGY.md
- High-level overview: @README.md

### 1. Core Architecture

- Project context: @CLAUDE.md
- Claude-specific documentation: @.claude/README.md
- Available commands: @.claude/commands/README.md
- Look for available agents in directories ending with -agent/
- Supervisor orchestration: @supabolt-supervisor/README.md

### 2. Understand the Stack

- **Orchestration**: LangGraph 1.x workflow: @supabolt-supervisor/workflow.py
- **MCP Integration**: Agent invocation: @supabolt-supervisor/mcp/process_client.py
- **Standardization**: What all agents inherit: @packages/mcp-core/src/server/base-server.ts
- **Tool Pattern**: How agents expose capabilities: @packages/mcp-core/src/tools/base-tool.ts

### 3. Dynamic Agent Discovery and Analysis

Use the Task tool to discover all agents and their capabilities from live code:

```typescript
Task({
  description: 'Discover all agents and capabilities',
  prompt: `
    Execute dynamic agent discovery:
    
    1. Use Glob to find all agent directories: pattern "*-agent"
    
    2. For each agent found:
       - For JS agents: read package.json for name/version/description
       - Count MCP tools using Glob: "{agent}/src/mcp/tools/*.ts" (exclude *.test.ts)
       - Check documentation: README.md, ARCHITECTURE.md, CLAUDE.md exists
       - For supabolt-supervisor, note manifests and tests under manifests/ and tests/
    
    3. Create comprehensive table:
       | Agent | Version | Description | Tool Count | Documentation | Integrations |
    
    4. Generate architecture summary explaining LangGraph + MCP pattern
    
    5. Provide current system metrics
    
    This makes code the source of truth, not static documentation.
  `,
});
```

## Your Task

Provide a **focused** system overview:

1. **Architecture Summary** (1 paragraph)

   - How LangGraph + MCP agents work together
   - Role of mcp-core standardization
   - Key architectural decisions and why they matter

2. **Available Capabilities** (dynamically discovered)

   Based on the agents found by the glob command above:

   Create a table like this:
   | Agent | Purpose | Tools | Primary Function | Key Integrations |
   |-------|---------|-------|------------------|------------------|
   | bitcoin-agent | [from package.json] | [count] | Data retrieval | lightning, sanctions |
   | ... | ... | ... | ... | ... |

   Then explain how these agents compose together following Unix philosophy - small, focused tools that combine to solve complex compliance challenges.

3. **Quick Start Guide**

   - How to analyze a specific agent ("analyze bitcoin agent")
   - Where to find compliance workflows
   - Key files for understanding the system

4. **What Makes This Special**
   - Unix philosophy applied to compliance workflows
   - First Lightning Network native compliance solution
   - Platform architecture ready for composable agent ecosystem
   - $200B+ stablecoin market opportunity with USDC agent

## Smart Analysis Tips

- **Don't load everything** - Use sampling and patterns
- **Focus on interfaces** - Tool definitions > implementations
- **Understand "why"** - Architecture decisions > file listings
- **Think platform** - This is infrastructure for an ecosystem
- **Check hidden files** - .claude/, .env.example, .mcp.json contain important context
- **Security matters** - Review .gitignore to understand security boundaries

Remember: A good primer helps someone understand the system in 5 minutes, not exhaustively document every file.
