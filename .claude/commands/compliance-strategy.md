---
allowed-tools: Read, Glob
description: Analyze compliance strategy and market positioning for financial institutions
---

## What is Supa<PERSON>? (Context for Analysis)

**Supabolt** is a Bitcoin-native Compliance OS that applies Unix philosophy to digital asset compliance.

- **Core Innovation**: First Lightning Network native compliance solution with composable workflows
- **Architecture**: LangGraph orchestrates MCP agents that each do one thing well
- **Business Model**: Platform for compliance workflows, not just data queries
- **Target Market**: VASPs, exchanges, financial institutions automating crypto compliance

## Technical Foundation for Compliance

### 1. Compliance Architecture Pattern

- How we standardize compliance: Read the file packages/mcp-core/src/types/compliance.ts
- Lightning compliance innovation: Read the file lightning-agent/src/mcp/tools/audit-lightning-compliance.ts
- Sanctions screening integration: Read the file sanctions-agent/src/mcp/tools/check-sanctions-status.ts

### 2. Real Compliance Workflows (Not Just Tools)

- Compliance orchestration: Read the file supabolt-supervisor/workflow.py
- MCP execution pipeline: Read the file supabolt-supervisor/mcp/process_client.py

### 3. Integration Points

- Agent discovery and coordination: Read the file supabolt-supervisor/manifests/toolsets.yaml
- Bitcoin analysis integration: Read the file bitcoin-agent/package.json
- Lightning compliance integration: Read the file lightning-agent/package.json
- USDC stablecoin integration: Read the file usdc-agent/package.json

## Your Task

Analyze Supabolt's compliance strategy as if advising a major financial institution:

### 1. **Regulatory Coverage Assessment**

- FATF compliance gaps
- MiCA readiness
- FinCEN requirements
- Regional variations handling

### 2. **Lightning Network Compliance Innovation**

- First-mover advantage analysis
- Technical barriers for competitors
- Regulatory acceptance pathway
- Risk vs opportunity for FIs

### 3. **Competitive Analysis**

- vs Chainalysis (market leader) - workflow automation vs data queries
- vs Elliptic (UK/EU focus) - Unix composability vs monolithic solution
- vs TRM Labs (DeFi focus) - Lightning native vs Ethereum-first
- vs CipherTrace - open platform vs closed system
- **Unique differentiation**: Only platform with composable compliance workflows

### 4. **Adoption Barriers**

- Integration complexity
- Compliance officer training
- Audit/attestation needs
- Performance at scale

### 5. **Strategic Recommendations**

- Go-to-market approach
- Partnership priorities
- Feature roadmap based on FI needs
- Pricing model implications

Focus on what compliance officers and CROs actually care about.
