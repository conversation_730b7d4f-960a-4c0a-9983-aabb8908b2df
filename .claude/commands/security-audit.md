---
allowed-tools: Task, Glob, Read, Edit, MultiEdit, Write, TodoWrite, <PERSON>re<PERSON>, <PERSON><PERSON>(find:*), <PERSON><PERSON>(grep:*), Bash(sort:*)
description: Comprehensive security audit for mission-critical compliance infrastructure
---

## Security Audit Command

Performs systematic security review of the Supabolt codebase. This is mission-critical infrastructure handling financial compliance - security is non-negotiable.

## Critical Security Requirements

- **No console.log** in production code
- **All errors sanitized** before client exposure
- **No hardcoded secrets** or credentials
- **Input validation** on all external data
- **Authentication** properly implemented
- **Audit trails** for compliance

## Phase 0: Hidden Files and Directories Audit

### Discover All Hidden Configuration

Find all hidden files and directories:

Use Bash to run: find . -name "._" -type f -o -name "._" -type d | grep -v "node_modules" | grep -v ".git/" | sort

Pay special attention to:

- .env files (may contain secrets)
- .claude directories (may have sensitive docs)
- .mcp.json files (MCP configuration)
- .gitignore files (security boundaries)

## Phase 1: Console.log Detection

### Find All console.log Instances

Search for console.log usage across the codebase:

1. Search all agent src/ directories
2. Check packages/mcp-core/src/

Exclude: test files, example files, scripts

Use Grep to find console logging:

- Pattern: console\\.(log|error|warn|info)
- Path: .
- Glob: src/\*_/_.ts
- Show line numbers

### Remediation

Replace all console.log with proper logging:

```typescript
// BAD
console.log('Processing transaction', txId);

// GOOD
context.getLogger().info('Processing transaction', { txId });
```

## Phase 2: Error Sanitization Audit

### Check Error Handling Patterns

Find all catch blocks in the codebase:

Use Grep tool:

- Pattern: catch.\*\\{
- Path: .
- Glob: **/src/**/\*.ts
- Show 5 lines after match to verify context.sanitizeError() usage

### Required Pattern

All error handling must follow:

```typescript
catch (error) {
  const sanitizedError = context.sanitizeError(
    error instanceof Error ? error : new Error(String(error))
  );
  // Use sanitizedError, never raw error
}
```

## Phase 3: Secrets and Hardcoded Values

### Search for Potential Secrets

Search for potential hardcoded secrets:

1. API keys (pattern: _\_KEY, _\_SECRET)
2. Passwords (pattern: password =, pwd =)
3. Private keys (pattern: private, priv)
4. Tokens (pattern: token =, bearer)
5. Connection strings with credentials

Check all .ts, .js, .json files
Exclude: .env.example, test fixtures

Common patterns to search:

- api*key.*=.\_[\'"]\\w+['"]
- password._=._[\'"]\\w+['"]
- secret._=._[\'"]\\w+['"]
- private*key.*=.\_[\'"]\\w+['"]
- bearer.\*[\'"]\\w+['"]

### Environment Variable Check

Ensure all secrets come from environment:

```typescript
// GOOD
const apiKey = process.env.OPENSANCTIONS_API_KEY;
if (!apiKey) {
  throw new Error('OPENSANCTIONS_API_KEY required');
}

// BAD
const apiKey = 'sk_live_abc123...';
```

### Check Hidden Configuration Files

Find and check all .env files:

Use Bash to run: find . -name ".env\*" -type f | grep -v node_modules

Then verify:

1. No actual secrets in .env.example files
2. .env files are in .gitignore
3. Look for patterns like .env.local, .env.production

Read the .gitignore file to ensure it includes: .env, .env._, _.key, \*.pem

## Phase 4: Input Validation

### Verify Zod Schema Usage

Find all tool implementations using Glob:

- Pattern: \*_/src/mcp/tools/_.ts

For each tool found, verify:

1. Has Zod schema defined
2. Uses schema.parseAsync() or schema.parse()
3. No direct parameter usage without validation

### Check External API Calls

Audit all external API calls (axios, fetch, http):

1. Verify input sanitization
2. Check timeout configuration
3. Ensure error handling
4. Verify no credential leakage in URLs

## Phase 5: Authentication Patterns

### LND Authentication

Verify macaroon handling using Grep:

- Pattern: macaroon|Macaroon
- Path: lightning-agent
- Show content

Verify:

- Proper file permissions check
- No macaroon content in logs
- Secure transport (TLS)

### API Key Management

Check all API key usage with Grep:

- Pattern: process\\.env\\.
- Path: .
- Glob: **/src/**/\*.ts
- Show content

Verify:

1. Keys validated at startup
2. Never logged
3. Not included in error messages

## Phase 6: Logging Audit

### Sensitive Data in Logs

Review all logger calls for:

1. Private keys or addresses
2. API keys or tokens
3. Personal information
4. Full error stacks with secrets

Check: logger.info, logger.debug, logger.error calls

### Required Sanitization

Use sensitivePatterns.ts for redaction:

```typescript
// From lightning-agent
const sanitizeLog = (data: any) => {
  // Redact sensitive patterns
  return redactSensitiveData(data, SENSITIVE_PATTERNS);
};
```

## Phase 7: Dependency Vulnerabilities

### Check for Known Vulnerabilities

Run npm audit for each component:

First, get the list of directories to audit:
Use Bash to run: ls -d \*-agent packages/mcp-core 2>/dev/null | head -20

Then for each directory found, run npm audit individually:

- For bitcoin-agent: Use Bash to run: cd bitcoin-agent && npm audit
- For lightning-agent: Use Bash to run: cd lightning-agent && npm audit
- For sanctions-agent: Use Bash to run: cd sanctions-agent && npm audit
- For usdc-agent: Use Bash to run: cd usdc-agent && npm audit
- For blockchain-agent: Use Bash to run: cd blockchain-agent && npm audit
- For analytics-agent: Use Bash to run: cd analytics-agent && npm audit
- For supabolt-supervisor: Use Bash to run: cd supabolt-supervisor && poetry run pip-audit
- For packages/mcp-core: Use Bash to run: cd packages/mcp-core && npm audit

### Review Direct Dependencies

Check package.json files for:

1. Deprecated packages
2. Unmaintained packages (>2 years old)
3. Packages with few downloads
4. Git dependencies (security risk)

## Phase 8: MCP Security

### Tool Permission Verification

Each tool should declare required permissions. Use Grep:

- Pattern: permissions._:._\\[
- Path: .
- Glob: \*_/tools/_.ts
- Show 2 lines before and after match

### Error Response Sanitization

Verify all tools use BaseTool error handling:

```typescript
// Should sanitize via BaseTool.formatError()
protected formatError(error: unknown, context: C): ToolResponse {
  const sanitizedError = context.sanitizeError(error);
  // Never expose raw error to client
}
```

## Security Checklist

Create comprehensive security checklist using TodoWrite with these items:

- Remove all console.log from production code (high priority)
- Verify error sanitization in all catch blocks (high priority)
- Check for hardcoded secrets (high priority)
- Audit input validation (high priority)
- Review authentication patterns (high priority)
- Check logging for sensitive data (medium priority)
- Run dependency vulnerability scan (medium priority)
- Verify MCP permission model (medium priority)

## Common Security Issues

### Issue 1: Unsanitized Error Messages

**Problem**: Raw errors exposed to clients
**Fix**: Always use context.sanitizeError()

### Issue 2: Verbose Logging

**Problem**: Sensitive data in logs
**Fix**: Implement redaction patterns

### Issue 3: Missing Input Validation

**Problem**: Direct use of user input
**Fix**: Add Zod schemas

### Issue 4: Credential Leakage

**Problem**: Secrets in error messages
**Fix**: Sanitize all error responses

## Compliance Requirements

For financial compliance software:

1. **Audit Trails**: Every action must be logged
2. **Data Integrity**: No data modification without trace
3. **Access Control**: Proper authentication required
4. **Error Handling**: No information leakage
5. **Secure Transport**: TLS/SSL required

## Final Security Report

Generate comprehensive security audit summary:

1. Critical Issues Found:
   - Count of console.log instances
   - Unsanitized errors
   - Hardcoded secrets
2. Medium Priority Issues:
   - Missing input validation
   - Verbose logging
   - Outdated dependencies
3. Recommendations:
   - Immediate fixes required
   - Best practices to implement
   - Ongoing monitoring needs
4. Compliance Status:
   - Audit trail completeness
   - Data handling security
   - Authentication strength

## Remediation Priority

1. **IMMEDIATE** (Block deployment):

   - Hardcoded secrets
   - Missing authentication
   - Unsanitized errors with stack traces

2. **HIGH** (Fix within 24h):

   - console.log in production
   - Missing input validation
   - Credential logging

3. **MEDIUM** (Fix within week):
   - Verbose logging
   - Deprecated dependencies
   - Missing permissions

Remember: This is financial compliance infrastructure. A single security vulnerability could compromise millions in value and destroy trust. Zero tolerance for security issues.
