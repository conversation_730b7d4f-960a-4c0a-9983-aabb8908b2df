# Supabolt

**Evidence-generation and workflow automation for the agent economy.**

<PERSON><PERSON><PERSON> gives regulated crypto teams a factual record of what their autonomous systems did, why they did it, and which mandates authorized the movement of value. The platform fuses a LangGraph supervisor with specialist Model Context Protocol (MCP) agents so compliance officers receive regulator-ready case files rather than opaque risk scores.

## Why Evidence Matters Now

- **MiCA, Travel Rule, and US state regimes** demand verifiable audit trails for every material transfer.
- **Agent Payment Protocol (AP2) + A2A x402** introduces mandate-native settlement that regulators will expect enterprises to interpret correctly.
- **AI-native competitors** already generate natural-language reports; what they lack is cryptographic proof that a payment respected policy.

Su<PERSON><PERSON>’s FACTS ONLY architecture keeps every workflow grounded in deterministically sourced data. No scores. No heuristics. Just the facts an examiner demands.

## What Ships Today

- **Bitcoin + Lightning telemetry** from customer-controlled nodes with rich LND coverage for existing revenue flows.
- **Stablecoin evidence (USDC)** via on-chain balance, whale heuristics, and jurisdiction mapping.
- **Sanctions + pattern analytics** that collapse third-party data and internal telemetry into a single compliance fact sheet.
- **supabolt-supervisor (LangGraph 1.x)** that sequences agents, enforces audit logging, and emits machine-verifiable reports.

## Value Proposition

- **Regulator-ready reports** – Continuous Bitcoin, Lightning, and stablecoin evidence that satisfies MiCA, GENIUS, and Travel Rule inquiries.
- **Keep capital flowing** – On-demand sanctions and transaction records prevent compliance gaps from blocking banking or fundraising.
- **One compliance workflow** – Finance and ops teams meet obligations across every rail without adding headcount or juggling tools.

## Roadmap: Three-Stage Pivot (Investor Ready)

1. **Stabilize Lightning Revenue (now–Q4 2025)**

   - Lock the production Lightning stack and guarantee SLA-backed support.
   - Ship instrumentation proving “facts only” case files for Bitcoin, Ethereum, and major stablecoins.
   - Stand up BYOK analytics connectors (Chainalysis, Elliptic) to reuse customer telemetry inside Supabolt workflows.

2. **Build AP2 + Spark Foundations (Q1–Q2 2026)**

   - Deliver AP2 mandate parsing, attestation verification, and Spark network telemetry.
   - Run dual-validation pilots so customers compare mandate evidence with existing controls.

3. **Mandate-Native Compliance Platform (2H 2026)**
   - Converge Lightning, Spark, and AP2 flows into a unified evidence layer.
   - Launch mandate compliance proofs, zero-knowledge attestations, and partner-certified playbooks.

Each milestone is measured by shipped code, evidence artifacts, and design partner adoption, not slideware.

### Strategic Optional Bets

- **Agent Payment Protocol (AP2) compliance automation** – Mandate parsing and evidence packaging activated if AP2 partner adoption (PayPal, Mastercard, Coinbase, Google) accelerates.
- **Spark Layer 2 telemetry** – Lightspark’s Spark wallets (Wallet of Satoshi, Breez, Tether WDK, Magic Eden, Theya, Brale, Privy) trigger dedicated monitoring and evidence capsules as usage scales.

## Architecture at a Glance

- **Supabolt Supervisor** (Python + LangGraph 1.x): Supervises workflows, binds tools dynamically, and signs audit trails.
- **Specialist MCP Agents**: Bitcoin, Lightning, USDC, sanctions, analytics, blockchain intelligence, and (soon) Spark/AP2.
- **FACTS ONLY State Machine**: Guarantees outputs remain verifiable observations aligned to explicit regulatory patterns.
- **Audit & Telemetry Layer**: Structured logs suitable for regulator handoff or automated evidence packaging.

## Core Agents and Status

| Agent                            | Domain                     | Status         | Notes                                                                      |
| -------------------------------- | -------------------------- | -------------- | -------------------------------------------------------------------------- |
| `bitcoin-agent`                  | Bitcoin Core RPC           | Active         | Transaction tracing, mempool, and chain health.                            |
| `lightning-agent`                | LND                        | Active         | Production workflow support with pagination, filtering, and diagnostics.   |
| `usdc-agent`                     | Ethereum (USDC)            | Active         | Balance telemetry + whale exposure heuristics.                             |
| `sanctions-agent` / `ofac-agent` | OpenSanctions & local OFAC | Active         | Batch/entity screening with deterministic responses.                       |
| `analytics-agent`                | Pattern synthesis          | Active         | Reconciles cross-agent facts into case files.                              |
| `blockchain-agent`               | BitQuery GraphQL           | Active         | Bridges BitQuery data today; BYOK analytics connectors are in development. |
| `spark-agent`                    | Spark L2                   | In development | Skeleton scaffold ready for SDK integration.                               |

Explore each workspace’s `AGENTS.md` for setup, configuration, and MCP inspector commands.

## Compliance Guarantees

- **FACTS ONLY**: Subjective risk grades are banned at the type level inside the supervisor state.
- **Deterministic provenance**: Every observation includes the tool + source that produced it.
- **Human review flags**: Mandate when human approval is required instead of guessing at risk tolerance.
- **Audit readiness**: `AuditLogger` signs structured output for downstream storage or regulator submission.

## Getting Started

```bash
git clone https://github.com/supabolt/supabolt.git
cd supabolt
make install                    # bootstrap agents and supabolt-supervisor
make build                      # compile all TypeScript agents
make test                       # run agent Vitest suites + supervisor pytest suite
# shared services + live dev session
make up                         # start Redis / LocalStack
make dev                        # launch supervisor API + internal AI agent (requires make up)
```

Run agents individually for local development:

```bash
cd bitcoin-agent && npm run dev
cd lightning-agent && npm run dev
cd supabolt-supervisor && poetry run uvicorn api.server:app --reload
```

Use the LangGraph workflow harness to exercise end-to-end compliance checks (`npm run test` within each package or `./scripts/test.sh` for the full matrix).

### Demo Workflow

```bash
# REST API on port 8080
./scripts/start-demo.sh
```

The script builds required agents, validates Redis, runs health probes, and prints both local and ngrok URLs. You can immediately test the supervisor REST API:

```bash
curl -s http://localhost:8080/api/v1/analyze \
  -H 'Content-Type: application/json' \
  -d '{"entityId":"**********************************","entityType":"bitcoin_address"}' | jq
```

### Redis-Backed Checkpointing

Supervisor state can persist to Redis through the `langgraph.checkpoint.redis` saver. The helper scripts below manage the bundled container and validate connectivity:

```bash
# Start Redis locally
docker compose up -d redis

# Run the supervisor with persistence enabled
cd supabolt-supervisor
poetry run uvicorn api.server:app --host 0.0.0.0 --port 8080

# Validate checkpoints are being written
make redis-test

# Stop Redis when finished
docker compose stop redis
```

Adjust retention via `REDIS_CHECKPOINT_TTL_MINUTES` (default 7 days). In environments without Redis the supervisor falls back to in-memory checkpoints automatically.

## Contributing

- Follow the repository guidelines in `AGENTS.md`.
- Use Conventional Commits (`feat(agent): ...`, `chore(docs): ...`).
- Run `npm run lint`, `npm run typecheck`, and relevant tests before submitting PRs.
- Document new environment variables in the affected agent guide.

## Contact

**Email**: <EMAIL>  
**GitHub**: [github.com/supabolt](https://github.com/supabolt)

—

_Supabolt transforms autonomous crypto activity into regulator-grade evidence so customers can ship AP2-era products with confidence._
