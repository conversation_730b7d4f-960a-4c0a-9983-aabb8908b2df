{"mcpServers": {"bitcoin": {"command": "node", "args": ["/path/to/supabolt/bitcoin-agent/build/server.js"], "env": {"BITCOIN_RPC_HOST": "your-bitcoin-rpc-host", "BITCOIN_RPC_USER": "your-rpc-username", "BITCOIN_RPC_PASSWORD": "your-rpc-password"}}, "sanctions": {"command": "node", "args": ["/path/to/supabolt/sanctions-agent/build/server.js"], "env": {"OPENSANCTIONS_API_KEY": "your-opensanctions-api-key"}}, "supabolt-internal": {"command": "node", "args": ["/path/to/supabolt/internal-ai-agent/build/server.js"], "cwd": "/path/to/supabolt/internal-ai-agent", "env": {"OPENAI_MODEL": "gpt-4o-mini", "ANTHROPIC_API_KEY": "your-anthropic-api-key-here", "ANTHROPIC_MODEL": "claude-3-5-sonnet-latest", "LLM_PROVIDER_PRIORITY": "anthropic,openai"}}}}