.PHONY: help up down build install test audit redis-test supervisor-setup supervisor-test supervisor-serve dev supervisor-dev internal-dev test-golden

help:
	@echo "make up               - Start Redis and LocalStack"
	@echo "make down             - Stop all services"
	@echo "make build            - Build TypeScript agents"
	@echo "make install          - Install TypeScript agents and supervisor dependencies"
	@echo "make test             - Run agent + supervisor test suites"
	@echo "make audit            - Run npm & poetry audits"
	@echo "make redis-test       - Verify Redis Stack modules for supervisor"
	@echo "make supervisor-setup - Install Poetry environment for supabolt-supervisor"
	@echo "make supervisor-test  - Run supabolt-supervisor pytest suite"
	@echo "make supervisor-serve - Launch supabolt-supervisor API locally"
	@echo "make supervisor-dev   - Supervisor dev server helper"
	@echo "make internal-dev     - Internal AI agent dev server helper"
	@echo "make dev              - Launch supervisor API and internal AI agent (requires make up)"
	@echo "make kms-setup        - Setup KMS keys and secrets"

up:
	docker-compose up -d

down:
	docker-compose down

build:
	./scripts/build.sh

kms-setup:
	@./scripts/setup-kms.sh

install:
	./scripts/install.sh

test:
	./scripts/test.sh
	$(MAKE) supervisor-test

audit:
	./scripts/audit.sh
	cd supabolt-supervisor && poetry run pip-audit

redis-test:
	cd supabolt-supervisor && poetry run python scripts/check_redis_modules.py

supervisor-setup:
	cd supabolt-supervisor && poetry install

supervisor-test:
	cd supabolt-supervisor && poetry run pytest

test-golden:
	cd supabolt-supervisor && poetry run pytest tests/test_golden_dataset.py

supervisor-serve:
	./supabolt-supervisor/scripts/serve.sh

supervisor-dev:
	SUPABOLT_SUPERVISOR_RELOAD=true ./supabolt-supervisor/scripts/serve.sh

internal-dev:
	./internal-ai-agent/scripts/dev.sh

dev:
	./scripts/dev.sh
