{"compilerOptions": {"module": "NodeNext", "moduleResolution": "NodeNext", "target": "ES2022", "lib": ["ES2022"], "strict": true, "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["**/src/**/*.ts", "**/src/**/*.mts", "**/src/**/*.cts", "**/config/**/*.ts", "**/config/**/*.mts", "**/config/**/*.cts"], "exclude": ["node_modules", "**/node_modules", "dist", "build", "**/dist", "**/build", "**/test/**/*", "**/*.test.ts"]}