#!/usr/bin/env bash

set -euo pipefail

ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}Supabolt multi-repo installer${NC}"

require_node() {
  if ! command -v node >/dev/null 2>&1; then
    echo -e "${RED}Node.js is not installed. Install Node.js v18 or newer before continuing.${NC}"
    exit 1
  fi

  local major
  major="$(node -v | cut -d 'v' -f 2 | cut -d '.' -f 1)"
  if [[ "${major}" -lt 18 ]]; then
    echo -e "${RED}Node.js v18 or higher is required. Detected v${major}.${NC}"
    exit 1
  fi
}

# Load .env if present so NPM_TOKEN and other shared vars come through.
load_env() {
  if [[ -z "${NPM_TOKEN:-}" && -f "${ROOT_DIR}/.env" ]]; then
    set -a
    # shellcheck disable=SC1091
    source "${ROOT_DIR}/.env"
    set +a
  fi

  if [[ -z "${NPM_TOKEN:-}" ]]; then
    echo -e "${RED}NPM_TOKEN is not set. Export it or add it to ${ROOT_DIR}/.env before running this installer.${NC}"
    exit 1
  fi
}

configure_hooks() {
  if git rev-parse --is-inside-work-tree >/dev/null 2>&1; then
    local current_path
    current_path="$(git config core.hooksPath || true)"
    if [[ "${current_path}" != ".husky" ]]; then
      git config core.hooksPath .husky
      echo -e "${GREEN}Git hooks path configured to .husky/${NC}"
    fi
  fi
}

# Determine which repositories to install.
resolve_targets() {
  local -a targets
  if [[ $# -gt 0 ]]; then
    for repo in "$@"; do
      if [[ -d "${ROOT_DIR}/${repo}" ]]; then
        targets+=("${ROOT_DIR}/${repo}")
      else
        echo -e "${YELLOW}Skipping unknown repository: ${repo}${NC}"
      fi
    done
  else
    if command -v jq >/dev/null 2>&1 && [[ -f "${ROOT_DIR}/AGENT_METADATA.json" ]]; then
      while IFS= read -r name; do
        if [[ -d "${ROOT_DIR}/${name}" ]]; then
          targets+=("${ROOT_DIR}/${name}")
        fi
      done < <(jq -r 'keys[]' "${ROOT_DIR}/AGENT_METADATA.json")
    else
      for path in "${ROOT_DIR}"/*-agent; do
        [[ -d "${path}" ]] && targets+=("${path}")
      done
    fi
  fi
  printf '%s\n' "${targets[@]:-}" | sort
}

require_node
configure_hooks
load_env

mapfile -t TARGET_REPOS < <(resolve_targets "$@") || true
if [[ ${#TARGET_REPOS[@]} -eq 0 ]]; then
  echo -e "${YELLOW}No repositories matched the install criteria.${NC}"
  exit 0
fi

declare -a SUCCEEDED=()
declare -a FAILED=()
declare -a SKIPPED=()

echo -e "${GREEN}Preparing to run install scripts for ${#TARGET_REPOS[@]} repositories...${NC}"

for repo_path in "${TARGET_REPOS[@]}"; do
  repo_name="$(basename "${repo_path}")"
  install_script="${repo_path}/scripts/install.sh"

  if [[ ! -x "${install_script}" ]]; then
    echo -e "${YELLOW}[${repo_name}] No executable scripts/install.sh found. Skipping.${NC}"
    SKIPPED+=("${repo_name}")
    continue
  fi

  echo -e "${GREEN}[${repo_name}] Running scripts/install.sh...${NC}"
  set +e
  (cd "${repo_path}" && "${install_script}")
  status=$?
  set -e

  if [[ ${status} -eq 0 ]]; then
    SUCCEEDED+=("${repo_name}")
  else
    echo -e "${RED}[${repo_name}] Install script failed with status ${status}.${NC}"
    FAILED+=("${repo_name}")
  fi
  echo
done

echo -e "${GREEN}Install summary:${NC}"
if [[ ${#SUCCEEDED[@]} -gt 0 ]]; then
  printf '  %b %s\n' "${GREEN}✔${NC}" "${SUCCEEDED[*]}"
fi
if [[ ${#FAILED[@]} -gt 0 ]]; then
  printf '  %b %s\n' "${RED}✖${NC}" "${FAILED[*]}"
fi
if [[ ${#SKIPPED[@]} -gt 0 ]]; then
  printf '  %b %s\n' "${YELLOW}⚠${NC}" "${SKIPPED[*]}"
fi

if [[ ${#FAILED[@]} -gt 0 ]]; then
  exit 1
fi
