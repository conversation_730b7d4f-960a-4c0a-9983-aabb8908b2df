#!/usr/bin/env bash

set -euo pipefail

# --- Configuration ---
ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_DIR="${ROOT_DIR}/logs/demo"
MODE="api"
ORCHESTRATOR_PORT=""
ORCHESTRATOR_LOG="${LOG_DIR}/supabolt-supervisor.log"
STUDIO_PORT=2024
API_PORT_DEFAULT=8080

print_usage() {
    cat <<'EOF'
Usage: ./scripts/start-demo.sh [--studio]

Options:
  --studio    Launch LangGraph Studio instead of the REST API server.
  -h, --help  Show this help message.
EOF
}

while (($#)); do
    case "$1" in
        --studio)
            MODE="studio"
            shift
            ;;
        -h|--help)
            print_usage
            exit 0
            ;;
        *)
            echo "[demo] error: unknown option '$1'"
            print_usage
            exit 1
            ;;
    esac
done

if [[ -z "${ORCHESTRATOR_PORT}" ]]; then
    if [[ "${MODE}" == "studio" ]]; then
        ORCHESTRATOR_PORT="${STUDIO_PORT}"
    else
        ORCHESTRATOR_PORT="${API_PORT_DEFAULT}"
    fi
fi

# Create log directory
mkdir -p "${LOG_DIR}"

# --- Environment Setup ---

# Load shared environment variables (.env file for keys, endpoints, etc.)
if [[ -f "${ROOT_DIR}/.env" ]]; then
    echo "[demo] Sourcing and exporting environment variables from .env"
    set -a
    # shellcheck disable=SC1091
    source "${ROOT_DIR}/.env"
    set +a
fi

# --- Dependency Checks ---

if ! command -v curl >/dev/null 2>&1; then
    echo "[demo] error: curl is required but not installed"
    exit 1
fi

if ! command -v ngrok >/dev/null 2>&1; then
    echo "[demo] error: ngrok is required (see https://ngrok.com/download)"
    exit 1
fi

if ! command -v python3 >/dev/null 2>&1; then
    echo "[demo] error: python3 is required to encode URLs"
    exit 1
fi

# Check for netcat to wait for the server to start
if ! command -v nc >/dev/null 2>&1; then
    echo "[demo] error: netcat (nc) is required to wait for server startup"
    exit 1
fi

if ! command -v poetry >/dev/null 2>&1; then
    echo "[demo] error: poetry is required to run supabolt-supervisor"
    exit 1
fi

# Check for ngrok auth token
if [[ -z "${NGROK_AUTHTOKEN:-}" ]]; then
    echo "[demo] error: NGROK_AUTHTOKEN is not set. Add it to .env or export it before running the demo."
    exit 1
fi

# Persist ngrok auth token if not already done
ngrok config add-authtoken "${NGROK_AUTHTOKEN}" >/dev/null 2>&1 || true

# --- Global Functions ---

DEMO_PIDS=()

# Cleanup function executed on script exit (EXIT, INT, TERM)
cleanup() {
    local status=$?
    echo
    echo "[demo] cleaning up background agent processes..."
    # Kill all background processes started by this script
    for pid in "${DEMO_PIDS[@]:-}"; do
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid" 2>/dev/null || true
        fi
    done
    wait "${DEMO_PIDS[@]:-}" 2>/dev/null || true
    exit "$status"
}

trap cleanup EXIT INT TERM

# Starts an agent process and redirects its output to a log file
start_agent() {
    local name="$1"
    local dir="${ROOT_DIR}/${name}"
    if [[ ! -d "$dir" ]]; then
        echo "[demo] skipping ${name} (directory not found)"
        return
    fi
    
    echo "[demo] starting ${name} (logs: logs/demo/${name}.log)"
    (
        cd "$dir"
        npm run start
    ) >"${LOG_DIR}/${name}.log" 2>&1 & # Redirect stdout and stderr to log file, run in background
    DEMO_PIDS+=("$!") # Add PID to list for cleanup
}

# Waits for a local TCP port to start listening
wait_for_port() {
    local host="$1"
    local port="$2"
    local timeout=30
    local count=0
    echo "[demo] Waiting for ${host}:${port} to open..."
    # Use netcat (nc) to check the port status
    while ! nc -z -w 1 "${host}" "${port}" >/dev/null 2>&1; do
        if [ "$count" -ge "$timeout" ]; then
            echo "[demo] Error: Timeout waiting for ${host}:${port} after ${timeout} seconds."
            return 1
        fi
        sleep 1
        count=$((count + 1))
    done
    echo "[demo] ${host}:${port} is open."
    return 0
}

# --- Main Execution ---

# 1. Ensure Redis is running via Docker Compose
echo "[demo] ensuring redis container is running..."
docker compose -f "${ROOT_DIR}/docker-compose.yml" up -d redis >/dev/null

# 2. Build the project
echo "[demo] building orchestrator and agents (this may take a minute)..."
"${ROOT_DIR}/scripts/build.sh"

# 3. Start all agent processes
start_agent "bitcoin-agent"
start_agent "bitcoin-aws-agent"
start_agent "sanctions-agent"
start_agent "analytics-agent"

# 4. Launch Orchestrator and ngrok

if [[ "${MODE}" == "studio" ]]; then
    echo "[demo] LangGraph Studio mode is no longer supported. Use REST API mode instead."
    exit 1
fi

echo "[demo] launching supabolt-supervisor REST API (http://localhost:${ORCHESTRATOR_PORT})"
echo "[demo] logs: ${ORCHESTRATOR_LOG}"
cd "${ROOT_DIR}/supabolt-supervisor"

poetry install >/dev/null

# Start Supervisor in the background, logging to a file
(
    poetry run uvicorn api.server:app --host 0.0.0.0 --port "${ORCHESTRATOR_PORT}"
) >"${ORCHESTRATOR_LOG}" 2>&1 &

ORCHESTRATOR_PID=$!
DEMO_PIDS+=("${ORCHESTRATOR_PID}")

# Wait for the orchestrator server to fully start
wait_for_port "localhost" "${ORCHESTRATOR_PORT}" || exit 1

if [[ "${MODE}" != "studio" ]]; then
    echo "[demo] verifying REST API health..."
    if ! curl --silent --show-error --fail "http://localhost:${ORCHESTRATOR_PORT}/health" >/dev/null; then
        echo "[demo] error: REST API health check failed (see ${ORCHESTRATOR_LOG})"
        exit 1
    fi
fi

# Start ngrok tunnel in the background
echo "[demo] starting ngrok tunnel..."
ngrok http "${ORCHESTRATOR_PORT}" --authtoken "${NGROK_AUTHTOKEN}" --log=stdout --log-level=warn --region=us >"${LOG_DIR}/ngrok.log" 2>&1 &
NGROK_PID=$!
DEMO_PIDS+=("${NGROK_PID}")

# Poll the ngrok API to get the public URL
PUBLIC_URL=""
for _ in $(seq 1 40); do
    if PUBLIC_URL=$( { curl --silent --fail http://127.0.0.1:4040/api/tunnels 2>/dev/null || true; } \
        | python3 -c 'import sys,json; data=json.load(sys.stdin); tunnels=data.get("tunnels") or []; print(tunnels[0]["public_url"] if tunnels else "")' 2>/dev/null ); then
        if [[ -n "${PUBLIC_URL}" ]]; then
            break
        fi
    fi
    sleep 0.25
done

# 5. Report final status and wait

if [[ -n "${PUBLIC_URL}" ]]; then
    if [[ "${MODE}" == "studio" ]]; then
        ENCODED_URL=$(python3 -c "import urllib.parse,sys; print(urllib.parse.quote(sys.argv[1], safe=''))" "${PUBLIC_URL}")
        echo
        echo "[demo] Setup Complete."
        echo "[demo] ------------------------------------------------------------------------------------------------"
        echo "[demo] LangGraph Studio (local): http://localhost:${ORCHESTRATOR_PORT}/studio"
        echo "[demo] LangGraph Studio (external): https://smith.langchain.com/studio?baseUrl=${ENCODED_URL}"
        echo "[demo] Orchestrator logs: ${ORCHESTRATOR_LOG}"
        echo "[demo] ------------------------------------------------------------------------------------------------"
    else
        echo
        echo "[demo] Setup Complete."
        echo "[demo] ------------------------------------------------------------------------------------------------"
        echo "[demo] API Server (local): http://localhost:${ORCHESTRATOR_PORT}"
        echo "[demo] API Server (external): ${PUBLIC_URL}"
        echo "[demo] Health check: curl -sSf http://localhost:${ORCHESTRATOR_PORT}/health"
        echo "[demo] Analyze example:"
        echo "       curl -s http://localhost:${ORCHESTRATOR_PORT}/api/v1/analyze \\"
        echo "         -H 'Content-Type: application/json' \\"
        echo "         -d '{\"entityId\":\"**********************************\",\"entityType\":\"bitcoin_address\"}' | jq"
        echo "[demo] LangServe stream: curl -N http://localhost:${ORCHESTRATOR_PORT}/compliance/stream ..."
        echo "[demo] Orchestrator logs: ${ORCHESTRATOR_LOG}"
        echo "[demo] ------------------------------------------------------------------------------------------------"
    fi
else
    echo "[demo] Error: unable to determine ngrok public URL (check ${LOG_DIR}/ngrok.log)"
    exit 1
fi

echo
echo "[demo] All services are running in the background. Press Ctrl+C to stop them."
# Keep the script running by waiting on the main orchestrator PID
wait "${ORCHESTRATOR_PID}"
