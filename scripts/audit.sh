#!/usr/bin/env bash

set -euo pipefail

ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}Supabolt dependency audit${NC}"

require_node() {
  if ! command -v node >/dev/null 2>&1; then
    echo -e "${RED}Node.js is not installed. Install Node.js v18 or newer before continuing.${NC}"
    exit 1
  fi

  local major
  major="$(node -v | cut -d 'v' -f 2 | cut -d '.' -f 1)"
  if [[ "${major}" -lt 18 ]]; then
    echo -e "${RED}Node.js v18 or higher is required. Detected v${major}.${NC}"
    exit 1
  fi
}

resolve_targets() {
  local -a targets
  if [[ $# -gt 0 ]]; then
    for repo in "$@"; do
      if [[ -d "${ROOT_DIR}/${repo}" ]]; then
        targets+=("${ROOT_DIR}/${repo}")
      else
        echo -e "${YELLOW}Skipping unknown repository: ${repo}${NC}"
      fi
    done
  else
    if command -v jq >/dev/null 2>&1 && [[ -f "${ROOT_DIR}/AGENT_METADATA.json" ]]; then
      while IFS= read -r name; do
        if [[ -d "${ROOT_DIR}/${name}" ]]; then
          targets+=("${ROOT_DIR}/${name}")
        fi
      done < <(jq -r 'keys[]' "${ROOT_DIR}/AGENT_METADATA.json")
    else
      for path in "${ROOT_DIR}"/*-agent; do
        [[ -d "${path}" ]] && targets+=("${path}")
      done
    fi
  fi
  printf '%s\n' "${targets[@]:-}" | sort
}

require_node

mapfile -t TARGET_REPOS < <(resolve_targets "$@") || true
if [[ ${#TARGET_REPOS[@]} -eq 0 ]]; then
  echo -e "${YELLOW}No repositories matched the audit criteria.${NC}"
  exit 0
fi

declare -a CLEAN=()
declare -a ISSUES=()

echo -e "${GREEN}Running npm audit for ${#TARGET_REPOS[@]} repositories...${NC}"

for repo_path in "${TARGET_REPOS[@]}"; do
  repo_name="$(basename "${repo_path}")"
  echo -e "${GREEN}[${repo_name}] npm audit --production...${NC}"

  set +e
  audit_output=$(cd "${repo_path}" && npm audit --production 2>&1)
  status=$?
  set -e

  if [[ ${status} -eq 0 ]]; then
    CLEAN+=("${repo_name}")
  else
    ISSUES+=("${repo_name}")
    echo "${audit_output}"
  fi
  echo
done

echo -e "${GREEN}Audit summary:${NC}"
if [[ ${#CLEAN[@]} -gt 0 ]]; then
  printf '  %b %s\n' "${GREEN}✔${NC}" "${CLEAN[*]}"
fi
if [[ ${#ISSUES[@]} -gt 0 ]]; then
  printf '  %b %s\n' "${RED}✖${NC}" "${ISSUES[*]}"
  exit 1
fi
