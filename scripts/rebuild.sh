#!/usr/bin/env bash

set -euo pipefail

ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}Supabolt multi-repo rebuild runner${NC}"

require_node() {
  if ! command -v node >/dev/null 2>&1; then
    echo -e "${RED}Node.js is not installed. Install Node.js v18 or newer before continuing.${NC}"
    exit 1
  fi

  local major
  major="$(node -v | cut -d 'v' -f 2 | cut -d '.' -f 1)"
  if [[ "${major}" -lt 18 ]]; then
    echo -e "${RED}Node.js v18 or higher is required. Detected v${major}.${NC}"
    exit 1
  fi
}

resolve_targets() {
  local -a targets
  if [[ $# -gt 0 ]]; then
    for repo in "$@"; do
      if [[ -d "${ROOT_DIR}/${repo}" ]]; then
        targets+=("${ROOT_DIR}/${repo}")
      else
        echo -e "${YELLOW}Skipping unknown repository: ${repo}${NC}"
      fi
    done
  else
    if command -v jq >/dev/null 2>&1 && [[ -f "${ROOT_DIR}/AGENT_METADATA.json" ]]; then
      while IFS= read -r name; do
        if [[ -d "${ROOT_DIR}/${name}" ]]; then
          targets+=("${ROOT_DIR}/${name}")
        fi
      done < <(jq -r 'keys[]' "${ROOT_DIR}/AGENT_METADATA.json")
    else
      for path in "${ROOT_DIR}"/*-agent; do
        [[ -d "${path}" ]] && targets+=("${path}")
      done
    fi
  fi
  printf '%s\n' "${targets[@]:-}" | sort
}

require_node

mapfile -t TARGET_REPOS < <(resolve_targets "$@") || true
if [[ ${#TARGET_REPOS[@]} -eq 0 ]]; then
  echo -e "${YELLOW}No repositories matched the rebuild criteria.${NC}"
  exit 0
fi

declare -a SUCCEEDED=()
declare -a FAILED=()
declare -a SKIPPED=()

echo -e "${GREEN}Rebuilding ${#TARGET_REPOS[@]} repositories...${NC}"

for repo_path in "${TARGET_REPOS[@]}"; do
  repo_name="$(basename "${repo_path}")"

  if [[ ! -f "${repo_path}/package.json" ]]; then
    echo -e "${YELLOW}[${repo_name}] No package.json found. Skipping.${NC}"
    SKIPPED+=("${repo_name}")
    continue
  fi

  clean_script=$(cd "${repo_path}" && node -e "try { const pkg=require('./package.json'); if (pkg?.scripts?.clean) { process.stdout.write('1'); } } catch { }")
  build_script=$(cd "${repo_path}" && node -e "try { const pkg=require('./package.json'); if (pkg?.scripts?.build) { process.stdout.write('1'); } } catch { }")

  if [[ -z "${build_script}" ]]; then
    echo -e "${YELLOW}[${repo_name}] No build script defined. Skipping.${NC}"
    SKIPPED+=("${repo_name}")
    continue
  fi

  if [[ -n "${clean_script}" ]]; then
    echo -e "${GREEN}[${repo_name}] npm run clean${NC}"
    set +e
    (cd "${repo_path}" && npm run clean)
    clean_status=$?
    set -e
    if [[ ${clean_status} -ne 0 ]]; then
      echo -e "${RED}[${repo_name}] Clean failed with status ${clean_status}.${NC}"
      FAILED+=("${repo_name}")
      echo
      continue
    fi
  else
    echo -e "${YELLOW}[${repo_name}] No clean script defined. Skipping clean step.${NC}"
  fi

  echo -e "${GREEN}[${repo_name}] npm run build${NC}"
  set +e
  (cd "${repo_path}" && npm run build)
  build_status=$?
  set -e

  if [[ ${build_status} -eq 0 ]]; then
    SUCCEEDED+=("${repo_name}")
  else
    echo -e "${RED}[${repo_name}] Build failed with status ${build_status}.${NC}"
    FAILED+=("${repo_name}")
  fi
  echo
done

echo -e "${GREEN}Rebuild summary:${NC}"
if [[ ${#SUCCEEDED[@]} -gt 0 ]]; then
  printf '  %b %s\n' "${GREEN}✔${NC}" "${SUCCEEDED[*]}"
fi
if [[ ${#FAILED[@]} -gt 0 ]]; then
  printf '  %b %s\n' "${RED}✖${NC}" "${FAILED[*]}"
fi
if [[ ${#SKIPPED[@]} -gt 0 ]]; then
  printf '  %b %s\n' "${YELLOW}⚠${NC}" "${SKIPPED[*]}"
fi

if [[ ${#FAILED[@]} -gt 0 ]]; then
  exit 1
fi
