#!/usr/bin/env bash

set -euo pipefail

ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SUPERVISOR_SCRIPT="${ROOT_DIR}/supabolt-supervisor/scripts/serve.sh"

AGENT_DIRS=(
  "analytics-agent"
  "bitcoin-agent"
  "bitcoin-aws-agent"
  "blockchain-agent"
  "internal-ai-agent"
  "lightning-agent"
  "ofac-agent"
  "sanctions-agent"
  "spark-agent"
  "usdc-agent"
)

if [[ ! -x "${SUPERVISOR_SCRIPT}" ]]; then
  echo "Supervisor script not executable: ${SUPERVISOR_SCRIPT}" >&2
  exit 1
fi

for agent_dir in "${AGENT_DIRS[@]}"; do
  AGENT_SCRIPT="${ROOT_DIR}/${agent_dir}/scripts/dev.sh"
  if [[ ! -f "${AGENT_SCRIPT}" ]]; then
    echo "Agent script not found: ${AGENT_SCRIPT}" >&2
    echo "Attempting to use 'npm run dev' as a fallback."
  fi
done

SUPABOLT_SUPERVISOR_RELOAD="${SUPABOLT_SUPERVISOR_RELOAD:-true}" \
  "${SUPERVISOR_SCRIPT}" "$@" &
SUPERVISOR_PID=$!

AGENT_PIDS=()
cleanup() {
  echo "Shutting down supervisor and agents..."
  if ps -p "${SUPERVISOR_PID}" >/dev/null 2>&1; then
    kill "${SUPERVISOR_PID}" >/dev/null 2>&1 || true
  fi
  for pid in "${AGENT_PIDS[@]}"; do
    if ps -p "${pid}" >/dev/null 2>&1; then
      kill "${pid}" >/dev/null 2>&1 || true
    fi
  done
  echo "Shutdown complete."
}

trap cleanup EXIT INT TERM

for agent_dir in "${AGENT_DIRS[@]}"; do
  AGENT_SCRIPT="${ROOT_DIR}/${agent_dir}/scripts/dev.sh"
  if [[ -f "${AGENT_SCRIPT}" ]]; then
    (cd "${ROOT_DIR}/${agent_dir}" && "${AGENT_SCRIPT}" "$@") &
  else
    (cd "${ROOT_DIR}/${agent_dir}" && npm run dev) &
  fi
  AGENT_PIDS+=($!)
done

echo "Supervisor and all agents started."
echo "Press Ctrl+C to shut down."

# Wait indefinitely until the script is interrupted
while true; do
  sleep 1
done
