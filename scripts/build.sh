#!/usr/bin/env bash

set -euo pipefail

ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}Supabolt multi-repo builder${NC}"

# Load .env if present
load_env() {
  if [[ -f "${ROOT_DIR}/.env" ]]; then
    set -a
    # shellcheck disable=SC1091
    source "${ROOT_DIR}/.env"
    set +a
  fi
}

# Determine which repositories to build.
resolve_targets() {
  local -a repo_targets=()
  local -a package_targets=()
  declare -A seen=()

  add_repo_target() {
    local candidate="$1"
    if [[ -d "${candidate}" && -f "${candidate}/package.json" ]]; then
      if [[ -z "${seen["${candidate}"]+x}" ]]; then
        repo_targets+=("${candidate}")
        seen["${candidate}"]=1
      fi
    fi
  }

  add_package_target() {
    local candidate="$1"
    if [[ -d "${candidate}" && -f "${candidate}/package.json" ]]; then
      if [[ -z "${seen["${candidate}"]+x}" ]]; then
        package_targets+=("${candidate}")
        seen["${candidate}"]=1
      fi
    fi
  }
  if [[ $# -gt 0 ]]; then
    for repo in "$@"; do
      if [[ -d "${ROOT_DIR}/${repo}" ]]; then
        add_repo_target "${ROOT_DIR}/${repo}"
      else
        echo -e "${YELLOW}Skipping unknown repository: ${repo}${NC}"
      fi
    done
  else
    if command -v jq >/dev/null 2>&1 && [[ -f "${ROOT_DIR}/AGENT_METADATA.json" ]]; then
      while IFS= read -r name; do
        add_repo_target "${ROOT_DIR}/${name}"
      done < <(jq -r 'keys[]' "${ROOT_DIR}/AGENT_METADATA.json")
    else
      for path in "${ROOT_DIR}"/*-agent; do
        add_repo_target "${path}"
      done
    fi
  fi

  # Always build shared packages first so agents see up-to-date artifacts.
  if [[ -d "${ROOT_DIR}/packages" ]]; then
    while IFS= read -r package_dir; do
      add_package_target "${package_dir}"
    done < <(find "${ROOT_DIR}/packages" -maxdepth 1 -mindepth 1 -type d)
  fi

  # Emit package targets first (sorted), followed by repo targets (sorted) while
  # preserving deduplication via the seen map.
  {
    printf '%s\n' "${package_targets[@]}" | sort
    printf '%s\n' "${repo_targets[@]}" | sort
  } | awk 'NF && !seen[$0]++ { seen[$0]=1; print $0 }'
}

load_env
mapfile -t TARGET_REPOS < <(resolve_targets "$@") || true
if [[ ${#TARGET_REPOS[@]} -eq 0 ]]; then
  echo -e "${YELLOW}No repositories matched the build criteria.${NC}"
  exit 0
fi

declare -a SUCCEEDED=()
declare -a FAILED=()
declare -a SKIPPED=()

echo -e "${GREEN}Preparing to build ${#TARGET_REPOS[@]} repositories...${NC}"

for repo_path in "${TARGET_REPOS[@]}"; do
  repo_name="$(basename "${repo_path}")"

  if [[ ! -f "${repo_path}/package.json" ]]; then
    echo -e "${YELLOW}[${repo_name}] No package.json found. Skipping.${NC}"
    SKIPPED+=("${repo_name}")
    continue
  fi

  echo -e "${GREEN}[${repo_name}] Building...${NC}"
  set +e
  (\
    cd "${repo_path}" && \
    npm install && \
    npm run clean --if-present >/dev/null 2>&1 || true && \
    rm -f tsconfig.tsbuildinfo && \
    npm run build --if-present
  )
  status=$?
  set -e

  if [[ ${status} -eq 0 ]]; then
    SUCCEEDED+=("${repo_name}")
  else
    echo -e "${RED}[${repo_name}] Build failed with status ${status}.${NC}"
    FAILED+=("${repo_name}")
  fi
  echo
done

echo -e "${GREEN}Build summary:${NC}"
if [[ ${#SUCCEEDED[@]} -gt 0 ]]; then
  printf '  %b %s\n' "${GREEN}✔${NC}" "${SUCCEEDED[*]}"
fi
if [[ ${#FAILED[@]} -gt 0 ]]; then
  printf '  %b %s\n' "${RED}✖${NC}" "${FAILED[*]}"
fi
if [[ ${#SKIPPED[@]} -gt 0 ]]; then
  printf '  %b %s\n' "${YELLOW}⚠${NC}" "${SKIPPED[*]}"
fi

if [[ ${#FAILED[@]} -gt 0 ]]; then
  exit 1
fi
