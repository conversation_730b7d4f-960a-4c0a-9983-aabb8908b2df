# Supabolt Architecture

## System Overview

Supabolt is a Bitcoin-native Compliance OS implementing the **Supervisor Architecture** pattern - a proven multi-agent system design where an intelligent supervisor orchestrates specialized worker agents using Unix philosophy principles.

## Architectural Pattern: Supervisor Architecture + Unix Philosophy

### Core Design Philosophy

1. **Do One Thing Well**: Each MCP agent masters a single domain
2. **Composable Design**: Complex workflows from simple, reliable components
3. **Standard Protocol**: MCP provides standardized communication
4. **Stateless Agents**: No customer data storage, immutable operations
5. **Intelligent Orchestration**: LangGraph StateGraph manages workflow complexity

### System Architecture Diagram

```mermaid
graph TD
    subgraph "Supervisor Layer"
        A[supabolt-supervisor<br/>Python LangGraph 1.x<br/>Intelligent Supervisor]
    end

    subgraph "Worker Agents (MCP Servers)"
        B[bitcoin-agent<br/>7 tools]
        C[lightning-agent<br/>10 tools]
        D[sanctions-agent<br/>4 tools]
        E[usdc-agent<br/>1 tool]
        F[analytics-agent<br/>6 tools]
        G[blockchain-agent<br/>2 tools]
        H[bitcoin-aws-agent<br/>5 tools]
        J[ofac-agent<br/>4 tools]
    end

    subgraph "Foundation Layer"
        I[@supabolt/mcp-core<br/>Shared MCP SDK Facade]
    end

    A -.->|MCP Protocol| B
    A -.->|MCP Protocol| C
    A -.->|MCP Protocol| D
    A -.->|MCP Protocol| E
    A -.->|MCP Protocol| F
    A -.->|MCP Protocol| G
    A -.->|MCP Protocol| H
    A -.->|MCP Protocol| K

    B --> I
    C --> I
    D --> I
    E --> I
    F --> I
    G --> I
    H --> I
    K --> I

    A --> J
```

## Component Architecture

### 1. Supervisor Layer

#### supabolt-supervisor (Python LangGraph 1.x)

- **Role**: Intelligent workflow orchestrator and supervisor for all MCP agents
- **Technology**: Python 3.11 + LangGraph 1.x workflow engine
- **Capabilities**:
  - Manifest-driven routing with deterministic Send fan-out plans
  - Concurrent agent execution with optional human-in-the-loop interruptions
  - Validation of tool outputs into FACTS ONLY schemas
  - REST API surface via FastAPI with Supabase-authenticated tenants
  - Redis Stack integration for optional checkpoint durability and audit trails

### 2. Worker Agent Layer (MCP Servers)

All agents built on `@supabolt/mcp-core` foundation following identical patterns:

#### Core Data Providers

- **bitcoin-agent** (7 tools): Pure Bitcoin blockchain data via Bitcoin Core RPC
- **lightning-agent** (10 tools): Lightning Network data via LND
- **usdc-agent** (1 tool): Multi-chain USDC balance tracking
- **blockchain-agent** (2 tools): Natural language blockchain queries via BitQuery

#### Specialized Services

- **sanctions-agent** (4 tools): Entity screening via OpenSanctions API
- **analytics-agent** (6 tools): Regulatory pattern detection and factual analysis
- **bitcoin-aws-agent** (5 tools): Enterprise Bitcoin data via AWS Managed Blockchain
- **ofac-agent** (4 tools): Direct OFAC sanctions list checking

### 3. Foundation Layer

#### @supabolt/mcp-core

- **Purpose**: Standardized MCP SDK facade for all agents
- **Components**: BaseTool, BaseContext, BaseServer patterns
- **Benefits**: Consistency, type safety, security patterns

## Data Flow Architecture

### Unix Pipe Pattern Implementation

```
## Data Flow Architecture

### Unix Pipe Pattern Implementation

```
User Query → Supabolt Supervisor → MCP Agent Selection → Data Retrieval → Fact Synthesis → Compliance Report
      ↓              ↓                       ↓                  ↓               ↓              ↓
  Natural Language  Intelligent Routing   Domain Expertise   Pure Data      Factual Analysis  Audit Trail
```

### Workflow Execution Patterns

1. **Sequential Pipeline**: Data flows through agents in order

   ```
   bitcoin-agent → sanctions-agent → analytics-agent → compliance report
   ```

2. **Parallel Execution**: Multiple agents called simultaneously

   ```
   ┌─ bitcoin-agent ──┐
   │                  ├─→ aggregation → report
   └─ sanctions-agent ┘
   ```

3. **Conditional Routing**: Agent selection based on findings
   ```
   analytics-agent → [if suspicious patterns] → deep investigation workflow
                   → [if clean] → standard compliance report
   ```

## Declarative Workflow Architecture (Manifest-Driven)

To support deterministic, auditable, and easily configurable workflows, the orchestrator implements a dual-mode execution model. Alongside the dynamic LLM-based supervisor, there is a manifest-driven path that executes workflows defined in declarative YAML files.

### Core Components

1.  **Workflow Manifests (`/manifests/*.yaml`)**: These YAML files define the structure of a workflow, including its stages, the tools to be called, and the conditional rules for routing between stages.

    ```yaml
    name: On-Chain Entity Trace
    initialState:
      entityId: string
      blockchain: string
    stages:
      - name: initial-screen
        concurrent:
          - agent: self
            tool: <workflow-tool-name>
            input:
              entityId: !state entityId
              blockchain: !state blockchain
          - agent: sanctions-agent
            tool: checkEntity
            input:
              entityId: !state entityId
    routing:
      - from: initial-screen
        to: synthesizer
        condition: "state.toolResults['sanctions-agent.checkEntity'].hasMatches == false"
      - from: initial-screen
        to: human-in-the-loop
        condition: "state.toolResults['sanctions-agent.checkEntity'].hasMatches == true"
    ```

2.  **Workflow Tools**: Following the pattern from the MCP playbook, the orchestrator can host its own higher-level "Workflow Tools". These tools encapsulate the logic for calling different agents based on input—for example, a dispatch tool could route blockchain traces to the appropriate agent based on the requested network.

3.  **Manifest-Driven Graph Nodes**: The `supervisor.ts` file contains a new set of nodes dedicated to this path:
    *   `entryPointRouter`: Directs the graph to the correct execution path (LLM vs. manifest) based on the invocation input.
    *   `manifestParserNode`: Loads and parses the specified YAML manifest file.
    *   `manifestExecutorNode`: Executes the tools for the current stage defined in the manifest, handling both `self` tools and external MCP agents.
    *   `manifestRouterNode`: Evaluates the `condition` from the manifest's routing rules to determine the next stage.

### Execution Flow

```mermaid
graph TD
    A[Start] --> B{entryPointRouter};
    B -- manifestName provided --> C[manifestParserNode];
    B -- no manifestName --> D[Legacy LLM Supervisor];
    C --> E[manifestExecutorNode];
    E --> F{manifestRouterNode};
    F -- condition met --> G[Next Stage Executor];
    F -- no condition met --> H[synthesizer];
    G --> F;
    H --> I[END];
    D --> I;
```

```

### Workflow Execution Patterns

1. **Sequential Pipeline**: Data flows through agents in order

   ```
   bitcoin-agent → sanctions-agent → analytics-agent → compliance report
   ```

2. **Parallel Execution**: Multiple agents called simultaneously

   ```
   ┌─ bitcoin-agent ──┐
   │                  ├─→ aggregation → report
   └─ sanctions-agent ┘
   ```

3. **Conditional Routing**: Agent selection based on findings
   ```
   analytics-agent → [if suspicious patterns] → deep investigation workflow
                   → [if clean] → standard compliance report
   ```

## Technology Stack

### Core Technologies

- **TypeScript 5.9+**: Strict mode, no `any` types, ES modules
- **Node.js 18+**: Modern JavaScript runtime with ES module support
- **MCP Protocol**: Model Context Protocol for agent communication
- **LangGraph**: Workflow orchestration and state management
- **Redis + LangGraph RedisSaver**: Persistent checkpoint store with TTL-based retention (managed via `make redis-test` and the supervisor's Poetry commands)

### External Integrations

- **Bitcoin Core**: Authoritative Bitcoin blockchain data
- **LND**: Lightning Network Daemon for Lightning data
- **OpenSanctions**: Global sanctions and watchlist data
- **AWS Managed Blockchain**: Enterprise blockchain query services
- **BitQuery**: Multi-blockchain GraphQL API

## Security Architecture

### Security Principles

1. **Input Validation**: Zod schemas for all external inputs
2. **Error Sanitization**: No sensitive data in error messages
3. **Secret Management**: Environment variables only, no hardcoding
4. **Audit Logging**: All operations logged for compliance
5. **Stateless Design**: No customer data persistence

### Error Handling Pattern

```typescript
try {
  // Operation
} catch (error) {
  const sanitized = context.sanitizeError(error);
  throw new McpError('OPERATION_FAILED', sanitized.message);
}
```

## FACTS ONLY Architecture

### Design Enforcement

The architecture physically prevents subjective assessments:

1. **Type System**: No risk score or assessment types in shared interfaces
2. **Tool Patterns**: BaseTool enforces factual return types only
3. **Agent Boundaries**: Data agents cannot access compliance logic
4. **Orchestrator Synthesis**: Only the supervisor combines data into reports

### Prohibited Patterns

- Risk scoring algorithms in data agents
- Subjective labels in tool responses
- Opinion-based categorizations
- Automated compliance decisions

## Deployment Architecture

### Development

```
┌─ Local Bitcoin Core ─┐    ┌─ MCP Agents ─┐    ┌─ Orchestrator ─┐
│ Port 8332 (RPC)      │────│ Port 3000+    │────│ LangGraph      │
└─ Regtest/Testnet     ┘    └─ Unix sockets  ┘    └─ HTTP API      ┘
```

### Production

```
┌─ Enterprise Bitcoin ─┐    ┌─ Agent Cluster ─┐    ┌─ Orchestrator ─┐
│ AWS/Cloud RPC        │────│ Load Balanced   │────│ Redis State    │
└─ High Availability   ┘    └─ Auto Scaling   ┘    └─ Audit Logs    ┘
```

### Checkpoint Persistence

- The compliance orchestrator uses the official `@langchain/langgraph-checkpoint-redis` saver to persist LangGraph state.
- Default retention is seven days (`REDIS_CHECKPOINT_TTL_MINUTES=10080`); adjust per environment without redeploying.
- `make redis-test` runs an automated probe (`scripts/test-redis-persistence.ts`) to confirm checkpoints are written to Redis.
- When Redis is unavailable, the supervisor automatically falls back to in-memory checkpoints for development workflows.

## Performance Characteristics

### Target Performance

- **Simple Screening**: <500ms end-to-end
- **Complex Investigation**: <5s multi-agent workflow
- **Throughput**: 1000+ workflows/minute
- **Availability**: Designed for high availability

### Scaling Strategy

- **Horizontal**: Multiple agent instances behind load balancer
- **Caching**: Redis for frequently accessed data
- **Parallelization**: LangGraph manages concurrent agent execution
- **Resource Management**: CPU and memory limits per agent

## Development Standards

### Code Organization

```
agent-name/
├── src/
│   ├── domain/          # Domain models and business logic
│   ├── application/     # Service layer (optional)
│   ├── infrastructure/  # External integrations
│   ├── mcp/            # MCP server implementation
│   │   ├── context.ts  # Dependency injection
│   │   └── tools/      # Tool implementations
│   └── server.ts       # Entry point
├── test/               # Test files
├── ARCHITECTURE.md     # Agent-specific architecture
├── CLAUDE.md          # Claude development guidance
└── README.md          # User documentation
```

### Network Safety Architecture

All agents require explicit network configuration to prevent accidental mainnet operations:

- **No Default Networks**: Agents fail to start without explicit BITCOIN_NETWORK or ETHEREUM_NETWORK
- **Network Consistency**: Orchestrator validates that Bitcoin and Lightning agents use the same network
- **Domain Ownership**: Each agent owns its network configuration entirely
- **Explicit Configuration**: Environment variables must be set deliberately
- **Cross-Agent Validation**: Only the orchestrator validates network consistency across agents

Example network configuration enforcement:

```typescript
// bitcoin-agent/src/domain/network.ts
export class NetworkConfig {
  static fromEnvironment(): NetworkConfig {
    const network = process.env.BITCOIN_NETWORK;
    if (!network) {
      throw new Error(
        'BITCOIN_NETWORK environment variable is REQUIRED. ' +
          'Set to "mainnet", "testnet", or "regtest" to explicitly configure the Bitcoin network.'
      );
    }
    return new NetworkConfig(network as BitcoinNetwork);
  }
}
```

### Package Management Strategy

All agents share a common foundation through `@supabolt/mcp-core`, which is published as a private npm package to GitHub Packages:

```json
"dependencies": {
  "@supabolt/mcp-core": "^1.0.7"
}
```

This architecture enables:

- **Independent Deployment**: Each agent can be deployed separately
- **Version Management**: Agents can pin to specific mcp-core versions
- **CI/CD Compatible**: Works with standard npm workflows
- **Docker Ready**: Can build proper Docker images
- **True Microservices**: Actual architectural independence

#### Authentication Setup

Agents authenticate to GitHub Packages via `.npmrc`:

```
@supabolt:registry=https://npm.pkg.github.com
//npm.pkg.github.com/:_authToken=${NPM_TOKEN}
```

Production deployments require setting `NPM_TOKEN` environment variable with a GitHub Personal Access Token that has `packages:read` scope.

### Quality Standards

- **Test Coverage**: Target comprehensive coverage for all production code
- **Type Safety**: Strict TypeScript, no `any` types
- **Security**: All errors sanitized, secrets externalized
- **Network Safety**: Explicit network configuration required
- **Documentation**: Architecture decisions documented
- **Unix Philosophy**: Single responsibility per agent

## Future Architecture Evolution

### Planned Enhancements

1. **Streaming Workflows**: Real-time data processing
2. **Agent Marketplace**: Plugin architecture for custom agents
3. **Multi-Region**: Global deployment with data locality
4. **ML Integration**: Pattern detection with machine learning
5. **Regulatory Modules**: Jurisdiction-specific compliance rules

### Architectural Principles to Maintain

- Unix philosophy (do one thing well)
- FACTS ONLY principle (no subjective assessments)
- MCP standardization across all agents
- Stateless design for scalability
- Security-first development practices

This architecture provides a solid foundation for building production-ready compliance workflows while maintaining the flexibility to evolve with changing regulatory requirements and technical innovations.
