# GEMINI.md

**Primary Documentation**: Read `AGENTS.md` in this directory for complete project context.

## Gemini CLI Specific Instructions

### Session Start
Read `AGENTS.md` for agent architecture, development workflow, and code standards.

### Project Type
- TypeScript 5.9+ (strict mode, ES2022)
- Node.js 18+
- Monorepo with 13 specialist agents + 1 orchestrator

### Key Principles
- FACTS ONLY (no risk scores or opinions)
- Unix philosophy (each agent does ONE thing well)
- Stateless agents, stateful orchestrator
- MCP protocol for agent communication

---

**Read AGENTS.md for complete project context.**
