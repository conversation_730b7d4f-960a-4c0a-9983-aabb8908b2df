# Supabolt Agent Architecture

**Single source of truth for AI assistants** - All AI tools (<PERSON>, <PERSON>, <PERSON>, Cursor) read this file for project context.

## Product Overview

Supabolt is a Bitcoin-native Compliance OS implementing **Unix philosophy** for digital asset compliance. We transform complex requirements into composable workflows through specialized agents that each do ONE thing well.

**Architecture**: Supervisor pattern with the Python supabolt-supervisor + specialized MCP agents

## Agent Matrix (13 Agents)

| Agent | Tools | Domain | Detailed Docs |
|-------|-------|--------|---------------|
| supabolt-supervisor | REST API | LangGraph-inspired workflow orchestration (Python) | `supabolt-supervisor/README.md` |
| bitcoin-agent | 7 | Bitcoin blockchain data via Bitcoin Core RPC | `bitcoin-agent/AGENTS.md` |
| lightning-agent | 11 | Lightning Network data via LND | `lightning-agent/AGENTS.md` |
| sanctions-agent | 4 | Entity screening via OpenSanctions API | `sanctions-agent/AGENTS.md` |
| usdc-agent | 1 | Multi-chain USDC/stablecoin balance tracking | `usdc-agent/AGENTS.md` |
| analytics-agent | 6 | Regulatory pattern detection (FATF/MiCA/FinCEN) | `analytics-agent/AGENTS.md` |
| blockchain-agent | 2 | Natural language blockchain queries via BitQuery | `blockchain-agent/AGENTS.md` |
| bitcoin-aws-agent | 5 | Enterprise Bitcoin data via AWS Managed Blockchain | `bitcoin-aws-agent/AGENTS.md` |
| ofac-agent | 4 | Direct OFAC sanctions list checking | `ofac-agent/AGENTS.md` |
| spark-agent | 2 | Spark shared-custody network telemetry | `spark-agent/AGENTS.md` |
| internal-ai-agent | 7 | Internal team personas (NOT customer-facing) | `internal-ai-agent/AGENTS.md` |
| synthesis-agent | - | Compliance fact sheet compiler (design phase) | `synthesis-agent/AGENTS.md` |
| packages/mcp-core | - | Shared MCP foundation library | `packages/mcp-core/AGENTS.md` |

> **Note:** The legacy `compliance-orchestrator` TypeScript project is deprecated and replaced by the
> Python-based `supabolt-supervisor`. References to the old orchestrator remain in historical docs only.
**For tool schemas, setup instructions, and architecture details**: Read the individual agent AGENTS.md files.

## Core Principles

### Unix Philosophy for Compliance
1. **Do One Thing Well**: Each agent masters single domain
2. **Expect Composition**: LangGraph orchestrates agents into workflows
3. **Standard Protocol**: MCP communication, LangGraph pipeline management
4. **Stateless Filters**: Agents transform data, don't store state
5. **Fail Fast**: Clear validation at boundaries

### Supervisor Architecture
```
User Query → LangGraph StateGraph → Agent Selection → Data Retrieval → Synthesis → Report
```

- **LangGraph Layer**: Manages state, routes intelligently, handles execution patterns
- **MCP Agent Layer**: Domain expertise, stateless tools, single responsibility
- **Integration**: MultiServerMCPClient bridges LangGraph to MCP servers

### Workflow Patterns
- **Sequential**: bitcoin-agent → sanctions-agent → analytics-agent
- **Parallel**: Multiple agents called simultaneously
- **Conditional**: Route based on findings (if pattern detected → deep investigation)

## FACTS ONLY Principle (CRITICAL)

**THE NON-NEGOTIABLE CORE REQUIREMENT**

Agents MUST return ONLY:
- Factual observations: "transaction velocity: 45/hour", "address age: 2 days"
- Regulatory pattern matches: "matches FATF structuring pattern"
- Objective measurements: "300% volume increase in 1 hour"

Agents MUST NEVER return:
- Risk scores, subjective assessments, arbitrary ratings, opinions, generated metrics

**Why**: Scores create liability. Facts create defensible compliance. Risk assessment is the compliance officer's responsibility, not the tools.

**Architecture enforcement**: Type system prohibits risk scores, BaseTool enforces factual returns, agents have no access to compliance logic.

### Verification Discipline
- Confirm behavior against the live code path (or other authoritative source) before describing it to users. If verification isn't possible, clearly state the uncertainty instead of presenting assumptions as facts.

### Evidence & Trace Requirements
- Output Contract (CRITICAL): All agents that emit case results MUST set outputType = EvidenceReport[vX.Y]. Any non-conforming output triggers a guardrail tripwire and returns a 422.
- Trace Correlation (CRITICAL): case_id MUST equal trace_id. Each Fact.payload.audit MUST include { trace_id, tool, source, version }.
- Handoff Reasons: Every handoff MUST include escalation_reason and handoff_policy_id for audit comparability.
- Idempotency: Tools MUST accept idempotency_key; duplicate keys MUST be no-ops returning the original Fact set.

## Development Workflow

### System-Wide Commands (Makefile)
```bash
make install              # Install dependencies across agents + supabolt-supervisor
make build                # Build all TypeScript agents
make test                 # Run agent Vitest suites and supervisor pytest suite
make up / make down       # Start/stop Docker services (Redis, LocalStack)
make audit                # Run npm audit and supervisor pip-audit
make redis-test           # Validate Redis Stack modules for supervisor
make supervisor-serve     # Launch the supabolt-supervisor REST API locally
```

### Per-Agent Development
```bash
cd {agent-name}/
npm run dev               # Development mode with hot reload
npm start                 # Production mode
npm test                  # Run tests
npm run typecheck         # TypeScript type checking
npm run lint              # Run linter
```

## Code Standards

### Critical Rules
- **TypeScript**: Strict mode, no `any`, explicit return types
- **Formatting**: 2-space indentation, single quotes, semicolons
- **Naming**: PascalCase (classes), camelCase (functions), kebab-case (files)
- **No Emojis**: Professional tone required
- **No Unsubstantiated Claims**: Never state metrics without data

### Nullish Coalescing (CRITICAL FOR COMPLIANCE DATA)
**ALWAYS use `??` not `||`** - zero, empty string, false are VALID values!

```typescript
// CORRECT: Only defaults when null/undefined
const balance = account.balance ?? 0;
const network = request.network ?? 'bitcoin';

// WRONG: Treats 0, "", false as "missing"
const balance = account.balance || 0;  // Balance of 0 BTC is VALID!
```

**Why**: Zero balances, empty addresses, false flags are legitimate compliance data.

### MCP Foundation
ALL agents import through `@supabolt/mcp-core` facade:
```typescript
// CORRECT
import { BaseTool, McpServer } from '@supabolt/mcp-core';

// WRONG: Direct MCP SDK imports cause version conflicts
import { BaseTool } from '@modelcontextprotocol/sdk/...';
```

### Security & Logging
- Zod schemas for all input validation
- `context.sanitizeError()` for all error handling
- `context.getLogger()` for logging - NEVER `console.log`
- Environment variables only for secrets

## Technology Stack

- **TypeScript 5.9+**: Strict mode, ES modules
- **Node.js 18+**: Modern runtime
- **LangGraph**: Workflow orchestration, state management
- **MCP Protocol**: Agent communication standard
- **Redis**: Checkpoint persistence (7-day TTL)
- **External**: Bitcoin Core, LND, OpenSanctions, AWS Managed Blockchain, BitQuery

## Lightning Network Limitations

**What we CAN see**:
- Channel opens/closes (on-chain Bitcoin transactions)
- Channel capacity, node public keys, peer connections
- Funding sources (Bitcoin addresses)

**What we CANNOT see** (by design):
- Payment routing (onion routing ensures privacy)
- Channel balances (private)
- Invoice details (unless we're endpoint)

This is intentional - Lightning provides payment privacy while maintaining on-chain auditability.

## Project Boundaries

- **Single Responsibility**: Each agent has bounded context
- **No Cross-Boundary Knowledge**: Bitcoin agent doesn't know sanctions agent exists
- **Supervisor Exception**: Only supabolt-supervisor has a broad system view
- **Mission Critical**: Securing large amounts of Bitcoin and stablecoins - use surgical precision

## Repository Structure

```
Root Level:
- AGENTS.md (this file)    → AI assistant context (all tools)
- ARCHITECTURE.md          → Technical architecture deep dive (humans)
- CLAUDE.md                → Claude Code specific instructions
- GEMINI.md                → Gemini CLI specific instructions
- README.md                → User-facing documentation

Agent Level:
- {agent}/AGENTS.md        → Tool schemas, setup, architecture
- {agent}/CLAUDE.md        → Claude-specific notes (optional)
- {agent}/README.md        → User documentation
```

## Testing & Quality

- **Framework**: Vitest across all packages
- **Location**: Tests colocated with source (`*.test.ts`)
- **Coverage**: `npm run test:coverage` before releases
- **Integration**: Exercise supabolt-supervisor with cross-agent flows
- **Network Safety**: All agents require explicit BITCOIN_NETWORK/ETHEREUM_NETWORK (no defaults)

## Commit Standards

- **Format**: Conventional Commits (`feat(agent):`, `fix(supervisor):`, `docs:`)
- **Scope**: Primary agent or package affected
- **PRs**: Must include user impact, issue references, CLI output, passing tests

---

**This is mission-critical infrastructure. Use surgical precision. Respect bounded context. FACTS ONLY is non-negotiable.**

**For detailed information**: Read `ARCHITECTURE.md` (technical), individual `{agent}/AGENTS.md` (agent details), tool-specific AI guidance in `CLAUDE.md`/`GEMINI.md`.
