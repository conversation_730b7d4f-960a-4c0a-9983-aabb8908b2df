# Supabolt Supervisor

Supabolt Supervisor is Supabolt's mission-critical compliance workflow runner. It orchestrates the
TypeScript MCP agents via a hardened Python 3.11 control plane that:

- launches each MCP server as a subprocess over stdio using the `MCPProcessClient` stdio adapter;
- executes manifest-driven fan-out routing with LangGraph `Send[]` fan-out and deterministic joins;
- enforces the FACTS ONLY schema through Pydantic v2 models and canonical fact-sheet generation;
- surfaces human-in-the-loop checkpoints via native LangGraph interrupts (no risk scores);
- exposes an audited FastAPI interface guarded by Supabase JWTs and Prometheus metrics.

## Getting Started

```bash
cp .env.example .env
poetry install  # installs supervisor + test dependencies
poetry run uvicorn api.server:app --reload
```

### Supabase Auth (local development)

The supervisor validates every `/workflows` request with a Supabase-style HS256 JWT. For local testing
you control both sides:

1. Set `SUPABASE_JWT_SECRET` in `.env` (the supervisor reads it at startup).
2. Generate a bearer token using the same secret (example with Poetry and `python-jose`):

```bash
cd supabolt-supervisor
poetry run python - <<'PY'
from jose import jwt

secret = "dev-secret"  # match SUPABASE_JWT_SECRET
payload = {
    "sub": "tenant-demo",  # tenant identifier returned by require_tenant()
    "iss": "local",
    "iat": 0,
    "exp": 4102444800,  # far-future expiry for dev only
}
print(jwt.encode(payload, secret, algorithm="HS256"))
PY
```

3. Export the token before invoking the API:

```bash
export SUPABASE_TENANT_JWT=<token-from-step-2>
curl -X POST http://localhost:8080/workflows \
  -H "Authorization: Bearer $SUPABASE_TENANT_JWT" \
  -H "Content-Type: application/json" \
  -d '{ "workflow_id": "demo-001", "entity": {"type":"address","value":"tb1q..."}, "runtime_config": {}, "messages": [] }'
```

`SUPABASE_JWT_SECRET` stays in your environment/server, while `SUPABASE_TENANT_JWT` is the per-request
bearer token you send to the supervisor.

## Key Features

- **Manifest-driven routing** – `manifests/toolsets.yaml` maps toolsets to agents/tools and powers the
  routing function (returns `Send[]` for dynamic fan-out) built by `RouterConfig`.
- **Custom MCP stdio client** – the supervisor embeds a Python JSON-RPC client (`MCPProcessClient`) so
  it can spawn Supabolt's TypeScript agents without external SDK assumptions.
- **Parallel fan-out + HITL** – a LangGraph 1.x `StateGraph` fans out to `agent__<name>` nodes, joins
  their deltas, and raises a native interrupt whenever an agent flags human review.
- **FACTS ONLY enforcement** – every agent response is validated into `FactualObservation`/
  `RegulatoryPattern` models before it reaches downstream consumers.
- **FastAPI + Prometheus** – the `/workflows` endpoint returns canonical fact sheets, `/metrics` exposes
  Prometheus counters (including `supabolt_interrupts_total` and `supabolt_router_fanout_size`), and
  Supabase JWTs protect multi-tenant access.

## Infrastructure Requirements

- Redis Stack (`redis/redis-stack-server`) with RedisJSON and RediSearch modules enabled (the API startup
  hook validates module availability).
- Supabase Postgres/Auth for tenant authentication.
- Node.js 18+ for the TypeScript MCP agents specified in `manifests/mcp/servers.yaml`.
- Optional: LangSmith for tracing and Redis persistence (install `langgraph[redis]` when you want
  checkpoint durability).

Refer to `docs/adr` for design records, `manifests/` for routing definitions, and `tests/` for the
golden-dataset harness that validates multi-agent orchestration.
