.PHONY: setup lint typecheck test redis-test serve audit

setup:
	poetry install

lint:
	poetry run ruff check .
	poetry run black --check .

typecheck:
	poetry run mypy supabolt_supervisor api auth

test:
	poetry run pytest

redis-test:
	poetry run python scripts/check_redis_modules.py

serve:
	poetry run uvicorn api.server:app --host 0.0.0.0 --port 8080 --env-file ../.env

audit:
	poetry run pip-audit
