{".class": "MypyFile", "_fullname": "redis", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AuthenticationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.AuthenticationError", "line": 54, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.exceptions.AuthenticationError"}}, "AuthenticationWrongNumberOfArgsError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.AuthenticationWrongNumberOfArgsError", "line": 55, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.exceptions.AuthenticationWrongNumberOfArgsError"}}, "BlockingConnectionPool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.BlockingConnectionPool", "line": 40, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.connection.BlockingConnectionPool"}}, "BusyLoadingError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.BusyLoadingError", "line": 56, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.exceptions.BusyLoadingError"}}, "ChildDeadlockedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.ChildDeadlockedError", "line": 57, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.exceptions.ChildDeadlockedError"}}, "Connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.Connection", "line": 41, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.connection.Connection"}}, "ConnectionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.ConnectionError", "line": 58, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.exceptions.ConnectionError"}}, "ConnectionPool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.ConnectionPool", "line": 42, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.connection.ConnectionPool"}}, "CredentialProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.CredentialProvider", "line": 68, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.credentials.CredentialProvider"}}, "DataError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.DataError", "line": 59, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.exceptions.DataError"}}, "InvalidResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.InvalidResponse", "line": 60, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.exceptions.InvalidResponse"}}, "PubSubError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.PubSubError", "line": 61, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.exceptions.PubSubError"}}, "ReadOnlyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.ReadOnlyError", "line": 62, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.exceptions.ReadOnlyError"}}, "Redis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.Redis", "line": 38, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "redis.client.Redis"}}}, "RedisCluster": {".class": "SymbolTableNode", "cross_ref": "redis.cluster.RedisCluster", "kind": "Gdef"}, "RedisError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.RedisError", "line": 63, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.exceptions.RedisError"}}, "ResponseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.ResponseError", "line": 64, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.exceptions.ResponseError"}}, "SSLConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.SSLConnection", "line": 43, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.connection.SSLConnection"}}, "Sentinel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.Sentinel", "line": 49, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.sentinel.Sentinel"}}, "SentinelConnectionPool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.SentinelConnectionPool", "line": 50, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.sentinel.SentinelConnectionPool"}}, "SentinelManagedConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.SentinelManagedConnection", "line": 51, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.sentinel.SentinelManagedConnection"}}, "SentinelManagedSSLConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.SentinelManagedSSLConnection", "line": 52, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.sentinel.SentinelManagedSSLConnection"}}, "StrictRedis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.StrictRedis", "line": 44, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "redis.client.Redis"}}}, "TimeoutError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.TimeoutError", "line": 65, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.exceptions.TimeoutError"}}, "UnixDomainSocketConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.UnixDomainSocketConnection", "line": 45, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.connection.UnixDomainSocketConnection"}}, "UsernamePasswordCredentialProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.UsernamePasswordCredentialProvider", "line": 69, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.credentials.UsernamePasswordCredentialProvider"}}, "VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.VERSION", "name": "VERSION", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "WatchError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.WatchError", "line": 66, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "redis.exceptions.WatchError"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "redis.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "backoff": {".class": "SymbolTableNode", "cross_ref": "redis.backoff", "kind": "Gdef", "module_public": false}, "client": {".class": "SymbolTableNode", "cross_ref": "redis.client", "kind": "Gdef", "module_public": false}, "connection": {".class": "SymbolTableNode", "cross_ref": "redis.connection", "kind": "Gdef", "module_public": false}, "credentials": {".class": "SymbolTableNode", "cross_ref": "redis.credentials", "kind": "Gdef", "module_public": false}, "default_backoff": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "redis.default_backoff", "name": "default_backoff", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "redis.backoff.Equal<PERSON>itte<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exceptions": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions", "kind": "Gdef", "module_public": false}, "from_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "redis.from_url", "name": "from_url", "setter_type": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5, 3, 4], "arg_names": ["url", "db", "decode_responses", "kwargs"], "arg_types": ["builtins.str", "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "redis.client.Redis"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["url", "db", "decode_responses", "kwargs"], "arg_types": ["builtins.str", "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "redis.client.Redis"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "sentinel": {".class": "SymbolTableNode", "cross_ref": "redis.sentinel", "kind": "Gdef", "module_public": false}, "utils": {".class": "SymbolTableNode", "cross_ref": "redis.utils", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/redis-stubs/__init__.pyi"}