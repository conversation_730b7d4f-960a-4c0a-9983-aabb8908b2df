{".class": "MypyFile", "_fullname": "redis.commands.sentinel", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AsyncSentinelCommands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.commands.sentinel.SentinelCommands"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.sentinel.AsyncSentinelCommands", "name": "AsyncSentinelCommands", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.AsyncSentinelCommands", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.sentinel", "mro": ["redis.commands.sentinel.AsyncSentinelCommands", "redis.commands.sentinel.SentinelCommands", "builtins.object"], "names": {".class": "SymbolTable", "sentinel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.commands.sentinel.AsyncSentinelCommands.sentinel", "name": "sentinel", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["redis.commands.sentinel.AsyncSentinelCommands", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sentinel of AsyncSentinelCommands", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.sentinel.AsyncSentinelCommands.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.sentinel.AsyncSentinelCommands", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SentinelCommands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.sentinel.SentinelCommands", "name": "SentinelCommands", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.sentinel", "mro": ["redis.commands.sentinel.SentinelCommands", "builtins.object"], "names": {".class": "SymbolTable", "sentinel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands.sentinel", "name": "sentinel", "original_first_arg": "self", "type": null}}, "sentinel_ckquorum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_master_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands.sentinel_ckquorum", "name": "sentinel_ckquorum", "original_first_arg": "self", "type": null}}, "sentinel_failover": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_master_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands.sentinel_failover", "name": "sentinel_failover", "original_first_arg": "self", "type": null}}, "sentinel_flushconfig": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands.sentinel_flushconfig", "name": "sentinel_flushconfig", "original_first_arg": "self", "type": null}}, "sentinel_get_master_addr_by_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "service_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands.sentinel_get_master_addr_by_name", "name": "sentinel_get_master_addr_by_name", "original_first_arg": "self", "type": null}}, "sentinel_master": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "service_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands.sentinel_master", "name": "sentinel_master", "original_first_arg": "self", "type": null}}, "sentinel_masters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands.sentinel_masters", "name": "sentinel_masters", "original_first_arg": "self", "type": null}}, "sentinel_monitor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "name", "ip", "port", "quorum"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands.sentinel_monitor", "name": "sentinel_monitor", "original_first_arg": "self", "type": null}}, "sentinel_remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands.sentinel_remove", "name": "sentinel_remove", "original_first_arg": "self", "type": null}}, "sentinel_reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands.sentinel_reset", "name": "sentinel_reset", "original_first_arg": "self", "type": null}}, "sentinel_sentinels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "service_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands.sentinel_sentinels", "name": "sentinel_sentinels", "original_first_arg": "self", "type": null}}, "sentinel_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "option", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands.sentinel_set", "name": "sentinel_set", "original_first_arg": "self", "type": null}}, "sentinel_slaves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "service_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.sentinel.SentinelCommands.sentinel_slaves", "name": "sentinel_slaves", "original_first_arg": "self", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.sentinel.SentinelCommands.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.sentinel.SentinelCommands", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.sentinel.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.sentinel.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.sentinel.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.sentinel.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.sentinel.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.sentinel.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/redis-stubs/commands/sentinel.pyi"}