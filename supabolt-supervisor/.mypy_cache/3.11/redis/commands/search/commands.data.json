{".class": "MypyFile", "_fullname": "redis.commands.search.commands", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ADDHASH_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.ADDHASH_CMD", "name": "ADDHASH_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.ADDHASH"}}}, "ADD_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.ADD_CMD", "name": "ADD_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.ADD"}}}, "AGGREGATE_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.AGGREGATE_CMD", "name": "AGGREGATE_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.AGGREGATE"}}}, "ALIAS_ADD_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.ALIAS_ADD_CMD", "name": "ALIAS_ADD_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.ALIASADD"}}}, "ALIAS_DEL_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.ALIAS_DEL_CMD", "name": "ALIAS_DEL_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.ALIASDEL"}}}, "ALIAS_UPDATE_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.ALIAS_UPDATE_CMD", "name": "ALIAS_UPDATE_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.ALIASUPDATE"}}}, "ALTER_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.ALTER_CMD", "name": "ALTER_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.ALTER"}}}, "AggregateRequest": {".class": "SymbolTableNode", "cross_ref": "redis.commands.search.aggregation.AggregateRequest", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AggregateResult": {".class": "SymbolTableNode", "cross_ref": "redis.commands.search.aggregation.AggregateResult", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CONFIG_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.CONFIG_CMD", "name": "CONFIG_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.CONFIG"}}}, "CREATE_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.CREATE_CMD", "name": "CREATE_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.CREATE"}}}, "CURSOR_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.CURSOR_CMD", "name": "CURSOR_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.CURSOR"}}}, "Cursor": {".class": "SymbolTableNode", "cross_ref": "redis.commands.search.aggregation.Cursor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DEL_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.DEL_CMD", "name": "DEL_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.DEL"}}}, "DICT_ADD_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.DICT_ADD_CMD", "name": "DICT_ADD_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.DICTADD"}}}, "DICT_DEL_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.DICT_DEL_CMD", "name": "DICT_DEL_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.DICTDEL"}}}, "DICT_DUMP_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.DICT_DUMP_CMD", "name": "DICT_DUMP_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.DICTDUMP"}}}, "DROP_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.DROP_CMD", "name": "DROP_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.DROP"}}}, "EXPLAINCLI_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.EXPLAINCLI_CMD", "name": "EXPLAINCLI_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.EXPLAINCLI"}}}, "EXPLAIN_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.EXPLAIN_CMD", "name": "EXPLAIN_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.EXPLAIN"}}}, "FUZZY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.FUZZY", "name": "FUZZY", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FUZZY"}}}, "GET_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.GET_CMD", "name": "GET_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.GET"}}}, "INFO_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.INFO_CMD", "name": "INFO_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.INFO"}}}, "Incomplete": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Incomplete", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MGET_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.MGET_CMD", "name": "MGET_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.MGET"}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NOFIELDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.NOFIELDS", "name": "NOFIELDS", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "NOFIELDS"}}}, "NOOFFSETS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.NOOFFSETS", "name": "NOOFFSETS", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "NOOFFSETS"}}}, "NUMERIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.NUMERIC", "name": "NUMERIC", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "NUMERIC"}}}, "PROFILE_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.PROFILE_CMD", "name": "PROFILE_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.PROFILE"}}}, "Query": {".class": "SymbolTableNode", "cross_ref": "redis.commands.search.query.Query", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Result": {".class": "SymbolTableNode", "cross_ref": "redis.commands.search.result.Result", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SEARCH_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.SEARCH_CMD", "name": "SEARCH_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.SEARCH"}}}, "SPELLCHECK_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.SPELLCHECK_CMD", "name": "SPELLCHECK_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.SPELLCHECK"}}}, "STOPWORDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.STOPWORDS", "name": "STOPWORDS", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "STOPWORDS"}}}, "SUGADD_COMMAND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.SUGADD_COMMAND", "name": "SUGADD_COMMAND", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.SUGADD"}}}, "SUGDEL_COMMAND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.SUGDEL_COMMAND", "name": "SUGDEL_COMMAND", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.SUGDEL"}}}, "SUGGET_COMMAND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.SUGGET_COMMAND", "name": "SUGGET_COMMAND", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.SUGGET"}}}, "SUGLEN_COMMAND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.SUGLEN_COMMAND", "name": "SUGLEN_COMMAND", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.SUGLEN"}}}, "SYNDUMP_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.SYNDUMP_CMD", "name": "SYNDUMP_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.SYNDUMP"}}}, "SYNUPDATE_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.SYNUPDATE_CMD", "name": "SYNUPDATE_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.SYNUPDATE"}}}, "SearchCommands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.search.commands.SearchCommands", "name": "SearchCommands", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.search.commands", "mro": ["redis.commands.search.commands.SearchCommands", "builtins.object"], "names": {".class": "SymbolTable", "add_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "doc_id", "nosave", "score", "payload", "replace", "partial", "language", "no_create", "fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.add_document", "name": "add_document", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "doc_id", "nosave", "score", "payload", "replace", "partial", "language", "no_create", "fields"], "arg_types": ["redis.commands.search.commands.SearchCommands", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_document of SearchCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_document_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "doc_id", "score", "language", "replace"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.add_document_hash", "name": "add_document_hash", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "doc_id", "score", "language", "replace"], "arg_types": ["redis.commands.search.commands.SearchCommands", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_document_hash of SearchCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aggregate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "query", "query_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.aggregate", "name": "aggregate", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "query", "query_params"], "arg_types": ["redis.commands.search.commands.SearchCommands", {".class": "UnionType", "items": ["redis.commands.search.aggregation.AggregateRequest", "redis.commands.search.aggregation.Cursor"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.search.commands._QueryParams"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of SearchCommands", "ret_type": "redis.commands.search.aggregation.AggregateResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aliasadd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.aliasadd", "name": "<PERSON><PERSON><PERSON>", "original_first_arg": "self", "type": null}}, "aliasdel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.aliasdel", "name": "alias<PERSON>", "original_first_arg": "self", "type": null}}, "aliasupdate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.aliasupdate", "name": "aliasupdate", "original_first_arg": "self", "type": null}}, "alter_schema_add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.alter_schema_add", "name": "alter_schema_add", "original_first_arg": "self", "type": null}}, "batch_indexer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.batch_indexer", "name": "batch_indexer", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "chunk_size"], "arg_types": ["redis.commands.search.commands.SearchCommands", "builtins.int"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batch_indexer of SearchCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config_get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "option"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.config_get", "name": "config_get", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option"], "arg_types": ["redis.commands.search.commands.SearchCommands", "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "config_get of SearchCommands", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "option", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.config_set", "name": "config_set", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "option", "value"], "arg_types": ["redis.commands.search.commands.SearchCommands", "builtins.str", "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "config_set of SearchCommands", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "fields", "no_term_offsets", "no_field_flags", "stopwords", "definition", "max_text_fields", "temporary", "no_highlight", "no_term_frequencies", "skip_initial_scan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.create_index", "name": "create_index", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "fields", "no_term_offsets", "no_field_flags", "stopwords", "definition", "max_text_fields", "temporary", "no_highlight", "no_term_frequencies", "skip_initial_scan"], "arg_types": ["redis.commands.search.commands.SearchCommands", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index of SearchCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "doc_id", "conn", "delete_actual_document"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.delete_document", "name": "delete_document", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "doc_id", "conn", "delete_actual_document"], "arg_types": ["redis.commands.search.commands.SearchCommands", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_document of SearchCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dict_add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "name", "terms"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.dict_add", "name": "dict_add", "original_first_arg": "self", "type": null}}, "dict_del": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "name", "terms"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.dict_del", "name": "dict_del", "original_first_arg": "self", "type": null}}, "dict_dump": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.dict_dump", "name": "dict_dump", "original_first_arg": "self", "type": null}}, "dropindex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "delete_documents"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.dropindex", "name": "dropindex", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "delete_documents"], "arg_types": ["redis.commands.search.commands.SearchCommands", "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dropindex of SearchCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "explain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "query", "query_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.explain", "name": "explain", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "query", "query_params"], "arg_types": ["redis.commands.search.commands.SearchCommands", {".class": "UnionType", "items": ["builtins.str", "redis.commands.search.query.Query"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.search.commands._QueryParams"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "explain of SearchCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "explain_cli": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.explain_cli", "name": "explain_cli", "original_first_arg": "self", "type": null}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.get", "name": "get", "original_first_arg": "self", "type": null}}, "get_params_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.get_params_args", "name": "get_params_args", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query_params"], "arg_types": ["redis.commands.search.commands.SearchCommands", {".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.search.commands._QueryParams"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_params_args of SearchCommands", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.info", "name": "info", "original_first_arg": "self", "type": null}}, "load_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.load_document", "name": "load_document", "original_first_arg": "self", "type": null}}, "profile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "query", "limited", "query_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.profile", "name": "profile", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "query", "limited", "query_params"], "arg_types": ["redis.commands.search.commands.SearchCommands", {".class": "UnionType", "items": ["builtins.str", "redis.commands.search.query.Query", "redis.commands.search.aggregation.AggregateRequest"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "profile of SearchCommands", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "query", "query_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.search", "name": "search", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "query", "query_params"], "arg_types": ["redis.commands.search.commands.SearchCommands", {".class": "UnionType", "items": ["builtins.str", "redis.commands.search.query.Query"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.commands.search.commands._QueryParams"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "search of SearchCommands", "ret_type": "redis.commands.search.result.Result", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "spellcheck": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "query", "distance", "include", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.spellcheck", "name": "spellcheck", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "query", "distance", "include", "exclude"], "arg_types": ["redis.commands.search.commands.SearchCommands", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "spellcheck of SearchCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sugadd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "key", "suggestions", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.sugadd", "name": "suga<PERSON>", "original_first_arg": "self", "type": null}}, "sugdel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.sugdel", "name": "sugdel", "original_first_arg": "self", "type": null}}, "sugget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "key", "prefix", "fuzzy", "num", "with_scores", "with_payloads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.sugget", "name": "sugget", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "key", "prefix", "fuzzy", "num", "with_scores", "with_payloads"], "arg_types": ["redis.commands.search.commands.SearchCommands", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.int", "builtins.bool", "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sugget of SearchCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "suglen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.suglen", "name": "suglen", "original_first_arg": "self", "type": null}}, "syndump": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.syndump", "name": "syndump", "original_first_arg": "self", "type": null}}, "synupdate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 2], "arg_names": ["self", "groupid", "skipinitial", "terms"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.commands.SearchCommands.synupdate", "name": "synupdate", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 2], "arg_names": ["self", "groupid", "skipinitial", "terms"], "arg_types": ["redis.commands.search.commands.SearchCommands", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "synupdate of SearchCommands", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tagvals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tagfield"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.commands.SearchCommands.tagvals", "name": "<PERSON><PERSON>s", "original_first_arg": "self", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.search.commands.SearchCommands.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.search.commands.SearchCommands", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TAGVALS_CMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.TAGVALS_CMD", "name": "TAGVALS_CMD", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "FT.TAGVALS"}}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WITHPAYLOADS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.WITHPAYLOADS", "name": "WITHPAYLOADS", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "WITHPAYLOADS"}}}, "WITHSCORES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.WITHSCORES", "name": "WITHSCORES", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "WITHSCORES"}}}, "_QueryParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "redis.commands.search.commands._QueryParams", "line": 10, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.commands.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/redis-stubs/commands/search/commands.pyi"}