{".class": "MypyFile", "_fullname": "redis.commands.search.aggregation", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AggregateRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.search.aggregation.AggregateRequest", "name": "AggregateRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.AggregateRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.search.aggregation", "mro": ["redis.commands.search.aggregation.AggregateRequest", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.aggregation.AggregateRequest.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "query"], "arg_types": ["redis.commands.search.aggregation.AggregateRequest", "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AggregateRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwexpr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.AggregateRequest.apply", "name": "apply", "original_first_arg": "self", "type": null}}, "build_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.AggregateRequest.build_args", "name": "build_args", "original_first_arg": "self", "type": null}}, "cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "count", "max_idle"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.aggregation.AggregateRequest.cursor", "name": "cursor", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "count", "max_idle"], "arg_types": ["redis.commands.search.aggregation.AggregateRequest", "builtins.int", "builtins.float"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cursor of AggregateRequest", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expressions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.AggregateRequest.filter", "name": "filter", "original_first_arg": "self", "type": null}}, "group_by": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "fields", "reducers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.AggregateRequest.group_by", "name": "group_by", "original_first_arg": "self", "type": null}}, "limit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "offset", "num"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.AggregateRequest.limit", "name": "limit", "original_first_arg": "self", "type": null}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.AggregateRequest.load", "name": "load", "original_first_arg": "self", "type": null}}, "sort_by": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "fields", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.AggregateRequest.sort_by", "name": "sort_by", "original_first_arg": "self", "type": null}}, "verbatim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.AggregateRequest.verbatim", "name": "verbatim", "original_first_arg": "self", "type": null}}, "with_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.AggregateRequest.with_schema", "name": "with_schema", "original_first_arg": "self", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.search.aggregation.AggregateRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.search.aggregation.AggregateRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AggregateResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.search.aggregation.AggregateResult", "name": "AggregateResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.AggregateResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.search.aggregation", "mro": ["redis.commands.search.aggregation.AggregateResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "rows", "cursor", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.aggregation.AggregateResult.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "rows", "cursor", "schema"], "arg_types": ["redis.commands.search.aggregation.AggregateResult", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AggregateResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.commands.search.aggregation.AggregateResult.cursor", "name": "cursor", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.commands.search.aggregation.AggregateResult.rows", "name": "rows", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.commands.search.aggregation.AggregateResult.schema", "name": "schema", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.search.aggregation.AggregateResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.search.aggregation.AggregateResult", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Asc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.commands.search.aggregation.SortDirection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.search.aggregation.Asc", "name": "Asc", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.Asc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.search.aggregation", "mro": ["redis.commands.search.aggregation.Asc", "redis.commands.search.aggregation.SortDirection", "builtins.object"], "names": {".class": "SymbolTable", "DIRSTRING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.commands.search.aggregation.Asc.DIRSTRING", "name": "DIRSTRING", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "ASC"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.search.aggregation.Asc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.search.aggregation.Asc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Cursor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.search.aggregation.Cursor", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.Cursor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.search.aggregation", "mro": ["redis.commands.search.aggregation.Cursor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.aggregation.Cursor.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cid"], "arg_types": ["redis.commands.search.aggregation.Cursor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Cursor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.Cursor.build_args", "name": "build_args", "original_first_arg": "self", "type": null}}, "cid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.commands.search.aggregation.Cursor.cid", "name": "cid", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.commands.search.aggregation.Cursor.count", "name": "count", "setter_type": null, "type": "builtins.int"}}, "max_idle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.commands.search.aggregation.Cursor.max_idle", "name": "max_idle", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.search.aggregation.Cursor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.search.aggregation.Cursor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Desc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.commands.search.aggregation.SortDirection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.search.aggregation.Desc", "name": "Desc", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.Desc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.search.aggregation", "mro": ["redis.commands.search.aggregation.Desc", "redis.commands.search.aggregation.SortDirection", "builtins.object"], "names": {".class": "SymbolTable", "DIRSTRING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.commands.search.aggregation.Desc.DIRSTRING", "name": "DIRSTRING", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "DESC"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.search.aggregation.Desc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.search.aggregation.Desc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FIELDNAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.aggregation.FIELDNAME", "name": "FIELDNAME", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "Limit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.search.aggregation.Limit", "name": "Limit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.Limit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.search.aggregation", "mro": ["redis.commands.search.aggregation.Limit", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "offset", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.aggregation.Limit.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "offset", "count"], "arg_types": ["redis.commands.search.aggregation.Limit", "builtins.int", "builtins.int"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Limit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.Limit.build_args", "name": "build_args", "original_first_arg": "self", "type": null}}, "count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.commands.search.aggregation.Limit.count", "name": "count", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.commands.search.aggregation.Limit.offset", "name": "offset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.search.aggregation.Limit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.search.aggregation.Limit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Reducer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.search.aggregation.Reducer", "name": "Reducer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.Reducer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.search.aggregation", "mro": ["redis.commands.search.aggregation.Reducer", "builtins.object"], "names": {".class": "SymbolTable", "NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.commands.search.aggregation.Reducer.NAME", "name": "NAME", "setter_type": null, "type": {".class": "NoneType"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.aggregation.Reducer.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["redis.commands.search.aggregation.Reducer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Reducer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.Reducer.alias", "name": "alias", "original_first_arg": "self", "type": null}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "redis.commands.search.aggregation.Reducer.args", "name": "args", "original_first_arg": "self", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "redis.commands.search.aggregation.Reducer.args", "name": "args", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.commands.search.aggregation.Reducer"], "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "args of Reducer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.search.aggregation.Reducer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.search.aggregation.Reducer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SortDirection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.commands.search.aggregation.SortDirection", "name": "SortDirection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.commands.search.aggregation.SortDirection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.commands.search.aggregation", "mro": ["redis.commands.search.aggregation.SortDirection", "builtins.object"], "names": {".class": "SymbolTable", "DIRSTRING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.commands.search.aggregation.SortDirection.DIRSTRING", "name": "DIRSTRING", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.commands.search.aggregation.SortDirection.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field"], "arg_types": ["redis.commands.search.aggregation.SortDirection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SortDirection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.commands.search.aggregation.SortDirection.field", "name": "field", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.search.aggregation.SortDirection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.commands.search.aggregation.SortDirection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.aggregation.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.aggregation.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.aggregation.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.aggregation.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.aggregation.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.commands.search.aggregation.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/redis-stubs/commands/search/aggregation.pyi"}