{".class": "MypyFile", "_fullname": "redis.cluster", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AbstractRedisCluster": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.cluster.AbstractRedisCluster", "name": "AbstractRedisCluster", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.cluster.AbstractRedisCluster", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.cluster", "mro": ["redis.cluster.AbstractRedisCluster", "builtins.object"], "names": {".class": "SymbolTable", "ALL_NODES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.cluster.AbstractRedisCluster.ALL_NODES", "name": "ALL_NODES", "setter_type": null, "type": "builtins.str"}}, "CLUSTER_COMMANDS_RESPONSE_CALLBACKS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.cluster.AbstractRedisCluster.CLUSTER_COMMANDS_RESPONSE_CALLBACKS", "name": "CLUSTER_COMMANDS_RESPONSE_CALLBACKS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "COMMAND_FLAGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.cluster.AbstractRedisCluster.COMMAND_FLAGS", "name": "COMMAND_FLAGS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "DEFAULT_NODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.cluster.AbstractRedisCluster.DEFAULT_NODE", "name": "DEFAULT_NODE", "setter_type": null, "type": "builtins.str"}}, "ERRORS_ALLOW_RETRY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.cluster.AbstractRedisCluster.ERRORS_ALLOW_RETRY", "name": "ERRORS_ALLOW_RETRY", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "redis.exceptions.RedisError"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "NODE_FLAGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.cluster.AbstractRedisCluster.NODE_FLAGS", "name": "NODE_FLAGS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "PRIMARIES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.cluster.AbstractRedisCluster.PRIMARIES", "name": "PRIMARIES", "setter_type": null, "type": "builtins.str"}}, "RANDOM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.cluster.AbstractRedisCluster.RANDOM", "name": "RANDOM", "setter_type": null, "type": "builtins.str"}}, "REPLICAS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.cluster.AbstractRedisCluster.REPLICAS", "name": "REPLICAS", "setter_type": null, "type": "builtins.str"}}, "RESULT_CALLBACKS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.cluster.AbstractRedisCluster.RESULT_CALLBACKS", "name": "RESULT_CALLBACKS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "RedisClusterRequestTTL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "redis.cluster.AbstractRedisCluster.RedisClusterRequestTTL", "name": "RedisClusterRequestTTL", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.AbstractRedisCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.cluster.AbstractRedisCluster", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseParser": {".class": "SymbolTableNode", "cross_ref": "redis.connection.BaseParser", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CaseInsensitiveDict": {".class": "SymbolTableNode", "cross_ref": "redis.client.CaseInsensitiveDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClusterNode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.cluster.ClusterNode", "name": "ClusterNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.cluster.ClusterNode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.cluster", "mro": ["redis.cluster.ClusterNode", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterNode.__del__", "name": "__del__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["redis.cluster.ClusterNode"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__del__ of ClusterNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterNode.__eq__", "name": "__eq__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["redis.cluster.ClusterNode", "builtins.object"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of ClusterNode", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "host", "port", "server_type", "redis_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterNode.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "host", "port", "server_type", "redis_connection"], "arg_types": ["redis.cluster.ClusterNode", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}], "extra_attrs": null, "type_ref": "redis.client.Redis"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ClusterNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterNode.host", "name": "host", "setter_type": null, "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterNode.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterNode.port", "name": "port", "setter_type": null, "type": "builtins.int"}}, "redis_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterNode.redis_connection", "name": "redis_connection", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}], "extra_attrs": null, "type_ref": "redis.client.Redis"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "server_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterNode.server_type", "name": "server_type", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.ClusterNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.cluster.ClusterNode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClusterParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.connection.BaseParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.cluster.ClusterParser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.cluster.ClusterParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.cluster", "mro": ["redis.cluster.ClusterParser", "redis.connection.BaseParser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.ClusterParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.cluster.ClusterParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClusterPipeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.cluster.ClusterPipeline", "name": "ClusterPipeline", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.cluster.ClusterPipeline", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.cluster", "mro": ["redis.cluster.ClusterPipeline", "redis.cluster.RedisCluster", "redis.cluster.AbstractRedisCluster", "redis.commands.cluster.RedisClusterCommands", "redis.commands.cluster.ClusterMultiKeyCommands", "redis.commands.cluster.ClusterManagementCommands", "redis.commands.core.ManagementCommands", "redis.commands.core.ACLCommands", "redis.commands.core.PubSubCommands", "redis.commands.cluster.ClusterDataAccessCommands", "redis.commands.core.DataAccessCommands", "redis.commands.core.BasicKeyCommands", "redis.commands.core.HyperlogCommands", "redis.commands.core.HashCommands", "redis.commands.core.GeoCommands", "redis.commands.core.ListCommands", "redis.commands.core.ScanCommands", "redis.commands.core.SetCommands", "redis.commands.core.StreamCommands", "redis.commands.core.SortedSetCommands", "builtins.object"], "names": {".class": "SymbolTable", "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.__bool__", "name": "__bool__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__bool__ of ClusterPipeline", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "nodes_manager", "commands_parser", "result_callbacks", "cluster_response_callbacks", "startup_nodes", "read_from_replicas", "cluster_error_retry_attempts", "reinitialize_steps", "lock", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "nodes_manager", "commands_parser", "result_callbacks", "cluster_response_callbacks", "startup_nodes", "read_from_replicas", "cluster_error_retry_attempts", "reinitialize_steps", "lock", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["_thread.LockType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ClusterPipeline", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.__len__", "name": "__len__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__len__ of ClusterPipeline", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotate_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exception", "number", "command"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.annotate_exception", "name": "annotate_exception", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exception", "number", "command"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "annotate_exception of ClusterPipeline", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster_error_retry_attempts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.cluster_error_retry_attempts", "name": "cluster_error_retry_attempts", "setter_type": null, "type": "builtins.int"}}, "cluster_response_callbacks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.cluster_response_callbacks", "name": "cluster_response_callbacks", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "command_flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.command_flags", "name": "command_flags", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "command_stack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.command_stack", "name": "command_stack", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "commands_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.commands_parser", "name": "commands_parser", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.ClusterPipeline.delete", "name": "delete", "original_first_arg": "self", "type": null}}, "encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.encoder", "name": "encoder", "setter_type": null, "type": "redis.connection.Encoder"}}, "eval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.eval", "name": "eval", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "eval of ClusterPipeline", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "raise_on_error"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.execute", "name": "execute", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "raise_on_error"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}, "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "execute of ClusterPipeline", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute_command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.ClusterPipeline.execute_command", "name": "execute_command", "original_first_arg": "self", "type": null}}, "explicit_transaction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.explicit_transaction", "name": "explicit_transaction", "setter_type": null, "type": "builtins.bool"}}, "immediate_execute_command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.immediate_execute_command", "name": "immediate_execute_command", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "options"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "immediate_execute_command of ClusterPipeline", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_scripts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.load_scripts", "name": "load_scripts", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load_scripts of ClusterPipeline", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "multi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.multi", "name": "multi", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "multi of ClusterPipeline", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nodes_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.nodes_manager", "name": "nodes_manager", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "pipeline_execute_command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.ClusterPipeline.pipeline_execute_command", "name": "pipeline_execute_command", "original_first_arg": "self", "type": null}}, "raise_first_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stack"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.raise_first_error", "name": "raise_first_error", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stack"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "raise_first_error of ClusterPipeline", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_from_replicas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.read_from_replicas", "name": "read_from_replicas", "setter_type": null, "type": "builtins.bool"}}, "refresh_table_asap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.refresh_table_asap", "name": "refresh_table_asap", "setter_type": null, "type": "builtins.bool"}}, "reinitialize_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.reinitialize_counter", "name": "reinitialize_counter", "setter_type": null, "type": "builtins.int"}}, "reinitialize_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.reinitialize_steps", "name": "reinitialize_steps", "setter_type": null, "type": "builtins.int"}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.reset", "name": "reset", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reset of ClusterPipeline", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "result_callbacks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.result_callbacks", "name": "result_callbacks", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "script_load_for_pipeline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.script_load_for_pipeline", "name": "script_load_for_pipeline", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "script_load_for_pipeline of ClusterPipeline", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scripts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.scripts", "name": "scripts", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "send_cluster_commands": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "stack", "raise_on_error", "allow_redirections"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.send_cluster_commands", "name": "send_cluster_commands", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "stack", "raise_on_error", "allow_redirections"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_cluster_commands of ClusterPipeline", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "startup_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.startup_nodes", "name": "startup_nodes", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "unwatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.unwatch", "name": "unwatch", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "unwatch of ClusterPipeline", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "watch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPipeline.watch", "name": "watch", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "names"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "watch of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "watching": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPipeline.watching", "name": "watching", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.ClusterPipeline.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.ClusterPipeline", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.ClusterPipeline"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_StrType"], "typeddict_type": null}}, "ClusterPubSub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.client.PubSub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.cluster.ClusterPubSub", "name": "ClusterPubSub", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.cluster.ClusterPubSub", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.cluster", "mro": ["redis.cluster.ClusterPubSub", "redis.client.PubSub", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "redis_cluster", "node", "host", "port", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPubSub.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "redis_cluster", "node", "host", "port", "kwargs"], "arg_types": ["redis.cluster.ClusterPubSub", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, {".class": "UnionType", "items": ["redis.cluster.ClusterNode", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ClusterPubSub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPubSub.cluster", "name": "cluster", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}}}, "execute_command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPubSub.execute_command", "name": "execute_command", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["redis.cluster.ClusterPubSub", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "execute_command of ClusterPubSub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_pubsub_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPubSub.get_pubsub_node", "name": "get_pubsub_node", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.cluster.ClusterPubSub"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_pubsub_node of ClusterPubSub", "ret_type": {".class": "UnionType", "items": ["redis.cluster.ClusterNode", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_redis_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPubSub.get_redis_connection", "name": "get_redis_connection", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.cluster.ClusterPubSub"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_redis_connection of ClusterPubSub", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.client.Redis"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.ClusterPubSub.node", "name": "node", "setter_type": null, "type": {".class": "UnionType", "items": ["redis.cluster.ClusterNode", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "set_pubsub_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "cluster", "node", "host", "port"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.ClusterPubSub.set_pubsub_node", "name": "set_pubsub_node", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "cluster", "node", "host", "port"], "arg_types": ["redis.cluster.ClusterPubSub", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, {".class": "UnionType", "items": ["redis.cluster.ClusterNode", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_pubsub_node of ClusterPubSub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.ClusterPubSub.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.cluster.ClusterPubSub", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CommandsParser": {".class": "SymbolTableNode", "cross_ref": "redis.commands.parser.CommandsParser", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Connection": {".class": "SymbolTableNode", "cross_ref": "redis.connection.Connection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConnectionPool": {".class": "SymbolTableNode", "cross_ref": "redis.connection.ConnectionPool", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EncodableT": {".class": "SymbolTableNode", "cross_ref": "redis.typing.EncodableT", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Encoder": {".class": "SymbolTableNode", "cross_ref": "redis.connection.Encoder", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Incomplete": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Incomplete", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "KWARGS_DISABLED_KEYS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.cluster.KWARGS_DISABLED_KEYS", "name": "KWARGS_DISABLED_KEYS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LoadBalancer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.cluster.LoadBalancer", "name": "LoadBalancer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.cluster.LoadBalancer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.cluster", "mro": ["redis.cluster.LoadBalancer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "start_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.LoadBalancer.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "start_index"], "arg_types": ["redis.cluster.LoadBalancer", "builtins.int"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of LoadBalancer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_server_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "primary", "list_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.LoadBalancer.get_server_index", "name": "get_server_index", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "primary", "list_size"], "arg_types": ["redis.cluster.LoadBalancer", "builtins.str", "builtins.int"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_server_index of LoadBalancer", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "primary_to_idx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.LoadBalancer.primary_to_idx", "name": "primary_to_idx", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.LoadBalancer.reset", "name": "reset", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.cluster.LoadBalancer"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reset of LoadBalancer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.LoadBalancer.start_index", "name": "start_index", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.LoadBalancer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.cluster.LoadBalancer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Lock": {".class": "SymbolTableNode", "cross_ref": "threading.Lock", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MovedError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.MovedError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NodeCommands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.cluster.NodeCommands", "name": "NodeCommands", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.cluster.NodeCommands", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.cluster", "mro": ["redis.cluster.NodeCommands", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "parse_response", "connection_pool", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodeCommands.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "parse_response", "connection_pool", "connection"], "arg_types": ["redis.cluster.NodeCommands", "redis.cluster._ParseResponseCallback", "redis.connection.ConnectionPool", "redis.connection.Connection"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NodeCommands", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "c"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodeCommands.append", "name": "append", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "c"], "arg_types": ["redis.cluster.NodeCommands", "redis.cluster.PipelineCommand"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "append of NodeCommands", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "commands": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.NodeCommands.commands", "name": "commands", "setter_type": null, "type": {".class": "Instance", "args": ["redis.cluster.PipelineCommand"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.NodeCommands.connection", "name": "connection", "setter_type": null, "type": "redis.connection.Connection"}}, "connection_pool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.NodeCommands.connection_pool", "name": "connection_pool", "setter_type": null, "type": "redis.connection.ConnectionPool"}}, "parse_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.NodeCommands.parse_response", "name": "parse_response", "setter_type": null, "type": "redis.cluster._ParseResponseCallback"}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodeCommands.read", "name": "read", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.cluster.NodeCommands"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read of NodeCommands", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodeCommands.write", "name": "write", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.cluster.NodeCommands"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "write of Node<PERSON>om<PERSON>s", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.NodeCommands.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.cluster.NodeCommands", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NodesManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.cluster.NodesManager", "name": "NodesManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.cluster.NodesManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.cluster", "mro": ["redis.cluster.NodesManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "startup_nodes", "from_url", "require_full_coverage", "lock", "dynamic_startup_nodes", "connection_pool_class", "address_remap", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodesManager.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "startup_nodes", "from_url", "require_full_coverage", "lock", "dynamic_startup_nodes", "connection_pool_class", "address_remap", "kwargs"], "arg_types": ["redis.cluster.NodesManager", {".class": "Instance", "args": ["redis.cluster.ClusterNode"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["_thread.LockType", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeType", "item": "redis.connection.ConnectionPool"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.int"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NodesManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "address_remap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.NodesManager.address_remap", "name": "address_remap", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.int"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "check_slots_coverage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "slots_cache"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodesManager.check_slots_coverage", "name": "check_slots_coverage", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "slots_cache"], "arg_types": ["redis.cluster.NodesManager", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["redis.cluster.ClusterNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_slots_coverage of NodesManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodesManager.close", "name": "close", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.cluster.NodesManager"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of NodesManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connection_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.NodesManager.connection_kwargs", "name": "connection_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "connection_pool_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.NodesManager.connection_pool_class", "name": "connection_pool_class", "setter_type": null, "type": {".class": "TypeType", "item": "redis.connection.ConnectionPool"}}}, "create_redis_connections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodesManager.create_redis_connections", "name": "create_redis_connections", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nodes"], "arg_types": ["redis.cluster.NodesManager", {".class": "Instance", "args": ["redis.cluster.ClusterNode"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_redis_connections of NodesManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_redis_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "host", "port", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodesManager.create_redis_node", "name": "create_redis_node", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "host", "port", "kwargs"], "arg_types": ["redis.cluster.NodesManager", "builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_redis_node of NodesManager", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}], "extra_attrs": null, "type_ref": "redis.client.Redis"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.NodesManager.default_node", "name": "default_node", "setter_type": null, "type": {".class": "UnionType", "items": ["redis.cluster.ClusterNode", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "from_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.NodesManager.from_url", "name": "from_url", "setter_type": null, "type": "builtins.bool"}}, "get_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "host", "port", "node_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodesManager.get_node", "name": "get_node", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "host", "port", "node_name"], "arg_types": ["redis.cluster.NodesManager", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_node of NodesManager", "ret_type": {".class": "UnionType", "items": ["redis.cluster.ClusterNode", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_node_from_slot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "slot", "read_from_replicas", "server_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodesManager.get_node_from_slot", "name": "get_node_from_slot", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "slot", "read_from_replicas", "server_type"], "arg_types": ["redis.cluster.NodesManager", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_node_from_slot of NodesManager", "ret_type": "redis.cluster.ClusterNode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_nodes_by_server_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "server_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodesManager.get_nodes_by_server_type", "name": "get_nodes_by_server_type", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "server_type"], "arg_types": ["redis.cluster.NodesManager", "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_nodes_by_server_type of NodesManager", "ret_type": {".class": "Instance", "args": ["redis.cluster.ClusterNode"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodesManager.initialize", "name": "initialize", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.cluster.NodesManager"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "initialize of NodesManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nodes_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.NodesManager.nodes_cache", "name": "nodes_cache", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "redis.cluster.ClusterNode"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "populate_startup_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodesManager.populate_startup_nodes", "name": "populate_startup_nodes", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nodes"], "arg_types": ["redis.cluster.NodesManager", {".class": "Instance", "args": ["redis.cluster.ClusterNode"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "populate_startup_nodes of NodesManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_load_balancer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.NodesManager.read_load_balancer", "name": "read_load_balancer", "setter_type": null, "type": "redis.cluster.LoadBalancer"}}, "remap_host_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "host", "port"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodesManager.remap_host_port", "name": "remap_host_port", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "host", "port"], "arg_types": ["redis.cluster.NodesManager", "builtins.str", "builtins.int"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remap_host_port of NodesManager", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodesManager.reset", "name": "reset", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.cluster.NodesManager"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reset of NodesManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "slots_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.NodesManager.slots_cache", "name": "slots_cache", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["redis.cluster.ClusterNode"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "startup_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.NodesManager.startup_nodes", "name": "startup_nodes", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "redis.cluster.ClusterNode"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "update_moved_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.NodesManager.update_moved_exception", "name": "update_moved_exception", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": ["redis.cluster.NodesManager", "redis.exceptions.MovedError"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_moved_exception of NodesManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.NodesManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.cluster.NodesManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PIPELINE_BLOCKED_COMMANDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.cluster.PIPELINE_BLOCKED_COMMANDS", "name": "PIPELINE_BLOCKED_COMMANDS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "PRIMARY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.cluster.PRIMARY", "name": "PRIMARY", "setter_type": null, "type": "builtins.str"}}, "PipelineCommand": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.cluster.PipelineCommand", "name": "PipelineCommand", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.cluster.PipelineCommand", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.cluster", "mro": ["redis.cluster.PipelineCommand", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "args", "options", "position"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.PipelineCommand.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "args", "options", "position"], "arg_types": ["redis.cluster.PipelineCommand", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.client._ParseResponseOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of PipelineCommand", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.PipelineCommand.args", "name": "args", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "asking": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.PipelineCommand.asking", "name": "asking", "setter_type": null, "type": "builtins.bool"}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.PipelineCommand.node", "name": "node", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.PipelineCommand.options", "name": "options", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "redis.client._ParseResponseOptions"}}}, "position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.PipelineCommand.position", "name": "position", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.PipelineCommand.result", "name": "result", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.PipelineCommand.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.cluster.PipelineCommand", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PubSub": {".class": "SymbolTableNode", "cross_ref": "redis.client.PubSub", "kind": "Gdef", "module_hidden": true, "module_public": false}, "REDIS_ALLOWED_KEYS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.cluster.REDIS_ALLOWED_KEYS", "name": "REDIS_ALLOWED_KEYS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "REPLICA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.cluster.REPLICA", "name": "REPLICA", "setter_type": null, "type": "builtins.str"}}, "Redis": {".class": "SymbolTableNode", "cross_ref": "redis.client.Redis", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RedisCluster": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.cluster.AbstractRedisCluster", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.commands.cluster.RedisClusterCommands"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.cluster.RedisCluster", "name": "RedisCluster", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.cluster.RedisCluster", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.cluster", "mro": ["redis.cluster.RedisCluster", "redis.cluster.AbstractRedisCluster", "redis.commands.cluster.RedisClusterCommands", "redis.commands.cluster.ClusterMultiKeyCommands", "redis.commands.cluster.ClusterManagementCommands", "redis.commands.core.ManagementCommands", "redis.commands.core.ACLCommands", "redis.commands.core.PubSubCommands", "redis.commands.cluster.ClusterDataAccessCommands", "redis.commands.core.DataAccessCommands", "redis.commands.core.BasicKeyCommands", "redis.commands.core.HyperlogCommands", "redis.commands.core.HashCommands", "redis.commands.core.GeoCommands", "redis.commands.core.ListCommands", "redis.commands.core.ScanCommands", "redis.commands.core.SetCommands", "redis.commands.core.StreamCommands", "redis.commands.core.SortedSetCommands", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.__del__", "name": "__del__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__del__ of RedisCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.RedisCluster.__enter__", "name": "__enter__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.RedisCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, "values": [], "variance": 0}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of RedisCluster", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.RedisCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.RedisCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.__exit__", "name": "__exit__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of RedisCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "host", "port", "startup_nodes", "cluster_error_retry_attempts", "retry", "require_full_coverage", "reinitialize_steps", "read_from_replicas", "dynamic_startup_nodes", "url", "address_remap", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "host", "port", "startup_nodes", "cluster_error_retry_attempts", "retry", "require_full_coverage", "reinitialize_steps", "read_from_replicas", "dynamic_startup_nodes", "url", "address_remap", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["redis.cluster.ClusterNode"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["redis.retry.Retry", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.int"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RedisCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.close", "name": "close", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of RedisCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster_error_retry_attempts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.RedisCluster.cluster_error_retry_attempts", "name": "cluster_error_retry_attempts", "setter_type": null, "type": "builtins.int"}}, "cluster_response_callbacks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.RedisCluster.cluster_response_callbacks", "name": "cluster_response_callbacks", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "redis.client.CaseInsensitiveDict"}}}, "command_flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.RedisCluster.command_flags", "name": "command_flags", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "commands_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.RedisCluster.commands_parser", "name": "commands_parser", "setter_type": null, "type": "redis.commands.parser.CommandsParser"}}, "determine_slot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.RedisCluster.determine_slot", "name": "determine_slot", "original_first_arg": "self", "type": null}}, "disconnect_connection_pools": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.disconnect_connection_pools", "name": "disconnect_connection_pools", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "disconnect_connection_pools of RedisCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.RedisCluster.encoder", "name": "encoder", "setter_type": null, "type": "redis.connection.Encoder"}}, "execute_command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.RedisCluster.execute_command", "name": "execute_command", "original_first_arg": "self", "type": null}}, "from_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "redis.cluster.RedisCluster.from_url", "name": "from_url", "original_first_arg": "cls", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "url", "kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.RedisCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, "values": [], "variance": 0}}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_url of RedisCluster", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.RedisCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.RedisCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "redis.cluster.RedisCluster.from_url", "name": "from_url", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "url", "kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.RedisCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, "values": [], "variance": 0}}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_url of RedisCluster", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.RedisCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.RedisCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, "values": [], "variance": 0}]}}}}, "get_connection_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.get_connection_kwargs", "name": "get_connection_kwargs", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_connection_kwargs of RedisCluster", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_default_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.get_default_node", "name": "get_default_node", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_default_node of RedisCluster", "ret_type": {".class": "UnionType", "items": ["redis.cluster.ClusterNode", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.get_encoder", "name": "get_encoder", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_encoder of RedisCluster", "ret_type": "redis.connection.Encoder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "host", "port", "node_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.get_node", "name": "get_node", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "host", "port", "node_name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_node of RedisCluster", "ret_type": {".class": "UnionType", "items": ["redis.cluster.ClusterNode", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_node_from_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "replica"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.get_node_from_key", "name": "get_node_from_key", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "replica"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, {".class": "TypeAliasType", "args": [], "type_ref": "redis.connection._Encodable"}, "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_node_from_key of RedisCluster", "ret_type": {".class": "UnionType", "items": ["redis.cluster.ClusterNode", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.get_nodes", "name": "get_nodes", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_nodes of RedisCluster", "ret_type": {".class": "Instance", "args": ["redis.cluster.ClusterNode"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_primaries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.get_primaries", "name": "get_primaries", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_primaries of RedisCluster", "ret_type": {".class": "Instance", "args": ["redis.cluster.ClusterNode"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_random_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.get_random_node", "name": "get_random_node", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_random_node of RedisCluster", "ret_type": "redis.cluster.ClusterNode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_redis_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.get_redis_connection", "name": "get_redis_connection", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, "redis.cluster.ClusterNode"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_redis_connection of RedisCluster", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.client.Redis"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_replicas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.get_replicas", "name": "get_replicas", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_replicas of RedisCluster", "ret_type": {".class": "Instance", "args": ["redis.cluster.ClusterNode"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keyslot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.keyslot", "name": "keyslot", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, {".class": "TypeAliasType", "args": [], "type_ref": "redis.connection._Encodable"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "keyslot of RedisCluster", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "timeout", "sleep", "blocking", "blocking_timeout", "lock_class", "thread_local"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.lock", "name": "lock", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "timeout", "sleep", "blocking", "blocking_timeout", "lock_class", "thread_local"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "lock of RedisCluster", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "monitor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "target_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.monitor", "name": "monitor", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "target_node"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "monitor of RedisCluster", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node_flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.RedisCluster.node_flags", "name": "node_flags", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "nodes_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.RedisCluster.nodes_manager", "name": "nodes_manager", "setter_type": null, "type": "redis.cluster.NodesManager"}}, "on_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.on_connect", "name": "on_connect", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, "redis.connection.Connection"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_connect of RedisCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pipeline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "transaction", "shard_hint"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.pipeline", "name": "pipeline", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "transaction", "shard_hint"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pipeline of RedisCluster", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pubsub": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "node", "host", "port", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.pubsub", "name": "pubsub", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "node", "host", "port", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pubsub of RedisCluster", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_from_replicas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.RedisCluster.read_from_replicas", "name": "read_from_replicas", "setter_type": null, "type": "builtins.bool"}}, "reinitialize_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.RedisCluster.reinitialize_counter", "name": "reinitialize_counter", "setter_type": null, "type": "builtins.int"}}, "reinitialize_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.RedisCluster.reinitialize_steps", "name": "reinitialize_steps", "setter_type": null, "type": "builtins.int"}}, "result_callbacks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.RedisCluster.result_callbacks", "name": "result_callbacks", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "redis.client.CaseInsensitiveDict"}}}, "set_default_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster.RedisCluster.set_default_node", "name": "set_default_node", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, {".class": "UnionType", "items": ["redis.cluster.ClusterNode", {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_default_node of RedisCluster", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "user_on_connect_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.cluster.RedisCluster.user_on_connect_func", "name": "user_on_connect_func", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["redis.connection.Connection"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster.RedisCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.commands.core._StrType", "id": 1, "name": "_StrType", "namespace": "redis.cluster.RedisCluster", "upper_bound": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.cluster.RedisCluster"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_StrType"], "typeddict_type": null}}, "RedisClusterCommands": {".class": "SymbolTableNode", "cross_ref": "redis.commands.cluster.RedisClusterCommands", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RedisError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.RedisError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Retry": {".class": "SymbolTableNode", "cross_ref": "redis.retry.Retry", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SLOT_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.cluster.SLOT_ID", "name": "SLOT_ID", "setter_type": null, "type": "builtins.str"}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unused": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Unused", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ConnectionPoolOptions": {".class": "SymbolTableNode", "cross_ref": "redis.connection._ConnectionPoolOptions", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Encodable": {".class": "SymbolTableNode", "cross_ref": "redis.connection._Encodable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ParseResponseCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.cluster._ParseResponseCallback", "name": "_ParseResponseCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "redis.cluster._ParseResponseCallback", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "redis.cluster", "mro": ["redis.cluster._ParseResponseCallback", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": [null, null, null, "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.cluster._ParseResponseCallback.__call__", "name": "__call__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": [null, null, null, "kwargs"], "arg_types": ["redis.cluster._ParseResponseCallback", "redis.connection.Connection", {".class": "TypeAliasType", "args": [], "type_ref": "redis.typing.EncodableT"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _ParseResponseCallback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.cluster._ParseResponseCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.cluster._ParseResponseCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ParseResponseOptions": {".class": "SymbolTableNode", "cross_ref": "redis.client._ParseResponseOptions", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_StrType": {".class": "SymbolTableNode", "cross_ref": "redis.commands.core._StrType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.cluster.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.cluster.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.cluster.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.cluster.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.cluster.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.cluster.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "block_pipeline_command": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.block_pipeline_command", "name": "block_pipeline_command", "original_first_arg": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "block_pipeline_command", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cleanup_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.cleanup_kwargs", "name": "cleanup_kwargs", "original_first_arg": "kwargs", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cleanup_kwargs", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["redis_node", "args", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.get_connection", "name": "get_connection", "original_first_arg": "redis_node", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["redis_node", "args", "options"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.client.Redis"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "TypeAliasType", "args": [], "type_ref": "redis.connection._ConnectionPoolOptions"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_connection", "ret_type": "redis.connection.Connection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_node_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["host", "port"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.get_node_name", "name": "get_node_name", "original_first_arg": "host", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["host", "port"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_node_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_cluster_myshardid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["resp", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.parse_cluster_myshardid", "name": "parse_cluster_myshardid", "original_first_arg": "resp", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["resp", "options"], "arg_types": ["builtins.bytes", "builtins.object"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_cluster_myshardid", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_cluster_slots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["resp", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.parse_cluster_slots", "name": "parse_cluster_slots", "original_first_arg": "resp", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["resp", "options"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_cluster_slots", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_pubsub_numsub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["command", "res", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.parse_pubsub_numsub", "name": "parse_pubsub_numsub", "original_first_arg": "command", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["command", "res", "options"], "arg_types": ["builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.object"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_pubsub_numsub", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_scan_result": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["command", "res", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "redis.cluster.parse_scan_result", "name": "parse_scan_result", "original_first_arg": "command", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["command", "res", "options"], "arg_types": ["builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_scan_result", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/redis-stubs/cluster.pyi"}