{"data_mtime": 1762066738, "dep_lines": [10, 2, 8, 9, 11, 12, 13, 14, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["redis.commands.core", "collections.abc", "redis.client", "redis.commands", "redis.connection", "redis.exceptions", "redis.retry", "redis.typing", "_typeshed", "threading", "types", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_thread", "abc", "redis.commands.cluster", "redis.commands.parser", "redis.commands.redismodules", "redis.commands.sentinel"], "hash": "843dc4f8c07075f2357f0b53f8cee57413716e2f", "id": "redis.cluster", "ignore_all": true, "interface_hash": "ccb5a1e03b0a9c2c75a68fd7284adbd8f874d15c", "mtime": 1762056680, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "fixed_format_cache": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_equality_for_none": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/redis-stubs/cluster.pyi", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": false, "init_typed": false, "warn_required_dynamic_aliases": false}, null], "size": 11142, "suppressed": [], "version_id": "1.18.2"}