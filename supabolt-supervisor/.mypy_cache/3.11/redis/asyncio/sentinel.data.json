{".class": "MypyFile", "_fullname": "redis.asyncio.sentinel", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AsyncSentinelCommands": {".class": "SymbolTableNode", "cross_ref": "redis.commands.sentinel.AsyncSentinelCommands", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseParser": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.connection.BaseParser", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConnectCallbackT": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.connection.ConnectCallbackT", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Connection": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.connection.Connection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConnectionError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.ConnectionError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConnectionPool": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.connection.ConnectionPool", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CredentialProvider": {".class": "SymbolTableNode", "cross_ref": "redis.credentials.CredentialProvider", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Encoder": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.connection.Encoder", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MasterNotFoundError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ConnectionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.sentinel.MasterNotFoundError", "name": "MasterNotFoundError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.sentinel.MasterNotFoundError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.sentinel", "mro": ["redis.asyncio.sentinel.MasterNotFoundError", "redis.exceptions.ConnectionError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel.MasterNotFoundError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.sentinel.MasterNotFoundError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Redis": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.client.Redis", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RedisError": {".class": "SymbolTableNode", "cross_ref": "redis.exceptions.RedisError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Retry": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.retry.Retry", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SSLConnection": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.connection.SSLConnection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sentinel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.commands.sentinel.AsyncSentinelCommands"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.sentinel.Sentinel", "name": "Sentinel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.sentinel.Sentinel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.sentinel", "mro": ["redis.asyncio.sentinel.Sentinel", "redis.commands.sentinel.AsyncSentinelCommands", "redis.commands.sentinel.SentinelCommands", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "sentinels", "min_other_sentinels", "sentinel_kwargs", "connection_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.sentinel.Sentinel.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "sentinels", "min_other_sentinels", "sentinel_kwargs", "connection_kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Sentinel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_master_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "service_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.sentinel.Sentinel.check_master_state", "name": "check_master_state", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "service_name"], "arg_types": ["redis.asyncio.sentinel.Sentinel", {".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.sentinel._State"}, "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_master_state of Sentinel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connection_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.sentinel.Sentinel.connection_kwargs", "name": "connection_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "discover_master": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "service_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.sentinel.Sentinel.discover_master", "name": "discover_master", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "service_name"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "discover_master of Sentinel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "discover_slaves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "service_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.sentinel.Sentinel.discover_slaves", "name": "discover_slaves", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "service_name"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "discover_slaves of Sentinel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute_command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "once", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.sentinel.Sentinel.execute_command", "name": "execute_command", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "once", "kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "execute_command of Sentinel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter_slaves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "slaves"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.sentinel.Sentinel.filter_slaves", "name": "filter_slaves", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "slaves"], "arg_types": ["redis.asyncio.sentinel.Sentinel", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.sentinel._State"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "filter_slaves of Sentinel", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "master_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "redis.asyncio.sentinel.Sentinel.master_for", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "service_name", "redis_class", "connection_pool_class", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.sentinel.Sentinel.master_for", "name": "master_for", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "service_name", "redis_class", "connection_pool_class", "kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.master_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "master_for of Sentinel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.master_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.master_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "redis.asyncio.sentinel.Sentinel.master_for", "name": "master_for", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "service_name", "redis_class", "connection_pool_class", "kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.master_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "master_for of Sentinel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.master_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.master_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "service_name", "connection_pool_class", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.sentinel.Sentinel.master_for", "name": "master_for", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "service_name", "connection_pool_class", "kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "master_for of Sentinel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "redis.asyncio.sentinel.Sentinel.master_for", "name": "master_for", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "service_name", "connection_pool_class", "kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "master_for of Sentinel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "service_name", "redis_class", "connection_pool_class", "kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.master_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "master_for of Sentinel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.master_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.master_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "service_name", "connection_pool_class", "kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "master_for of Sentinel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "min_other_sentinels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.sentinel.Sentinel.min_other_sentinels", "name": "min_other_sentinels", "setter_type": null, "type": "builtins.int"}}, "sentinel_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.sentinel.Sentinel.sentinel_kwargs", "name": "sentinel_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "sentinels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.sentinel.Sentinel.sentinels", "name": "sentinels", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "slave_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "redis.asyncio.sentinel.Sentinel.slave_for", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "service_name", "redis_class", "connection_pool_class", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.sentinel.Sentinel.slave_for", "name": "slave_for", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "service_name", "redis_class", "connection_pool_class", "kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.slave_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "slave_for of Sentinel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.slave_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.slave_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "redis.asyncio.sentinel.Sentinel.slave_for", "name": "slave_for", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "service_name", "redis_class", "connection_pool_class", "kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.slave_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "slave_for of Sentinel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.slave_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.slave_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "service_name", "connection_pool_class", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "redis.asyncio.sentinel.Sentinel.slave_for", "name": "slave_for", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "service_name", "connection_pool_class", "kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "slave_for of Sentinel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "redis.asyncio.sentinel.Sentinel.slave_for", "name": "slave_for", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "service_name", "connection_pool_class", "kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "slave_for of Sentinel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "service_name", "redis_class", "connection_pool_class", "kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.slave_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "slave_for of Sentinel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.slave_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "id": -1, "name": "_RedisT", "namespace": "redis.asyncio.sentinel.Sentinel.slave_for#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "service_name", "connection_pool_class", "kwargs"], "arg_types": ["redis.asyncio.sentinel.Sentinel", "builtins.str", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "slave_for of Sentinel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel.Sentinel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.sentinel.Sentinel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SentinelConnectionPool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.sentinel.SentinelConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.sentinel.SentinelConnectionPool", "name": "SentinelConnectionPool", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.sentinel.SentinelConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.sentinel.SentinelConnectionPool", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.sentinel", "mro": ["redis.asyncio.sentinel.SentinelConnectionPool", "redis.asyncio.connection.ConnectionPool", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "service_name", "sentinel_manager", "ssl", "connection_class", "is_master", "check_connection", "host", "port", "socket_keepalive", "socket_keepalive_options", "socket_type", "db", "password", "socket_timeout", "socket_connect_timeout", "retry_on_timeout", "retry_on_error", "encoding", "encoding_errors", "decode_responses", "parser_class", "socket_read_size", "health_check_interval", "client_name", "username", "retry", "redis_connect_func", "encoder_class", "credential_provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.sentinel.SentinelConnectionPool.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "service_name", "sentinel_manager", "ssl", "connection_class", "is_master", "check_connection", "host", "port", "socket_keepalive", "socket_keepalive_options", "socket_type", "db", "password", "socket_timeout", "socket_connect_timeout", "retry_on_timeout", "retry_on_error", "encoding", "encoding_errors", "decode_responses", "parser_class", "socket_read_size", "health_check_interval", "client_name", "username", "retry", "redis_connect_func", "encoder_class", "credential_provider"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.sentinel.SentinelConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}, "builtins.str", "redis.asyncio.sentinel.Sentinel", "builtins.bool", {".class": "TypeType", "item": "redis.asyncio.sentinel.SentinelManagedConnection"}, "builtins.bool", "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.bytes"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "redis.exceptions.RedisError"}], "extra_attrs": null, "type_ref": "builtins.list"}, "redis.asyncio.connection._Sentinel"], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "builtins.bool", {".class": "TypeType", "item": "redis.asyncio.connection.BaseParser"}, "builtins.int", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["redis.asyncio.retry.Retry", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.connection.ConnectCallbackT"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "redis.asyncio.connection.Encoder"}, {".class": "UnionType", "items": ["redis.credentials.CredentialProvider", {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SentinelConnectionPool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.sentinel.SentinelConnectionPool.check_connection", "name": "check_connection", "setter_type": null, "type": "builtins.bool"}}, "get_master_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.sentinel.SentinelConnectionPool.get_master_address", "name": "get_master_address", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.sentinel.SentinelConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_master_address of SentinelConnectionPool", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_master": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.sentinel.SentinelConnectionPool.is_master", "name": "is_master", "setter_type": null, "type": "builtins.bool"}}, "master_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.sentinel.SentinelConnectionPool.master_address", "name": "master_address", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "rotate_slaves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.sentinel.SentinelConnectionPool.rotate_slaves", "name": "rotate_slaves", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.sentinel.SentinelConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "rotate_slaves of SentinelConnectionPool", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sentinel_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.sentinel.SentinelConnectionPool.sentinel_manager", "name": "sentinel_manager", "setter_type": null, "type": "redis.asyncio.sentinel.Sentinel"}}, "service_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.sentinel.SentinelConnectionPool.service_name", "name": "service_name", "setter_type": null, "type": "builtins.str"}}, "slave_rr_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.sentinel.SentinelConnectionPool.slave_rr_counter", "name": "slave_rr_counter", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel.SentinelConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.connection._ConnectionT", "id": 1, "name": "_ConnectionT", "namespace": "redis.asyncio.sentinel.SentinelConnectionPool", "upper_bound": "redis.asyncio.connection.AbstractConnection", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "redis.asyncio.sentinel.SentinelConnectionPool"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ConnectionT"], "typeddict_type": null}}, "SentinelManagedConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.asyncio.connection.Connection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.sentinel.SentinelManagedConnection", "name": "SentinelManagedConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.sentinel.SentinelManagedConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.sentinel", "mro": ["redis.asyncio.sentinel.SentinelManagedConnection", "redis.asyncio.connection.Connection", "redis.asyncio.connection.AbstractConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "connection_pool", "host", "port", "socket_keepalive", "socket_keepalive_options", "socket_type", "db", "password", "socket_timeout", "socket_connect_timeout", "retry_on_timeout", "retry_on_error", "encoding", "encoding_errors", "decode_responses", "parser_class", "socket_read_size", "health_check_interval", "client_name", "username", "retry", "redis_connect_func", "encoder_class", "credential_provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "redis.asyncio.sentinel.SentinelManagedConnection.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "connection_pool", "host", "port", "socket_keepalive", "socket_keepalive_options", "socket_type", "db", "password", "socket_timeout", "socket_connect_timeout", "retry_on_timeout", "retry_on_error", "encoding", "encoding_errors", "decode_responses", "parser_class", "socket_read_size", "health_check_interval", "client_name", "username", "retry", "redis_connect_func", "encoder_class", "credential_provider"], "arg_types": ["redis.asyncio.sentinel.SentinelManagedConnection", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.bytes"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "redis.exceptions.RedisError"}], "extra_attrs": null, "type_ref": "builtins.list"}, "redis.asyncio.connection._Sentinel"], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "builtins.bool", {".class": "TypeType", "item": "redis.asyncio.connection.BaseParser"}, "builtins.int", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["redis.asyncio.retry.Retry", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "redis.asyncio.connection.ConnectCallbackT"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "redis.asyncio.connection.Encoder"}, {".class": "UnionType", "items": ["redis.credentials.CredentialProvider", {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SentinelManagedConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.sentinel.SentinelManagedConnection.connect", "name": "connect", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["redis.asyncio.sentinel.SentinelManagedConnection"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect of SentinelManagedConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect_to": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "redis.asyncio.sentinel.SentinelManagedConnection.connect_to", "name": "connect_to", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "address"], "arg_types": ["redis.asyncio.sentinel.SentinelManagedConnection", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect_to of SentinelManagedConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connection_pool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "redis.asyncio.sentinel.SentinelManagedConnection.connection_pool", "name": "connection_pool", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.connection.ConnectionPool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel.SentinelManagedConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.sentinel.SentinelManagedConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SentinelManagedSSLConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.asyncio.sentinel.SentinelManagedConnection", "redis.asyncio.connection.SSLConnection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.sentinel.SentinelManagedSSLConnection", "name": "SentinelManagedSSLConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.sentinel.SentinelManagedSSLConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.sentinel", "mro": ["redis.asyncio.sentinel.SentinelManagedSSLConnection", "redis.asyncio.sentinel.SentinelManagedConnection", "redis.asyncio.connection.SSLConnection", "redis.asyncio.connection.Connection", "redis.asyncio.connection.AbstractConnection", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel.SentinelManagedSSLConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.sentinel.SentinelManagedSSLConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SlaveNotFoundError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["redis.exceptions.ConnectionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.sentinel.SlaveNotFoundError", "name": "SlaveNotFoundError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.sentinel.SlaveNotFoundError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.sentinel", "mro": ["redis.asyncio.sentinel.SlaveNotFoundError", "redis.exceptions.ConnectionError", "redis.exceptions.RedisError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel.SlaveNotFoundError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "redis.asyncio.sentinel.SlaveNotFoundError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ConnectionT": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.connection._ConnectionT", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_RedisT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "redis.asyncio.sentinel._RedisT", "name": "_RedisT", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "redis.asyncio.client.Redis"}, "values": [], "variance": 0}}, "_Sentinel": {".class": "SymbolTableNode", "cross_ref": "redis.asyncio.connection._Sentinel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_State": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "redis.asyncio.sentinel._State", "name": "_State", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "redis.asyncio.sentinel._State", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "redis.asyncio.sentinel", "mro": ["redis.asyncio.sentinel._State", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["ip", "builtins.str"], ["port", "builtins.int"], ["is_master", "builtins.bool"], ["is_sdown", "builtins.bool"], ["is_odown", "builtins.bool"], ["num-other-sentinels", "builtins.int"]], "readonly_keys": [], "required_keys": ["ip", "is_master", "is_odown", "is_sdown", "num-other-sentinels", "port"]}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.sentinel.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.sentinel.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.sentinel.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.sentinel.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.sentinel.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "redis.asyncio.sentinel.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/redis-stubs/asyncio/sentinel.pyi"}