{".class": "MypyFile", "_fullname": "jsonschema_path.handlers.file", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseFilePathHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jsonschema_path.handlers.file.BaseFilePathHandler", "name": "BaseFilePathHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jsonschema_path.handlers.file.BaseFilePathHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jsonschema_path.handlers.file", "mro": ["jsonschema_path.handlers.file.BaseFilePathHandler", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "jsonschema_path.handlers.file.BaseFilePathHandler.__call__", "name": "__call__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "arg_types": ["jsonschema_path.handlers.file.BaseFilePathHandler", "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of BaseFilePathHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5], "arg_names": ["self", "allowed_schemes", "file_handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "jsonschema_path.handlers.file.BaseFilePathHandler.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5], "arg_names": ["self", "allowed_schemes", "file_handler"], "arg_types": ["jsonschema_path.handlers.file.BaseFilePathHandler", "builtins.str", {".class": "UnionType", "items": ["jsonschema_path.handlers.file.FileHandler", {".class": "NoneType"}], "uses_pep604_syntax": false}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseFilePathHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_open": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "jsonschema_path.handlers.file.BaseFilePathHandler._open", "name": "_open", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "arg_types": ["jsonschema_path.handlers.file.BaseFilePathHandler", "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_open of BaseFilePathHandler", "ret_type": {".class": "Instance", "args": ["jsonschema_path.handlers.protocols.SupportsRead"], "extra_attrs": null, "type_ref": "typing.ContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allowed_schemes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "jsonschema_path.handlers.file.BaseFilePathHandler.allowed_schemes", "name": "allowed_schemes", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "file_handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "jsonschema_path.handlers.file.BaseFilePathHandler.file_handler", "name": "file_handler", "setter_type": null, "type": "jsonschema_path.handlers.file.FileHandler"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jsonschema_path.handlers.file.BaseFilePathHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jsonschema_path.handlers.file.BaseFilePathHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContextManager": {".class": "SymbolTableNode", "cross_ref": "typing.ContextManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FileHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jsonschema_path.handlers.file.FileHandler", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jsonschema_path.handlers.file.FileHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jsonschema_path.handlers.file", "mro": ["jsonschema_path.handlers.file.FileHandler", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "jsonschema_path.handlers.file.FileHandler.__call__", "name": "__call__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["jsonschema_path.handlers.file.FileHandler", "jsonschema_path.handlers.protocols.SupportsRead"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FileHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "loader"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "jsonschema_path.handlers.file.FileHandler.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "loader"], "arg_types": ["jsonschema_path.handlers.file.FileHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FileHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "jsonschema_path.handlers.file.FileHandler._load", "name": "_load", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["jsonschema_path.handlers.file.FileHandler", "jsonschema_path.handlers.protocols.SupportsRead"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_load of FileHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loader": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "jsonschema_path.handlers.file.FileHandler.loader", "name": "loader", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jsonschema_path.handlers.file.FileHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jsonschema_path.handlers.file.FileHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FilePathHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jsonschema_path.handlers.file.BaseFilePathHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jsonschema_path.handlers.file.FilePathHandler", "name": "FilePathHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jsonschema_path.handlers.file.FilePathHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jsonschema_path.handlers.file", "mro": ["jsonschema_path.handlers.file.FilePathHandler", "jsonschema_path.handlers.file.BaseFilePathHandler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5], "arg_names": ["self", "allowed_schemes", "file_handler", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "jsonschema_path.handlers.file.FilePathHandler.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["self", "allowed_schemes", "file_handler", "encoding"], "arg_types": ["jsonschema_path.handlers.file.FilePathHandler", "builtins.str", {".class": "UnionType", "items": ["jsonschema_path.handlers.file.FileHandler", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FilePathHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_open": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "jsonschema_path.handlers.file.FilePathHandler._open", "name": "_open", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "arg_types": ["jsonschema_path.handlers.file.FilePathHandler", "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_open of FilePathHandler", "ret_type": {".class": "Instance", "args": ["jsonschema_path.handlers.protocols.SupportsRead"], "extra_attrs": null, "type_ref": "typing.ContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allowed_schemes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "jsonschema_path.handlers.file.FilePathHandler.allowed_schemes", "name": "allowed_schemes", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "encoding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "jsonschema_path.handlers.file.FilePathHandler.encoding", "name": "encoding", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jsonschema_path.handlers.file.FilePathHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jsonschema_path.handlers.file.FilePathHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JsonschemaSafeLoader": {".class": "SymbolTableNode", "cross_ref": "jsonschema_path.loaders.JsonschemaSafeLoader", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsRead": {".class": "SymbolTableNode", "cross_ref": "jsonschema_path.handlers.protocols.SupportsRead", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jsonschema_path.handlers.file.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jsonschema_path.handlers.file.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jsonschema_path.handlers.file.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jsonschema_path.handlers.file.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jsonschema_path.handlers.file.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jsonschema_path.handlers.file.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "dumps": {".class": "SymbolTableNode", "cross_ref": "json.dumps", "kind": "Gdef", "module_hidden": true, "module_public": false}, "load": {".class": "SymbolTableNode", "cross_ref": "yaml.load", "kind": "Gdef", "module_hidden": true, "module_public": false}, "loads": {".class": "SymbolTableNode", "cross_ref": "json.loads", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uri_to_path": {".class": "SymbolTableNode", "cross_ref": "jsonschema_path.handlers.utils.uri_to_path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/jsonschema_path/handlers/file.py"}