{".class": "MypyFile", "_fullname": "structlog", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BoundLogger": {".class": "SymbolTableNode", "cross_ref": "structlog._generic.BoundLogger", "kind": "Gdef"}, "BoundLoggerBase": {".class": "SymbolTableNode", "cross_ref": "structlog._base.BoundLoggerBase", "kind": "Gdef"}, "BytesLogger": {".class": "SymbolTableNode", "cross_ref": "structlog._output.BytesLogger", "kind": "Gdef"}, "BytesLoggerFactory": {".class": "SymbolTableNode", "cross_ref": "structlog._output.BytesLoggerFactory", "kind": "Gdef"}, "DropEvent": {".class": "SymbolTableNode", "cross_ref": "structlog.exceptions.DropEvent", "kind": "Gdef"}, "PrintLogger": {".class": "SymbolTableNode", "cross_ref": "structlog._output.PrintLogger", "kind": "Gdef"}, "PrintLoggerFactory": {".class": "SymbolTableNode", "cross_ref": "structlog._output.PrintLoggerFactory", "kind": "Gdef"}, "ReturnLogger": {".class": "SymbolTableNode", "cross_ref": "structlog.testing.ReturnLogger", "kind": "Gdef"}, "ReturnLoggerFactory": {".class": "SymbolTableNode", "cross_ref": "structlog.testing.ReturnLoggerFactory", "kind": "Gdef"}, "WriteLogger": {".class": "SymbolTableNode", "cross_ref": "structlog._output.WriteLogger", "kind": "Gdef"}, "WriteLoggerFactory": {".class": "SymbolTableNode", "cross_ref": "structlog._output.WriteLoggerFactory", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.__author__", "name": "__author__", "setter_type": null, "type": "builtins.str"}}, "__copyright__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.__copyright__", "name": "__copyright__", "setter_type": null, "type": "builtins.str"}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.__getattr__", "name": "__getattr__", "original_first_arg": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__license__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.__license__", "name": "__license__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__title__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.__title__", "name": "__title__", "setter_type": null, "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "configure": {".class": "SymbolTableNode", "cross_ref": "structlog._config.configure", "kind": "Gdef"}, "configure_once": {".class": "SymbolTableNode", "cross_ref": "structlog._config.configure_once", "kind": "Gdef"}, "contextvars": {".class": "SymbolTableNode", "cross_ref": "structlog.contextvars", "kind": "Gdef"}, "dev": {".class": "SymbolTableNode", "cross_ref": "structlog.dev", "kind": "Gdef"}, "getLogger": {".class": "SymbolTableNode", "cross_ref": "structlog._config.getLogger", "kind": "Gdef"}, "get_config": {".class": "SymbolTableNode", "cross_ref": "structlog._config.get_config", "kind": "Gdef"}, "get_context": {".class": "SymbolTableNode", "cross_ref": "structlog._base.get_context", "kind": "Gdef"}, "get_logger": {".class": "SymbolTableNode", "cross_ref": "structlog._config.get_logger", "kind": "Gdef"}, "is_configured": {".class": "SymbolTableNode", "cross_ref": "structlog._config.is_configured", "kind": "Gdef"}, "make_filtering_bound_logger": {".class": "SymbolTableNode", "cross_ref": "structlog._native.make_filtering_bound_logger", "kind": "Gdef"}, "processors": {".class": "SymbolTableNode", "cross_ref": "structlog.processors", "kind": "Gdef"}, "reset_defaults": {".class": "SymbolTableNode", "cross_ref": "structlog._config.reset_defaults", "kind": "Gdef"}, "stdlib": {".class": "SymbolTableNode", "cross_ref": "structlog.stdlib", "kind": "Gdef"}, "testing": {".class": "SymbolTableNode", "cross_ref": "structlog.testing", "kind": "Gdef"}, "threadlocal": {".class": "SymbolTableNode", "cross_ref": "structlog.threadlocal", "kind": "Gdef"}, "tracebacks": {".class": "SymbolTableNode", "cross_ref": "structlog.tracebacks", "kind": "Gdef"}, "twisted": {".class": "SymbolTableNode", "cross_ref": "structlog.twisted", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "structlog.types", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "structlog.typing", "kind": "Gdef"}, "wrap_logger": {".class": "SymbolTableNode", "cross_ref": "structlog._config.wrap_logger", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/structlog/__init__.py"}