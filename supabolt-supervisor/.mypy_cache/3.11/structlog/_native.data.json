{".class": "MypyFile", "_fullname": "structlog._native", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BoundLoggerBase": {".class": "SymbolTableNode", "cross_ref": "structlog._base.BoundLoggerBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BoundLoggerFilteringAtCritical": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog._native.BoundLoggerFilteringAtCritical", "name": "BoundLoggerFilteringAtCritical", "setter_type": null, "type": {".class": "TypeType", "item": "structlog.typing.FilteringBoundLogger"}}}, "BoundLoggerFilteringAtDebug": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog._native.BoundLoggerFilteringAtDebug", "name": "BoundLoggerFilteringAtDebug", "setter_type": null, "type": {".class": "TypeType", "item": "structlog.typing.FilteringBoundLogger"}}}, "BoundLoggerFilteringAtError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog._native.BoundLoggerFilteringAtError", "name": "BoundLoggerFilteringAtError", "setter_type": null, "type": {".class": "TypeType", "item": "structlog.typing.FilteringBoundLogger"}}}, "BoundLoggerFilteringAtInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog._native.BoundLoggerFilteringAtInfo", "name": "BoundLoggerFilteringAtInfo", "setter_type": null, "type": {".class": "TypeType", "item": "structlog.typing.FilteringBoundLogger"}}}, "BoundLoggerFilteringAtNotset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog._native.BoundLoggerFilteringAtNotset", "name": "BoundLoggerFilteringAtNotset", "setter_type": null, "type": {".class": "TypeType", "item": "structlog.typing.FilteringBoundLogger"}}}, "BoundLoggerFilteringAtWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog._native.BoundLoggerFilteringAtWarning", "name": "BoundLoggerFilteringAtWarning", "setter_type": null, "type": {".class": "TypeType", "item": "structlog.typing.FilteringBoundLogger"}}}, "CRITICAL": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels.CRITICAL", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DEBUG": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels.DEBUG", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ERROR": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels.ERROR", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FilteringBoundLogger": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.FilteringBoundLogger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "INFO": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels.INFO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LEVEL_TO_FILTERING_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog._native.LEVEL_TO_FILTERING_LOGGER", "name": "LEVEL_TO_FILTERING_LOGGER", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int", {".class": "TypeType", "item": "structlog.typing.FilteringBoundLogger"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "LEVEL_TO_NAME": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels.LEVEL_TO_NAME", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NOTSET": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels.NOTSET", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WARNING": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels.WARNING", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ASYNC_CALLING_STACK": {".class": "SymbolTableNode", "cross_ref": "structlog.contextvars._ASYNC_CALLING_STACK", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog._native.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog._native.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog._native.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog._native.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog._native.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog._native.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_anop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog._native._anop", "name": "_anop", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_anop", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_filtering_bound_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["min_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog._native._make_filtering_bound_logger", "name": "_make_filtering_bound_logger", "original_first_arg": "min_level", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["min_level"], "arg_types": ["builtins.int"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make_filtering_bound_logger", "ret_type": {".class": "TypeType", "item": "structlog.typing.FilteringBoundLogger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_nop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog._native._nop", "name": "_nop", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_nop", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aexception": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog._native.aexception", "name": "aexception", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.typing.FilteringBoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aexception", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "contextvars": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "exception": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog._native.exception", "name": "exception", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.typing.FilteringBoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "exception", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_filtering_bound_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["min_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog._native.make_filtering_bound_logger", "name": "make_filtering_bound_logger", "original_first_arg": "min_level", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["min_level"], "arg_types": ["builtins.int"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_filtering_bound_logger", "ret_type": {".class": "TypeType", "item": "structlog.typing.FilteringBoundLogger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/structlog/_native.py"}