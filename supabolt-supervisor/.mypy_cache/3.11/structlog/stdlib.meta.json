{"data_mtime": 1762142166, "dep_lines": [24, 25, 26, 27, 28, 29, 30, 31, 12, 14, 15, 16, 17, 18, 19, 22, 24, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["structlog._config", "structlog._base", "structlog._frames", "structlog._log_levels", "structlog.contextvars", "structlog.exceptions", "structlog.processors", "structlog.typing", "__future__", "asyncio", "<PERSON><PERSON><PERSON>", "functools", "logging", "sys", "warnings", "typing", "structlog", "builtins", "_asyncio", "_collections_abc", "_frozen_importlib", "abc", "asyncio.events", "types"], "hash": "f5389dfdf866044e13fb7547c00fd77b0157924f", "id": "structlog.stdlib", "ignore_all": true, "interface_hash": "8002d2d740f7e38772bd8bd6389d8e7ae3a0767f", "mtime": 1762056679, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "fixed_format_cache": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_equality_for_none": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/structlog/stdlib.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": false, "init_typed": false, "warn_required_dynamic_aliases": false}, null], "size": 38043, "suppressed": [], "version_id": "1.18.2"}