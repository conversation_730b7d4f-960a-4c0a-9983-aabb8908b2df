{"data_mtime": 1762142166, "dep_lines": [18, 28, 32, 15, 17, 20, 21, 22, 23, 27, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["os.path", "rich.pretty", "structlog.typing", "__future__", "os", "dataclasses", "traceback", "types", "typing", "rich", "builtins", "_frozen_importlib", "abc", "enum", "posixpath", "typing_extensions"], "hash": "314e192ba71a9703f0ce851f93012d62330ae8fe", "id": "structlog.tracebacks", "ignore_all": true, "interface_hash": "a39963725caaec31973c158ada482e83463db804", "mtime": 1762056679, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "fixed_format_cache": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_equality_for_none": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/structlog/tracebacks.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": false, "init_typed": false, "warn_required_dynamic_aliases": false}, null], "size": 14346, "suppressed": [], "version_id": "1.18.2"}