{"data_mtime": 1762059596, "dep_lines": [21, 22, 23, 24, 7, 18, 19, 20, 1, 3, 4, 5, 6, 8, 9, 10, 11, 12, 13, 14, 16, 323, 373, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["uvicorn.middleware.asgi2", "uvicorn.middleware.message_logger", "uvicorn.middleware.proxy_headers", "uvicorn.middleware.wsgi", "logging.config", "uvicorn._types", "uvicorn.importer", "uvicorn.logging", "__future__", "asyncio", "inspect", "json", "logging", "os", "socket", "ssl", "sys", "configparser", "pathlib", "typing", "click", "dotenv", "yaml", "builtins", "_frozen_importlib", "_socket", "_ssl", "_typeshed", "abc", "asyncio.coroutines", "asyncio.protocols", "dotenv.main", "enum", "types", "typing_extensions", "uvicorn.middleware"], "hash": "ffcb03a79c24501aa973ebd7b196a27ef03881da", "id": "uvicorn.config", "ignore_all": true, "interface_hash": "18e1fc3dce52cb72c3d7acbd619389f89fb76209", "mtime": 1762056611, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "fixed_format_cache": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_equality_for_none": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/uvicorn/config.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": false, "init_typed": false, "warn_required_dynamic_aliases": false}, null], "size": 20849, "suppressed": [], "version_id": "1.18.2"}