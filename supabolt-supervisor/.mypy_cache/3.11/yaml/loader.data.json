{".class": "MypyFile", "_fullname": "yaml.loader", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BaseConstructor": {".class": "SymbolTableNode", "cross_ref": "yaml.constructor.BaseConstructor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.reader.Reader", "yaml.scanner.Scanner", "yaml.parser.Parser", "yaml.composer.Composer", "yaml.constructor.BaseConstructor", "yaml.resolver.BaseResolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.loader.BaseLoader", "name": "Base<PERSON><PERSON>der", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.loader.BaseLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.loader", "mro": ["yaml.loader.BaseLoader", "yaml.reader.Reader", "yaml.scanner.Scanner", "yaml.parser.Parser", "yaml.composer.Composer", "yaml.constructor.BaseConstructor", "yaml.resolver.BaseResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.loader.BaseLoader.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["yaml.loader.BaseLoader", {".class": "TypeAliasType", "args": [], "type_ref": "yaml.reader._ReadStream"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.loader.BaseLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.loader.BaseLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseResolver": {".class": "SymbolTableNode", "cross_ref": "yaml.resolver.BaseResolver", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Composer": {".class": "SymbolTableNode", "cross_ref": "yaml.composer.Composer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Constructor": {".class": "SymbolTableNode", "cross_ref": "yaml.constructor.Constructor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FullConstructor": {".class": "SymbolTableNode", "cross_ref": "yaml.constructor.FullConstructor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FullLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.reader.Reader", "yaml.scanner.Scanner", "yaml.parser.Parser", "yaml.composer.Composer", "yaml.constructor.FullConstructor", "yaml.resolver.Resolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.loader.FullLoader", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.loader.FullLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.loader", "mro": ["yaml.loader.FullLoader", "yaml.reader.Reader", "yaml.scanner.Scanner", "yaml.parser.Parser", "yaml.composer.Composer", "yaml.constructor.FullConstructor", "yaml.constructor.SafeConstructor", "yaml.constructor.BaseConstructor", "yaml.resolver.Resolver", "yaml.resolver.BaseResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.loader.FullLoader.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["yaml.loader.FullLoader", {".class": "TypeAliasType", "args": [], "type_ref": "yaml.reader._ReadStream"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FullLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.loader.FullLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.loader.FullLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Loader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.reader.Reader", "yaml.scanner.Scanner", "yaml.parser.Parser", "yaml.composer.Composer", "yaml.constructor.Constructor", "yaml.resolver.Resolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.loader.Loader", "name": "Loader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.loader.Loader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.loader", "mro": ["yaml.loader.Loader", "yaml.reader.Reader", "yaml.scanner.Scanner", "yaml.parser.Parser", "yaml.composer.Composer", "yaml.constructor.Constructor", "yaml.constructor.SafeConstructor", "yaml.constructor.BaseConstructor", "yaml.resolver.Resolver", "yaml.resolver.BaseResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.loader.Loader.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["yaml.loader.Loader", {".class": "TypeAliasType", "args": [], "type_ref": "yaml.reader._ReadStream"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Loader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.loader.Loader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.loader.Loader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Parser": {".class": "SymbolTableNode", "cross_ref": "yaml.parser.Parser", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Reader": {".class": "SymbolTableNode", "cross_ref": "yaml.reader.Reader", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Resolver": {".class": "SymbolTableNode", "cross_ref": "yaml.resolver.Resolver", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SafeConstructor": {".class": "SymbolTableNode", "cross_ref": "yaml.constructor.SafeConstructor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SafeLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.reader.Reader", "yaml.scanner.Scanner", "yaml.parser.Parser", "yaml.composer.Composer", "yaml.constructor.SafeConstructor", "yaml.resolver.Resolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.loader.SafeLoader", "name": "<PERSON><PERSON><PERSON>der", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.loader.SafeLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.loader", "mro": ["yaml.loader.SafeLoader", "yaml.reader.Reader", "yaml.scanner.Scanner", "yaml.parser.Parser", "yaml.composer.Composer", "yaml.constructor.SafeConstructor", "yaml.constructor.BaseConstructor", "yaml.resolver.Resolver", "yaml.resolver.BaseResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.loader.SafeLoader.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["yaml.loader.SafeLoader", {".class": "TypeAliasType", "args": [], "type_ref": "yaml.reader._ReadStream"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SafeLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.loader.SafeLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.loader.SafeLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Scanner": {".class": "SymbolTableNode", "cross_ref": "yaml.scanner.Scanner", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UnsafeLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.reader.Reader", "yaml.scanner.Scanner", "yaml.parser.Parser", "yaml.composer.Composer", "yaml.constructor.Constructor", "yaml.resolver.Resolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.loader.UnsafeLoader", "name": "UnsafeLoader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.loader.UnsafeLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.loader", "mro": ["yaml.loader.UnsafeLoader", "yaml.reader.Reader", "yaml.scanner.Scanner", "yaml.parser.Parser", "yaml.composer.Composer", "yaml.constructor.Constructor", "yaml.constructor.SafeConstructor", "yaml.constructor.BaseConstructor", "yaml.resolver.Resolver", "yaml.resolver.BaseResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.loader.UnsafeLoader.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["yaml.loader.UnsafeLoader", {".class": "TypeAliasType", "args": [], "type_ref": "yaml.reader._ReadStream"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of UnsafeLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.loader.UnsafeLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.loader.UnsafeLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Loader": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "yaml.loader._Loader", "line": 12, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["yaml.loader.Loader", "yaml.loader.BaseLoader", "yaml.loader.FullLoader", "yaml.loader.SafeLoader", "yaml.loader.UnsafeLoader"], "uses_pep604_syntax": true}}}, "_ReadStream": {".class": "SymbolTableNode", "cross_ref": "yaml.reader._ReadStream", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "yaml.loader.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.loader.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.loader.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.loader.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.loader.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.loader.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.loader.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/yaml-stubs/loader.pyi"}