{".class": "MypyFile", "_fullname": "yaml.scanner", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MarkedYAMLError": {".class": "SymbolTableNode", "cross_ref": "yaml.error.MarkedYAMLError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Scanner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.scanner.Scanner", "name": "Scanner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.scanner", "mro": ["yaml.scanner.Scanner", "builtins.object"], "names": {".class": "SymbolTable", "ESCAPE_CODES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.Scanner.ESCAPE_CODES", "name": "ESCAPE_CODES", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "ESCAPE_REPLACEMENTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.Scanner.ESCAPE_REPLACEMENTS", "name": "ESCAPE_REPLACEMENTS", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.scanner.Scanner.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["yaml.scanner.Scanner"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Scanner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_indent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.add_indent", "name": "add_indent", "original_first_arg": "self", "type": null}}, "allow_simple_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.Scanner.allow_simple_key", "name": "allow_simple_key", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "check_block_entry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.check_block_entry", "name": "check_block_entry", "original_first_arg": "self", "type": null}}, "check_directive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.check_directive", "name": "check_directive", "original_first_arg": "self", "type": null}}, "check_document_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.check_document_end", "name": "check_document_end", "original_first_arg": "self", "type": null}}, "check_document_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.check_document_start", "name": "check_document_start", "original_first_arg": "self", "type": null}}, "check_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.check_key", "name": "check_key", "original_first_arg": "self", "type": null}}, "check_plain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.check_plain", "name": "check_plain", "original_first_arg": "self", "type": null}}, "check_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "choices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.check_token", "name": "check_token", "original_first_arg": "self", "type": null}}, "check_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.check_value", "name": "check_value", "original_first_arg": "self", "type": null}}, "done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.Scanner.done", "name": "done", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "fetch_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_alias", "name": "fetch_alias", "original_first_arg": "self", "type": null}}, "fetch_anchor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_anchor", "name": "fetch_anchor", "original_first_arg": "self", "type": null}}, "fetch_block_entry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_block_entry", "name": "fetch_block_entry", "original_first_arg": "self", "type": null}}, "fetch_block_scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_block_scalar", "name": "fetch_block_scalar", "original_first_arg": "self", "type": null}}, "fetch_directive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_directive", "name": "fetch_directive", "original_first_arg": "self", "type": null}}, "fetch_document_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_document_end", "name": "fetch_document_end", "original_first_arg": "self", "type": null}}, "fetch_document_indicator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "TokenClass"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_document_indicator", "name": "fetch_document_indicator", "original_first_arg": "self", "type": null}}, "fetch_document_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_document_start", "name": "fetch_document_start", "original_first_arg": "self", "type": null}}, "fetch_double": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_double", "name": "fetch_double", "original_first_arg": "self", "type": null}}, "fetch_flow_collection_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "TokenClass"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_flow_collection_end", "name": "fetch_flow_collection_end", "original_first_arg": "self", "type": null}}, "fetch_flow_collection_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "TokenClass"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_flow_collection_start", "name": "fetch_flow_collection_start", "original_first_arg": "self", "type": null}}, "fetch_flow_entry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_flow_entry", "name": "fetch_flow_entry", "original_first_arg": "self", "type": null}}, "fetch_flow_mapping_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_flow_mapping_end", "name": "fetch_flow_mapping_end", "original_first_arg": "self", "type": null}}, "fetch_flow_mapping_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_flow_mapping_start", "name": "fetch_flow_mapping_start", "original_first_arg": "self", "type": null}}, "fetch_flow_scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_flow_scalar", "name": "fetch_flow_scalar", "original_first_arg": "self", "type": null}}, "fetch_flow_sequence_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_flow_sequence_end", "name": "fetch_flow_sequence_end", "original_first_arg": "self", "type": null}}, "fetch_flow_sequence_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_flow_sequence_start", "name": "fetch_flow_sequence_start", "original_first_arg": "self", "type": null}}, "fetch_folded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_folded", "name": "fetch_folded", "original_first_arg": "self", "type": null}}, "fetch_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_key", "name": "fetch_key", "original_first_arg": "self", "type": null}}, "fetch_literal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_literal", "name": "fetch_literal", "original_first_arg": "self", "type": null}}, "fetch_more_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_more_tokens", "name": "fetch_more_tokens", "original_first_arg": "self", "type": null}}, "fetch_plain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_plain", "name": "fetch_plain", "original_first_arg": "self", "type": null}}, "fetch_single": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_single", "name": "fetch_single", "original_first_arg": "self", "type": null}}, "fetch_stream_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_stream_end", "name": "fetch_stream_end", "original_first_arg": "self", "type": null}}, "fetch_stream_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_stream_start", "name": "fetch_stream_start", "original_first_arg": "self", "type": null}}, "fetch_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_tag", "name": "fetch_tag", "original_first_arg": "self", "type": null}}, "fetch_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.fetch_value", "name": "fetch_value", "original_first_arg": "self", "type": null}}, "flow_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.Scanner.flow_level", "name": "flow_level", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.get_token", "name": "get_token", "original_first_arg": "self", "type": null}}, "indent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.Scanner.indent", "name": "indent", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "indents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.Scanner.indents", "name": "indents", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "need_more_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.need_more_tokens", "name": "need_more_tokens", "original_first_arg": "self", "type": null}}, "next_possible_simple_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.next_possible_simple_key", "name": "next_possible_simple_key", "original_first_arg": "self", "type": null}}, "peek_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.peek_token", "name": "peek_token", "original_first_arg": "self", "type": null}}, "possible_simple_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.Scanner.possible_simple_keys", "name": "possible_simple_keys", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "remove_possible_simple_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.remove_possible_simple_key", "name": "remove_possible_simple_key", "original_first_arg": "self", "type": null}}, "save_possible_simple_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.save_possible_simple_key", "name": "save_possible_simple_key", "original_first_arg": "self", "type": null}}, "scan_anchor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "TokenClass"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_anchor", "name": "scan_anchor", "original_first_arg": "self", "type": null}}, "scan_block_scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_block_scalar", "name": "scan_block_scalar", "original_first_arg": "self", "type": null}}, "scan_block_scalar_breaks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "indent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_block_scalar_breaks", "name": "scan_block_scalar_breaks", "original_first_arg": "self", "type": null}}, "scan_block_scalar_ignored_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_block_scalar_ignored_line", "name": "scan_block_scalar_ignored_line", "original_first_arg": "self", "type": null}}, "scan_block_scalar_indentation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_block_scalar_indentation", "name": "scan_block_scalar_indentation", "original_first_arg": "self", "type": null}}, "scan_block_scalar_indicators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_block_scalar_indicators", "name": "scan_block_scalar_indicators", "original_first_arg": "self", "type": null}}, "scan_directive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_directive", "name": "scan_directive", "original_first_arg": "self", "type": null}}, "scan_directive_ignored_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_directive_ignored_line", "name": "scan_directive_ignored_line", "original_first_arg": "self", "type": null}}, "scan_directive_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_directive_name", "name": "scan_directive_name", "original_first_arg": "self", "type": null}}, "scan_flow_scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_flow_scalar", "name": "scan_flow_scalar", "original_first_arg": "self", "type": null}}, "scan_flow_scalar_breaks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "double", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_flow_scalar_breaks", "name": "scan_flow_scalar_breaks", "original_first_arg": "self", "type": null}}, "scan_flow_scalar_non_spaces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "double", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_flow_scalar_non_spaces", "name": "scan_flow_scalar_non_spaces", "original_first_arg": "self", "type": null}}, "scan_flow_scalar_spaces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "double", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_flow_scalar_spaces", "name": "scan_flow_scalar_spaces", "original_first_arg": "self", "type": null}}, "scan_line_break": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_line_break", "name": "scan_line_break", "original_first_arg": "self", "type": null}}, "scan_plain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_plain", "name": "scan_plain", "original_first_arg": "self", "type": null}}, "scan_plain_spaces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "indent", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_plain_spaces", "name": "scan_plain_spaces", "original_first_arg": "self", "type": null}}, "scan_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_tag", "name": "scan_tag", "original_first_arg": "self", "type": null}}, "scan_tag_directive_handle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_tag_directive_handle", "name": "scan_tag_directive_handle", "original_first_arg": "self", "type": null}}, "scan_tag_directive_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_tag_directive_prefix", "name": "scan_tag_directive_prefix", "original_first_arg": "self", "type": null}}, "scan_tag_directive_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_tag_directive_value", "name": "scan_tag_directive_value", "original_first_arg": "self", "type": null}}, "scan_tag_handle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_tag_handle", "name": "scan_tag_handle", "original_first_arg": "self", "type": null}}, "scan_tag_uri": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_tag_uri", "name": "scan_tag_uri", "original_first_arg": "self", "type": null}}, "scan_to_next_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_to_next_token", "name": "scan_to_next_token", "original_first_arg": "self", "type": null}}, "scan_uri_escapes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_uri_escapes", "name": "scan_uri_escapes", "original_first_arg": "self", "type": null}}, "scan_yaml_directive_number": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_yaml_directive_number", "name": "scan_yaml_directive_number", "original_first_arg": "self", "type": null}}, "scan_yaml_directive_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "start_mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.scan_yaml_directive_value", "name": "scan_yaml_directive_value", "original_first_arg": "self", "type": null}}, "stale_possible_simple_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.stale_possible_simple_keys", "name": "stale_possible_simple_keys", "original_first_arg": "self", "type": null}}, "tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.Scanner.tokens", "name": "tokens", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "tokens_taken": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.Scanner.tokens_taken", "name": "tokens_taken", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "unwind_indent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yaml.scanner.Scanner.unwind_indent", "name": "unwind_indent", "original_first_arg": "self", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.scanner.Scanner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.scanner.Scanner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ScannerError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml.error.MarkedYAMLError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.scanner.ScannerError", "name": "ScannerError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.scanner.ScannerError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.scanner", "mro": ["yaml.scanner.ScannerError", "yaml.error.MarkedYAMLError", "yaml.error.YAMLError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.scanner.ScannerError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.scanner.ScannerError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleKey": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.scanner.SimpleKey", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.scanner.SimpleKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.scanner", "mro": ["yaml.scanner.SimpleKey", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "token_number", "required", "index", "line", "column", "mark"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.scanner.SimpleKey.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "token_number", "required", "index", "line", "column", "mark"], "arg_types": ["yaml.scanner.SimpleKey", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SimpleKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.SimpleKey.column", "name": "column", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.SimpleKey.index", "name": "index", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.SimpleKey.line", "name": "line", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "mark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.SimpleKey.mark", "name": "mark", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "required": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.SimpleKey.required", "name": "required", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "token_number": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "yaml.scanner.SimpleKey.token_number", "name": "token_number", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.scanner.SimpleKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.scanner.SimpleKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "yaml.scanner.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.scanner.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.scanner.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.scanner.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.scanner.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.scanner.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.scanner.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/yaml-stubs/scanner.pyi"}