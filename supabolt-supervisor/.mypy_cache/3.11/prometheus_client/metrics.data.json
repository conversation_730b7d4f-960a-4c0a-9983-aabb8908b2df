{".class": "MypyFile", "_fullname": "prometheus_client.metrics", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Collector": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.registry.Collector", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CollectorRegistry": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.registry.CollectorRegistry", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Counter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prometheus_client.metrics.MetricWrapperBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prometheus_client.metrics.Counter", "name": "Counter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.Counter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prometheus_client.metrics", "mro": ["prometheus_client.metrics.Counter", "prometheus_client.metrics.MetricWrapperBase", "prometheus_client.registry.Collector", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_child_samples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Counter._child_samples", "name": "_child_samples", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Counter"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_child_samples of Counter", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prometheus_client.samples.Sample"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_created": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Counter._created", "name": "_created", "setter_type": null, "type": "builtins.float"}}, "_metric_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Counter._metric_init", "name": "_metric_init", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Counter"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_metric_init of Counter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.metrics.Counter._type", "name": "_type", "setter_type": null, "type": "builtins.str"}}, "_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Counter._value", "name": "_value", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "count_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Counter.count_exceptions", "name": "count_exceptions", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "exception"], "arg_types": ["prometheus_client.metrics.Counter", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.BaseException"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count_exceptions of Counter", "ret_type": "prometheus_client.context_managers.ExceptionCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "amount", "exemplar"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Counter.inc", "name": "inc", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "amount", "exemplar"], "arg_types": ["prometheus_client.metrics.Counter", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "inc of Counter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Counter.reset", "name": "reset", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Counter"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reset of Counter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.Counter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prometheus_client.metrics.Counter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Enum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prometheus_client.metrics.MetricWrapperBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prometheus_client.metrics.Enum", "name": "Enum", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.Enum", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prometheus_client.metrics", "mro": ["prometheus_client.metrics.Enum", "prometheus_client.metrics.MetricWrapperBase", "prometheus_client.registry.Collector", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "documentation", "labelnames", "namespace", "subsystem", "unit", "registry", "_labelvalues", "states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Enum.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "documentation", "labelnames", "namespace", "subsystem", "unit", "registry", "_labelvalues", "states"], "arg_types": ["prometheus_client.metrics.Enum", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["prometheus_client.registry.CollectorRegistry", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Enum", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_child_samples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Enum._child_samples", "name": "_child_samples", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Enum"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_child_samples of Enum", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prometheus_client.samples.Sample"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_metric_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Enum._metric_init", "name": "_metric_init", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Enum"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_metric_init of Enum", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_states": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Enum._states", "name": "_states", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.metrics.Enum._type", "name": "_type", "setter_type": null, "type": "builtins.str"}}, "_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Enum._value", "name": "_value", "setter_type": null, "type": "builtins.int"}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Enum.state", "name": "state", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["prometheus_client.metrics.Enum", "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "state of Enum", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.Enum.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prometheus_client.metrics.Enum", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExceptionCounter": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.context_managers.ExceptionCounter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Exemplar": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.samples.Exemplar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "F": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.F", "name": "F", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}}, "Gauge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prometheus_client.metrics.MetricWrapperBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prometheus_client.metrics.Gauge", "name": "Gauge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.Gauge", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prometheus_client.metrics", "mro": ["prometheus_client.metrics.Gauge", "prometheus_client.metrics.MetricWrapperBase", "prometheus_client.registry.Collector", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_MOST_RECENT_MODES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.metrics.Gauge._MOST_RECENT_MODES", "name": "_MOST_RECENT_MODES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "_MULTIPROC_MODES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.metrics.Gauge._MULTIPROC_MODES", "name": "_MULTIPROC_MODES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "documentation", "labelnames", "namespace", "subsystem", "unit", "registry", "_labelvalues", "multiprocess_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Gauge.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "documentation", "labelnames", "namespace", "subsystem", "unit", "registry", "_labelvalues", "multiprocess_mode"], "arg_types": ["prometheus_client.metrics.Gauge", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["prometheus_client.registry.CollectorRegistry", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "all"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "liveall"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "min"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "livemin"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "livemax"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sum"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "livesum"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mostrecent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "livemostrecent"}], "uses_pep604_syntax": false}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Gauge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_child_samples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Gauge._child_samples", "name": "_child_samples", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Gauge"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_child_samples of Gauge", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prometheus_client.samples.Sample"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_most_recent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Gauge._is_most_recent", "name": "_is_most_recent", "setter_type": null, "type": "builtins.bool"}}, "_metric_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Gauge._metric_init", "name": "_metric_init", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Gauge"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_metric_init of Gauge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_multiprocess_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Gauge._multiprocess_mode", "name": "_multiprocess_mode", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "all"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "liveall"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "min"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "livemin"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "livemax"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sum"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "livesum"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mostrecent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "livemostrecent"}], "uses_pep604_syntax": false}}}, "_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.metrics.Gauge._type", "name": "_type", "setter_type": null, "type": "builtins.str"}}, "_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Gauge._value", "name": "_value", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "dec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "amount"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Gauge.dec", "name": "dec", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "amount"], "arg_types": ["prometheus_client.metrics.Gauge", "builtins.float"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dec of Gauge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "amount"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Gauge.inc", "name": "inc", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "amount"], "arg_types": ["prometheus_client.metrics.Gauge", "builtins.float"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "inc of Gauge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Gauge.set", "name": "set", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["prometheus_client.metrics.Gauge", "builtins.float"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set of Gauge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "f"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Gauge.set_function", "name": "set_function", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "f"], "arg_types": ["prometheus_client.metrics.Gauge", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_function of Gauge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_to_current_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Gauge.set_to_current_time", "name": "set_to_current_time", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Gauge"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_to_current_time of Gauge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Gauge.time", "name": "time", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Gauge"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "time of Gauge", "ret_type": "prometheus_client.context_managers.Timer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "track_inprogress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Gauge.track_inprogress", "name": "track_inprogress", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Gauge"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "track_inprogress of Gauge", "ret_type": "prometheus_client.context_managers.InprogressTracker", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.Gauge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prometheus_client.metrics.Gauge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Histogram": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prometheus_client.metrics.MetricWrapperBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prometheus_client.metrics.Histogram", "name": "Histogram", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.Histogram", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prometheus_client.metrics", "mro": ["prometheus_client.metrics.Histogram", "prometheus_client.metrics.MetricWrapperBase", "prometheus_client.registry.Collector", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_BUCKETS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.metrics.Histogram.DEFAULT_BUCKETS", "name": "DEFAULT_BUCKETS", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "documentation", "labelnames", "namespace", "subsystem", "unit", "registry", "_labelvalues", "buckets"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Histogram.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "documentation", "labelnames", "namespace", "subsystem", "unit", "registry", "_labelvalues", "buckets"], "arg_types": ["prometheus_client.metrics.Histogram", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["prometheus_client.registry.CollectorRegistry", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Histogram", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_buckets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Histogram._buckets", "name": "_buckets", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_child_samples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Histogram._child_samples", "name": "_child_samples", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Histogram"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_child_samples of Histogram", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prometheus_client.samples.Sample"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_created": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Histogram._created", "name": "_created", "setter_type": null, "type": "builtins.float"}}, "_metric_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Histogram._metric_init", "name": "_metric_init", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Histogram"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_metric_init of Histogram", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_buckets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source_buckets"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Histogram._prepare_buckets", "name": "_prepare_buckets", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "source_buckets"], "arg_types": ["prometheus_client.metrics.Histogram", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_prepare_buckets of Histogram", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reserved_labelnames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.metrics.Histogram._reserved_labelnames", "name": "_reserved_labelnames", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_sum": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Histogram._sum", "name": "_sum", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.metrics.Histogram._type", "name": "_type", "setter_type": null, "type": "builtins.str"}}, "_upper_bounds": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Histogram._upper_bounds", "name": "_upper_bounds", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "observe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "amount", "exemplar"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Histogram.observe", "name": "observe", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "amount", "exemplar"], "arg_types": ["prometheus_client.metrics.Histogram", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "observe of Histogram", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Histogram.time", "name": "time", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Histogram"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "time of Histogram", "ret_type": "prometheus_client.context_managers.Timer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.Histogram.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prometheus_client.metrics.Histogram", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "INF": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.utils.INF", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prometheus_client.metrics.MetricWrapperBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prometheus_client.metrics.Info", "name": "Info", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.Info", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prometheus_client.metrics", "mro": ["prometheus_client.metrics.Info", "prometheus_client.metrics.MetricWrapperBase", "prometheus_client.registry.Collector", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_child_samples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Info._child_samples", "name": "_child_samples", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Info"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_child_samples of Info", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prometheus_client.samples.Sample"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_labelname_set": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Info._labelname_set", "name": "_labelname_set", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_metric_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.Info._metric_init", "name": "_metric_init", "original_first_arg": "self", "type": null}}, "_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.metrics.Info._type", "name": "_type", "setter_type": null, "type": "builtins.str"}}, "_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "invalid_partial_type"], "fullname": "prometheus_client.metrics.Info._value", "name": "_value", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Info.info", "name": "info", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["prometheus_client.metrics.Info", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "info of Info", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.Info.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prometheus_client.metrics.Info", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InprogressTracker": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.context_managers.InprogressTracker", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Lock": {".class": "SymbolTableNode", "cross_ref": "threading.Lock", "kind": "Gdef", "module_hidden": true, "module_public": false}, "METRIC_LABEL_NAME_RE": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.metrics_core.METRIC_LABEL_NAME_RE", "kind": "Gdef", "module_hidden": true, "module_public": false}, "METRIC_NAME_RE": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.metrics_core.METRIC_NAME_RE", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Metric": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.metrics_core.Metric", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MetricWrapperBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prometheus_client.registry.Collector"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prometheus_client.metrics.MetricWrapperBase", "name": "MetricWrapperBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.MetricWrapperBase", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prometheus_client.metrics", "mro": ["prometheus_client.metrics.MetricWrapperBase", "prometheus_client.registry.Collector", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "documentation", "labelnames", "namespace", "subsystem", "unit", "registry", "_labelvalues"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.MetricWrapperBase.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "documentation", "labelnames", "namespace", "subsystem", "unit", "registry", "_labelvalues"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.T", "id": -1, "name": "T", "namespace": "prometheus_client.metrics.MetricWrapperBase.__init__", "upper_bound": "prometheus_client.metrics.MetricWrapperBase", "values": [], "variance": 0}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["prometheus_client.registry.CollectorRegistry", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MetricWrapperBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.T", "id": -1, "name": "T", "namespace": "prometheus_client.metrics.MetricWrapperBase.__init__", "upper_bound": "prometheus_client.metrics.MetricWrapperBase", "values": [], "variance": 0}]}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.MetricWrapperBase.__repr__", "name": "__repr__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prometheus_client.metrics.MetricWrapperBase"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of MetricWrapperBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.MetricWrapperBase.__str__", "name": "__str__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prometheus_client.metrics.MetricWrapperBase"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__str__ of MetricWrapperBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_child_samples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.MetricWrapperBase._child_samples", "name": "_child_samples", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.MetricWrapperBase"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_child_samples of MetricWrapperBase", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prometheus_client.samples.Sample"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_documentation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.MetricWrapperBase._documentation", "name": "_documentation", "setter_type": null, "type": "builtins.str"}}, "_get_metric": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.MetricWrapperBase._get_metric", "name": "_get_metric", "original_first_arg": "self", "type": null}}, "_is_observable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.MetricWrapperBase._is_observable", "name": "_is_observable", "original_first_arg": "self", "type": null}}, "_is_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.MetricWrapperBase._is_parent", "name": "_is_parent", "original_first_arg": "self", "type": null}}, "_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.MetricWrapperBase._kwargs", "name": "_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_labelnames": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.MetricWrapperBase._labelnames", "name": "_labelnames", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_labelvalues": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.MetricWrapperBase._labelvalues", "name": "_labelvalues", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.MetricWrapperBase._lock", "name": "_lock", "setter_type": null, "type": "_thread.LockType"}}, "_metric_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.MetricWrapperBase._metric_init", "name": "_metric_init", "original_first_arg": "self", "type": null}}, "_metrics": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.MetricWrapperBase._metrics", "name": "_metrics", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.T", "id": -1, "name": "T", "namespace": "prometheus_client.metrics.MetricWrapperBase.__init__", "upper_bound": "prometheus_client.metrics.MetricWrapperBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_multi_samples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.MetricWrapperBase._multi_samples", "name": "_multi_samples", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.MetricWrapperBase"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_multi_samples of MetricWrapperBase", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prometheus_client.samples.Sample"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.MetricWrapperBase._name", "name": "_name", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_raise_if_not_observable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.MetricWrapperBase._raise_if_not_observable", "name": "_raise_if_not_observable", "original_first_arg": "self", "type": null}}, "_reserved_labelnames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "prometheus_client.metrics.MetricWrapperBase._reserved_labelnames", "name": "_reserved_labelnames", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "_samples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.MetricWrapperBase._samples", "name": "_samples", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.MetricWrapperBase"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_samples of MetricWrapperBase", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prometheus_client.samples.Sample"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "prometheus_client.metrics.MetricWrapperBase._type", "name": "_type", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_unit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.MetricWrapperBase._unit", "name": "_unit", "setter_type": null, "type": "builtins.str"}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.MetricWrapperBase.clear", "name": "clear", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.MetricWrapperBase"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "clear of MetricWrapperBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.MetricWrapperBase.collect", "name": "collect", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.MetricWrapperBase"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "collect of MetricWrapperBase", "ret_type": {".class": "Instance", "args": ["prometheus_client.metrics_core.Metric"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.MetricWrapperBase.describe", "name": "describe", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.MetricWrapperBase"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe of MetricWrapperBase", "ret_type": {".class": "Instance", "args": ["prometheus_client.metrics_core.Metric"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "labelvalues", "labelkwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.MetricWrapperBase.labels", "name": "labels", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "labelvalues", "labelkwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.T", "id": -1, "name": "T", "namespace": "prometheus_client.metrics.MetricWrapperBase.labels", "upper_bound": "prometheus_client.metrics.MetricWrapperBase", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "labels of MetricWrapperBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.T", "id": -1, "name": "T", "namespace": "prometheus_client.metrics.MetricWrapperBase.labels", "upper_bound": "prometheus_client.metrics.MetricWrapperBase", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.T", "id": -1, "name": "T", "namespace": "prometheus_client.metrics.MetricWrapperBase.labels", "upper_bound": "prometheus_client.metrics.MetricWrapperBase", "values": [], "variance": 0}]}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "labelvalues"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.MetricWrapperBase.remove", "name": "remove", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "labelvalues"], "arg_types": ["prometheus_client.metrics.MetricWrapperBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remove of MetricWrapperBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.MetricWrapperBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prometheus_client.metrics.MetricWrapperBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "REGISTRY": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.registry.REGISTRY", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RESERVED_METRIC_LABEL_NAME_RE": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.metrics_core.RESERVED_METRIC_LABEL_NAME_RE", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sample": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.samples.Sample", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Summary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prometheus_client.metrics.MetricWrapperBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prometheus_client.metrics.Summary", "name": "Summary", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.Summary", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prometheus_client.metrics", "mro": ["prometheus_client.metrics.Summary", "prometheus_client.metrics.MetricWrapperBase", "prometheus_client.registry.Collector", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_child_samples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Summary._child_samples", "name": "_child_samples", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Summary"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_child_samples of Summary", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prometheus_client.samples.Sample"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Summary._count", "name": "_count", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "_created": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Summary._created", "name": "_created", "setter_type": null, "type": "builtins.float"}}, "_metric_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Summary._metric_init", "name": "_metric_init", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Summary"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_metric_init of Summary", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reserved_labelnames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.metrics.Summary._reserved_labelnames", "name": "_reserved_labelnames", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_sum": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.metrics.Summary._sum", "name": "_sum", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.metrics.Summary._type", "name": "_type", "setter_type": null, "type": "builtins.str"}}, "observe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "amount"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Summary.observe", "name": "observe", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "amount"], "arg_types": ["prometheus_client.metrics.Summary", "builtins.float"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "observe of Summary", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.metrics.Summary.time", "name": "time", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.metrics.Summary"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "time of Summary", "ret_type": "prometheus_client.context_managers.Timer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.Summary.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prometheus_client.metrics.Summary", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.metrics.T", "name": "T", "upper_bound": "prometheus_client.metrics.MetricWrapperBase", "values": [], "variance": 0}}, "Timer": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.context_managers.Timer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.metrics.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.metrics.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.metrics.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.metrics.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.metrics.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.metrics.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_build_full_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["metric_type", "name", "namespace", "subsystem", "unit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics._build_full_name", "name": "_build_full_name", "original_first_arg": "metric_type", "type": null}}, "_get_use_created": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics._get_use_created", "name": "_get_use_created", "original_first_arg": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_use_created", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_use_created": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.metrics._use_created", "name": "_use_created", "setter_type": null, "type": "builtins.bool"}}, "_validate_exemplar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["exemplar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics._validate_exemplar", "name": "_validate_exemplar", "original_first_arg": "exemplar", "type": null}}, "_validate_labelname": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["l"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics._validate_labelname", "name": "_validate_labelname", "original_first_arg": "l", "type": null}}, "_validate_labelnames": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "labelnames"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics._validate_labelnames", "name": "_validate_labelnames", "original_first_arg": "cls", "type": null}}, "disable_created_metrics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.disable_created_metrics", "name": "disable_created_metrics", "original_first_arg": null, "type": null}}, "enable_created_metrics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.metrics.enable_created_metrics", "name": "enable_created_metrics", "original_first_arg": null, "type": null}}, "floatToGoString": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.utils.floatToGoString", "kind": "Gdef", "module_hidden": true, "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_hidden": true, "module_public": false}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_hidden": true, "module_public": false}, "values": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.values", "kind": "Gdef", "module_hidden": true, "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/prometheus_client/metrics.py"}