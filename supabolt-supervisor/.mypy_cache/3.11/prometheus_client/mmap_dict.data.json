{".class": "MypyFile", "_fullname": "prometheus_client.mmap_dict", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MmapedDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prometheus_client.mmap_dict.MmapedDict", "name": "MmapedDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prometheus_client.mmap_dict.MmapedDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prometheus_client.mmap_dict", "mro": ["prometheus_client.mmap_dict.MmapedDict", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "filename", "read_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.mmap_dict.MmapedDict.__init__", "name": "__init__", "original_first_arg": "self", "type": null}}, "_capacity": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.mmap_dict.MmapedDict._capacity", "name": "_capacity", "setter_type": null, "type": "builtins.int"}}, "_f": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.mmap_dict.MmapedDict._f", "name": "_f", "setter_type": null, "type": "typing.BinaryIO"}}, "_fname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.mmap_dict.MmapedDict._fname", "name": "_fname", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_init_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.mmap_dict.MmapedDict._init_value", "name": "_init_value", "original_first_arg": "self", "type": null}}, "_m": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.mmap_dict.MmapedDict._m", "name": "_m", "setter_type": null, "type": "mmap.mmap"}}, "_positions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.mmap_dict.MmapedDict._positions", "name": "_positions", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_read_all_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.mmap_dict.MmapedDict._read_all_values", "name": "_read_all_values", "original_first_arg": "self", "type": null}}, "_used": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prometheus_client.mmap_dict.MmapedDict._used", "name": "_used", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.mmap_dict.MmapedDict.close", "name": "close", "original_first_arg": "self", "type": null}}, "read_all_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.mmap_dict.MmapedDict.read_all_values", "name": "read_all_values", "original_first_arg": "self", "type": null}}, "read_all_values_from_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "prometheus_client.mmap_dict.MmapedDict.read_all_values_from_file", "name": "read_all_values_from_file", "original_first_arg": "filename", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "prometheus_client.mmap_dict.MmapedDict.read_all_values_from_file", "name": "read_all_values_from_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_all_values_from_file of MmapedDict", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "read_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.mmap_dict.MmapedDict.read_value", "name": "read_value", "original_first_arg": "self", "type": null}}, "write_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "key", "value", "timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.mmap_dict.MmapedDict.write_value", "name": "write_value", "original_first_arg": "self", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.mmap_dict.MmapedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prometheus_client.mmap_dict.MmapedDict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_INITIAL_MMAP_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.mmap_dict._INITIAL_MMAP_SIZE", "name": "_INITIAL_MMAP_SIZE", "setter_type": null, "type": "builtins.int"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.mmap_dict.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.mmap_dict.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.mmap_dict.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.mmap_dict.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.mmap_dict.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.mmap_dict.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_pack_integer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["data", "pos", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.mmap_dict._pack_integer", "name": "_pack_integer", "original_first_arg": "data", "type": null}}, "_pack_integer_func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.mmap_dict._pack_integer_func", "name": "_pack_integer_func", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["v"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pack_two_doubles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["data", "pos", "value", "timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.mmap_dict._pack_two_doubles", "name": "_pack_two_doubles", "original_first_arg": "data", "type": null}}, "_pack_two_doubles_func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.mmap_dict._pack_two_doubles_func", "name": "_pack_two_doubles_func", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["v"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_read_all_values": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["data", "used"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.mmap_dict._read_all_values", "name": "_read_all_values", "original_first_arg": "data", "type": null}}, "_unpack_integer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.mmap_dict._unpack_integer", "name": "_unpack_integer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["buffer", "offset"], "arg_types": ["typing_extensions.Buffer", "builtins.int"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unpack_two_doubles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.mmap_dict._unpack_two_doubles", "name": "_unpack_two_doubles", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["buffer", "offset"], "arg_types": ["typing_extensions.Buffer", "builtins.int"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_hidden": true, "module_public": false}, "mmap": {".class": "SymbolTableNode", "cross_ref": "mmap", "kind": "Gdef", "module_hidden": true, "module_public": false}, "mmap_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["metric_name", "name", "labelnames", "labelvalues", "help_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.mmap_dict.mmap_key", "name": "mmap_key", "original_first_arg": "metric_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["metric_name", "name", "labelnames", "labelvalues", "help_text"], "arg_types": ["builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mmap_key", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/prometheus_client/mmap_dict.py"}