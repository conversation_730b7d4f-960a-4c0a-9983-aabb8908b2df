{".class": "MypyFile", "_fullname": "prometheus_client.gc_collector", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Collector": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.registry.Collector", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CollectorRegistry": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.registry.CollectorRegistry", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CounterMetricFamily": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.metrics_core.CounterMetricFamily", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GCCollector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prometheus_client.registry.Collector"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prometheus_client.gc_collector.GCCollector", "name": "GCCollector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prometheus_client.gc_collector.GCCollector", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prometheus_client.gc_collector", "mro": ["prometheus_client.gc_collector.GCCollector", "prometheus_client.registry.Collector", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "registry"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.gc_collector.GCCollector.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "registry"], "arg_types": ["prometheus_client.gc_collector.GCCollector", "prometheus_client.registry.CollectorRegistry"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of GCCollector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.gc_collector.GCCollector.collect", "name": "collect", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.gc_collector.GCCollector"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "collect of GCCollector", "ret_type": {".class": "Instance", "args": ["prometheus_client.metrics_core.Metric"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.gc_collector.GCCollector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prometheus_client.gc_collector.GCCollector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GC_COLLECTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.gc_collector.GC_COLLECTOR", "name": "GC_COLLECTOR", "setter_type": null, "type": "prometheus_client.gc_collector.GCCollector"}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Metric": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.metrics_core.Metric", "kind": "Gdef", "module_hidden": true, "module_public": false}, "REGISTRY": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.registry.REGISTRY", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.gc_collector.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.gc_collector.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.gc_collector.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.gc_collector.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.gc_collector.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.gc_collector.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "gc": {".class": "SymbolTableNode", "cross_ref": "gc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "platform": {".class": "SymbolTableNode", "cross_ref": "platform", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/prometheus_client/gc_collector.py"}