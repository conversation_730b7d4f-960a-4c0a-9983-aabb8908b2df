{".class": "MypyFile", "_fullname": "prometheus_client.exposition", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseHTTPRequestHandler": {".class": "SymbolTableNode", "cross_ref": "http.server.BaseHTTPRequestHandler", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseHandler": {".class": "SymbolTableNode", "cross_ref": "urllib.request.BaseHandler", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CONTENT_TYPE_LATEST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.exposition.CONTENT_TYPE_LATEST", "name": "CONTENT_TYPE_LATEST", "setter_type": null, "type": "builtins.str"}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CollectorRegistry": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.registry.CollectorRegistry", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HTTPError": {".class": "SymbolTableNode", "cross_ref": "urllib.error.HTTPError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HTTPHandler": {".class": "SymbolTableNode", "cross_ref": "urllib.request.HTTPHandler", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HTTPRedirectHandler": {".class": "SymbolTableNode", "cross_ref": "urllib.request.HTTPRedirectHandler", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HTTPSHandler": {".class": "SymbolTableNode", "cross_ref": "urllib.request.HTTPSHandler", "kind": "Gdef", "module_hidden": true, "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MetricsHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.server.BaseHTTPRequestHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prometheus_client.exposition.MetricsHandler", "name": "MetricsHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.MetricsHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prometheus_client.exposition", "mro": ["prometheus_client.exposition.MetricsHandler", "http.server.BaseHTTPRequestHandler", "socketserver.StreamRequestHandler", "socketserver.BaseRequestHandler", "builtins.object"], "names": {".class": "SymbolTable", "do_GET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.exposition.MetricsHandler.do_GET", "name": "do_GET", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prometheus_client.exposition.MetricsHandler"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "do_GET of MetricsHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "registry"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "prometheus_client.exposition.MetricsHandler.factory", "name": "factory", "original_first_arg": "cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "registry"], "arg_types": [{".class": "TypeType", "item": "prometheus_client.exposition.MetricsHandler"}, "prometheus_client.registry.CollectorRegistry"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "factory of MetricsHandler", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "prometheus_client.exposition.MetricsHandler.factory", "name": "factory", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "registry"], "arg_types": [{".class": "TypeType", "item": "prometheus_client.exposition.MetricsHandler"}, "prometheus_client.registry.CollectorRegistry"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "factory of MetricsHandler", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "log_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "format", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "prometheus_client.exposition.MetricsHandler.log_message", "name": "log_message", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "format", "args"], "arg_types": ["prometheus_client.exposition.MetricsHandler", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "log_message of MetricsHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "registry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "prometheus_client.exposition.MetricsHandler.registry", "name": "registry", "setter_type": null, "type": "prometheus_client.registry.CollectorRegistry"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.exposition.MetricsHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prometheus_client.exposition.MetricsHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "REGISTRY": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.registry.REGISTRY", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Request": {".class": "SymbolTableNode", "cross_ref": "urllib.request.Request", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ThreadingMixIn": {".class": "SymbolTableNode", "cross_ref": "socketserver.ThreadingMixIn", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ThreadingWSGIServer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["socketserver.ThreadingMixIn", "wsgiref.simple_server.WSGIServer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prometheus_client.exposition.ThreadingWSGIServer", "name": "ThreadingWSGIServer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.ThreadingWSGIServer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prometheus_client.exposition", "mro": ["prometheus_client.exposition.ThreadingWSGIServer", "socketserver.ThreadingMixIn", "wsgiref.simple_server.WSGIServer", "http.server.HTTPServer", "socketserver.TCPServer", "socketserver.BaseServer", "builtins.object"], "names": {".class": "SymbolTable", "daemon_threads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.exposition.ThreadingWSGIServer.daemon_threads", "name": "daemon_threads", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.exposition.ThreadingWSGIServer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prometheus_client.exposition.ThreadingWSGIServer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WSGIRequestHandler": {".class": "SymbolTableNode", "cross_ref": "wsgiref.simple_server.WSGIRequestHandler", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WSGIServer": {".class": "SymbolTableNode", "cross_ref": "wsgiref.simple_server.WSGIServer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_PrometheusRedirectHandler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib.request.HTTPRedirectHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prometheus_client.exposition._PrometheusRedirectHandler", "name": "_PrometheusRedirectHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition._PrometheusRedirectHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prometheus_client.exposition", "mro": ["prometheus_client.exposition._PrometheusRedirectHandler", "urllib.request.HTTPRedirectHandler", "urllib.request.BaseHandler", "builtins.object"], "names": {".class": "SymbolTable", "redirect_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "req", "fp", "code", "msg", "headers", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition._PrometheusRedirectHandler.redirect_request", "name": "redirect_request", "original_first_arg": "self", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.exposition._PrometheusRedirectHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prometheus_client.exposition._PrometheusRedirectHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SilentHandler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wsgiref.simple_server.WSGIRequestHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prometheus_client.exposition._SilentHandler", "name": "_<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition._SilentHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prometheus_client.exposition", "mro": ["prometheus_client.exposition._SilentHandler", "wsgiref.simple_server.WSGIRequestHandler", "http.server.BaseHTTPRequestHandler", "socketserver.StreamRequestHandler", "socketserver.BaseRequestHandler", "builtins.object"], "names": {".class": "SymbolTable", "log_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "format", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition._SilentHandler.log_message", "name": "log_message", "original_first_arg": "self", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prometheus_client.exposition._SilentHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prometheus_client.exposition._SilentHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.exposition.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.exposition.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.exposition.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.exposition.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.exposition.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.exposition.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prometheus_client.exposition.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_bake_output": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["registry", "accept_header", "accept_encoding_header", "params", "disable_compression"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition._bake_output", "name": "_bake_output", "original_first_arg": "registry", "type": null}}, "_escape_grouping_key": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["k", "v"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition._escape_grouping_key", "name": "_escape_grouping_key", "original_first_arg": "k", "type": null}}, "_get_best_family": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["address", "port"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition._get_best_family", "name": "_get_best_family", "original_first_arg": "address", "type": null}}, "_get_ssl_ctx": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["certfile", "keyfile", "protocol", "cafile", "<PERSON><PERSON>", "client_auth_required"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition._get_ssl_ctx", "name": "_get_ssl_ctx", "original_first_arg": "certfile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["certfile", "keyfile", "protocol", "cafile", "<PERSON><PERSON>", "client_auth_required"], "arg_types": ["builtins.str", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_ssl_ctx", "ret_type": "ssl.SSLContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["url", "method", "timeout", "headers", "data", "base_handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition._make_handler", "name": "_make_handler", "original_first_arg": "url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["url", "method", "timeout", "headers", "data", "base_handler"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bytes", {".class": "UnionType", "items": ["urllib.request.BaseHandler", "builtins.type"], "uses_pep604_syntax": false}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make_handler", "ret_type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_use_gateway": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["method", "gateway", "job", "registry", "grouping_key", "timeout", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition._use_gateway", "name": "_use_gateway", "original_first_arg": "method", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["method", "gateway", "job", "registry", "grouping_key", "timeout", "handler"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["prometheus_client.registry.CollectorRegistry", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_use_gateway", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "basic_auth_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["url", "method", "timeout", "headers", "data", "username", "password"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.basic_auth_handler", "name": "basic_auth_handler", "original_first_arg": "url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["url", "method", "timeout", "headers", "data", "username", "password"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bytes", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "basic_auth_handler", "ret_type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_opener": {".class": "SymbolTableNode", "cross_ref": "urllib.request.build_opener", "kind": "Gdef", "module_hidden": true, "module_public": false}, "choose_encoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["accept_header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.choose_encoder", "name": "choose_encoder", "original_first_arg": "accept_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["accept_header"], "arg_types": ["builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "choose_encoder", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prometheus_client.registry.CollectorRegistry"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "closing": {".class": "SymbolTableNode", "cross_ref": "contextlib.closing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "default_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["url", "method", "timeout", "headers", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.default_handler", "name": "default_handler", "original_first_arg": "url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["url", "method", "timeout", "headers", "data"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bytes"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "default_handler", "ret_type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_from_gateway": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["gateway", "job", "grouping_key", "timeout", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.delete_from_gateway", "name": "delete_from_gateway", "original_first_arg": "gateway", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["gateway", "job", "grouping_key", "timeout", "handler"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_from_gateway", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "floatToGoString": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.utils.floatToGoString", "kind": "Gdef", "module_hidden": true, "module_public": false}, "generate_latest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["registry"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.generate_latest", "name": "generate_latest", "original_first_arg": "registry", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["registry"], "arg_types": ["prometheus_client.registry.CollectorRegistry"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate_latest", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gzip": {".class": "SymbolTableNode", "cross_ref": "gzip", "kind": "Gdef", "module_hidden": true, "module_public": false}, "gzip_accepted": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["accept_encoding_header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.gzip_accepted", "name": "gzip_accepted", "original_first_arg": "accept_encoding_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["accept_encoding_header"], "arg_types": ["builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "gzip_accepted", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "instance_ip_grouping_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.instance_ip_grouping_key", "name": "instance_ip_grouping_key", "original_first_arg": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "instance_ip_grouping_key", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_asgi_app": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.asgi.make_asgi_app", "kind": "Gdef"}, "make_server": {".class": "SymbolTableNode", "cross_ref": "wsgiref.simple_server.make_server", "kind": "Gdef", "module_hidden": true, "module_public": false}, "make_wsgi_app": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["registry", "disable_compression"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.make_wsgi_app", "name": "make_wsgi_app", "original_first_arg": "registry", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["registry", "disable_compression"], "arg_types": ["prometheus_client.registry.CollectorRegistry", "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_wsgi_app", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "openmetrics": {".class": "SymbolTableNode", "cross_ref": "prometheus_client.openmetrics.exposition", "kind": "Gdef", "module_hidden": true, "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "parse_qs": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.parse_qs", "kind": "Gdef", "module_hidden": true, "module_public": false}, "passthrough_redirect_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["url", "method", "timeout", "headers", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.passthrough_redirect_handler", "name": "passthrough_redirect_handler", "original_first_arg": "url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["url", "method", "timeout", "headers", "data"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bytes"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "passthrough_redirect_handler", "ret_type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "push_to_gateway": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["gateway", "job", "registry", "grouping_key", "timeout", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.push_to_gateway", "name": "push_to_gateway", "original_first_arg": "gateway", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["gateway", "job", "registry", "grouping_key", "timeout", "handler"], "arg_types": ["builtins.str", "builtins.str", "prometheus_client.registry.CollectorRegistry", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "push_to_gateway", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pushadd_to_gateway": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["gateway", "job", "registry", "grouping_key", "timeout", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.pushadd_to_gateway", "name": "pushadd_to_gateway", "original_first_arg": "gateway", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["gateway", "job", "registry", "grouping_key", "timeout", "handler"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["prometheus_client.registry.CollectorRegistry", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pushadd_to_gateway", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "quote_plus": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.quote_plus", "kind": "Gdef", "module_hidden": true, "module_public": false}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef", "module_hidden": true, "module_public": false}, "start_http_server": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "prometheus_client.exposition.start_http_server", "name": "start_http_server", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["port", "addr", "registry", "certfile", "keyfile", "client_cafile", "client_capath", "protocol", "client_auth_required"], "arg_types": ["builtins.int", "builtins.str", "prometheus_client.registry.CollectorRegistry", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": ["wsgiref.simple_server.WSGIServer", "threading.Thread"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_wsgi_server": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["port", "addr", "registry", "certfile", "keyfile", "client_cafile", "client_capath", "protocol", "client_auth_required"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.start_wsgi_server", "name": "start_wsgi_server", "original_first_arg": "port", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["port", "addr", "registry", "certfile", "keyfile", "client_cafile", "client_capath", "protocol", "client_auth_required"], "arg_types": ["builtins.int", "builtins.str", "prometheus_client.registry.CollectorRegistry", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "start_wsgi_server", "ret_type": {".class": "TupleType", "implicit": false, "items": ["wsgiref.simple_server.WSGIServer", "threading.Thread"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tls_auth_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["url", "method", "timeout", "headers", "data", "certfile", "keyfile", "cafile", "protocol", "insecure_skip_verify"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.tls_auth_handler", "name": "tls_auth_handler", "original_first_arg": "url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["url", "method", "timeout", "headers", "data", "certfile", "keyfile", "cafile", "protocol", "insecure_skip_verify"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bytes", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tls_auth_handler", "ret_type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef", "module_hidden": true, "module_public": false}, "write_to_textfile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["path", "registry"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prometheus_client.exposition.write_to_textfile", "name": "write_to_textfile", "original_first_arg": "path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["path", "registry"], "arg_types": ["builtins.str", "prometheus_client.registry.CollectorRegistry"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "write_to_textfile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/prometheus_client/exposition.py"}