{"data_mtime": 1762142166, "dep_lines": [11, 12, 13, 17, 18, 19, 1, 2, 3, 4, 5, 9, 11, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 10, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["prometheus_client.values", "prometheus_client.context_managers", "prometheus_client.metrics_core", "prometheus_client.registry", "prometheus_client.samples", "prometheus_client.utils", "os", "threading", "time", "types", "typing", "warnings", "prometheus_client", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc", "re", "typing_extensions"], "hash": "05b585b92aa6e43ef138fc15069779e55fbed519", "id": "prometheus_client.metrics", "ignore_all": true, "interface_hash": "f7d22eebf3ef0d721982cc1fe1283f8a7f80f46e", "mtime": 1762056682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "fixed_format_cache": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_equality_for_none": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/prometheus_client/metrics.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": false, "init_typed": false, "warn_required_dynamic_aliases": false}, null], "size": 28001, "suppressed": [], "version_id": "1.18.2"}