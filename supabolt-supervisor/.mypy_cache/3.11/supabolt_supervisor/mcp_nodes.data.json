{".class": "MypyFile", "_fullname": "supa<PERSON>_supervisor.mcp_nodes", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DataAvailability": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.state.DataAvailability", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FactualObservation": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.state.FactualObservation", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GraphState": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.graph_state.GraphState", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "supabolt_supervisor.mcp_nodes.LOGGER", "name": "LOGGER", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "MCPToolsetManager": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.mcp.manager.MCPToolsetManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PartialGraphState": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.graph_state.PartialGraphState", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RegulatoryPattern": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.state.RegulatoryPattern", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.ValidationError", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.mcp_nodes.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.mcp_nodes.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.mcp_nodes.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.mcp_nodes.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.mcp_nodes.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.mcp_nodes.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "build_agent_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["manager", "agent", "tool"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "supabolt_supervisor.mcp_nodes.build_agent_node", "name": "build_agent_node", "original_first_arg": "manager", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["manager", "agent", "tool"], "arg_types": ["supabolt_supervisor.mcp.manager.MCPToolsetManager", "builtins.str", "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_agent_node", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "supabolt_supervisor.graph_state.GraphState"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "supabolt_supervisor.graph_state.PartialGraphState"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "record_agent_call": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.utils.metrics.record_agent_call", "kind": "Gdef", "module_hidden": true, "module_public": false}, "record_node_failure": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.utils.metrics.record_node_failure", "kind": "Gdef", "module_hidden": true, "module_public": false}, "structlog": {".class": "SymbolTableNode", "cross_ref": "structlog", "kind": "Gdef", "module_hidden": true, "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "supabolt_supervisor/mcp_nodes.py"}