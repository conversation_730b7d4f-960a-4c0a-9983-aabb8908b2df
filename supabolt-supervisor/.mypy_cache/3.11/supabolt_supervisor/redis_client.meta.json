{"data_mtime": 1762066803, "dep_lines": [5, 7, 1, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["redis.asyncio", "supabolt_supervisor.settings", "__future__", "typing", "redis", "builtins", "_frozen_importlib", "abc", "redis.asyncio.client", "redis.asyncio.connection", "redis.asyncio.retry", "redis.client", "redis.commands", "redis.commands.core", "redis.commands.redismodules", "redis.commands.sentinel", "redis.credentials", "redis.exceptions"], "hash": "0fb8374bbdd95ae8970612e19f8b7ef5afc2ec62", "id": "supa<PERSON>_supervisor.redis_client", "ignore_all": false, "interface_hash": "703121857a9358348c3898b04e97ea00e6245142", "mtime": 1762066754, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "fixed_format_cache": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_equality_for_none": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "supabolt_supervisor/redis_client.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": false, "init_typed": false, "warn_required_dynamic_aliases": false}, null], "size": 309, "suppressed": [], "version_id": "1.18.2"}