{"data_mtime": 1762142166, "dep_lines": [4, 1, 3, 5, 7, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "__future__", "time", "contextlib", "prometheus_client", "builtins", "_frozen_importlib", "_typeshed", "abc", "prometheus_client.metrics", "prometheus_client.registry", "typing"], "hash": "bcf7a277b21246aeeb5327b6035836ef1f675583", "id": "supabolt_supervisor.utils.metrics", "ignore_all": false, "interface_hash": "670aa12c33af8a8587967ec68bb2c1de205c1797", "mtime": 1762141602, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "fixed_format_cache": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_equality_for_none": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/code/supabolt/supabolt-supervisor/supabolt_supervisor/utils/metrics.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": false, "init_typed": false, "warn_required_dynamic_aliases": false}, null], "size": 2359, "suppressed": [], "version_id": "1.18.2"}