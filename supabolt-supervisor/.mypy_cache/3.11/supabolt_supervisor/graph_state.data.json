{".class": "MypyFile", "_fullname": "supabolt_supervisor.graph_state", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing.Annotated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DataAvailability": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.state.DataAvailability", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FactualObservation": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.state.FactualObservation", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GraphState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "supabolt_supervisor.graph_state.GraphState", "name": "GraphState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "supabolt_supervisor.graph_state.GraphState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "supabolt_supervisor.graph_state", "mro": ["supabolt_supervisor.graph_state.GraphState", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["factual_observations", {".class": "Instance", "args": ["supabolt_supervisor.state.FactualObservation"], "extra_attrs": null, "type_ref": "builtins.list"}], ["regulatory_patterns", {".class": "Instance", "args": ["supabolt_supervisor.state.RegulatoryPattern"], "extra_attrs": null, "type_ref": "builtins.list"}], ["data_availability", {".class": "Instance", "args": ["supabolt_supervisor.state.DataAvailability"], "extra_attrs": null, "type_ref": "builtins.list"}], ["tool_results", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], ["tool_errors", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], ["requires_hitl", "builtins.bool"], ["hitl_reason", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["runtime", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}]], "readonly_keys": [], "required_keys": []}}}, "PartialGraphState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "supabolt_supervisor.graph_state.PartialGraphState", "line": 32, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "supabolt_supervisor.graph_state.GraphState"}}}, "RegulatoryPattern": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.state.RegulatoryPattern", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.graph_state.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.graph_state.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.graph_state.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.graph_state.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.graph_state.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.graph_state.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "append_items": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.state.append_items", "kind": "Gdef", "module_hidden": true, "module_public": false}, "merge_dict": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.state.merge_dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "reduce_hitl_reason": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.state.reduce_hitl_reason", "kind": "Gdef", "module_hidden": true, "module_public": false}, "reduce_requires_hitl": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.state.reduce_requires_hitl", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "supabolt_supervisor/graph_state.py"}