{"data_mtime": 1762202417, "dep_lines": [12, 3, 7, 8, 10, 11, 1, 4, 5, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["supabolt_supervisor.utils.metrics", "collections.abc", "langgraph.types", "langchain_core.runnables", "supabolt_supervisor.graph_state", "supabolt_supervisor.toolsets", "__future__", "dataclasses", "typing", "builtins", "_frozen_importlib", "abc", "langchain_core", "langchain_core.runnables.base"], "hash": "8360b52f72346b3931c8102d70d094ece3d806ba", "id": "supabolt_supervisor.routing", "ignore_all": true, "interface_hash": "34bf56493c9b78c95f0b9471d30e865934d18d4b", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "fixed_format_cache": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_equality_for_none": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/code/supabolt/supabolt-supervisor/supabolt_supervisor/routing.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": false, "init_typed": false, "warn_required_dynamic_aliases": false}, null], "size": 2904, "suppressed": [], "version_id": "1.18.2"}