{".class": "MypyFile", "_fullname": "supabolt_supervisor.routing", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GraphState": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.graph_state.GraphState", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RouterConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "supabolt_supervisor.routing.RouterConfig", "name": "RouterConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "supabolt_supervisor.routing.RouterConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 19, "name": "toolsets", "type": {".class": "Instance", "args": ["builtins.str", "supabolt_supervisor.toolsets.ToolsetConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 20, "name": "default_toolset", "type": "builtins.str"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "supabolt_supervisor.routing", "mro": ["supabolt_supervisor.routing.RouterConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "supabolt_supervisor.routing.RouterConfig.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "toolsets", "default_toolset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "supabolt_supervisor.routing.RouterConfig.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "toolsets", "default_toolset"], "arg_types": ["supabolt_supervisor.routing.RouterConfig", {".class": "Instance", "args": ["builtins.str", "supabolt_supervisor.toolsets.ToolsetConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RouterConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "supabolt_supervisor.routing.RouterConfig.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "toolsets"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "default_toolset"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["toolsets", "default_toolset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "supabolt_supervisor.routing.RouterConfig.__mypy-replace", "name": "__mypy-replace", "original_first_arg": "toolsets", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["toolsets", "default_toolset"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "supabolt_supervisor.toolsets.ToolsetConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of RouterConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "supabolt_supervisor.routing.RouterConfig.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["toolsets", "default_toolset"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "supabolt_supervisor.toolsets.ToolsetConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of RouterConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "supabolt_supervisor.routing.RouterConfig.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "default_toolset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "supabolt_supervisor.routing.RouterConfig.default_toolset", "name": "default_toolset", "setter_type": null, "type": "builtins.str"}}, "resolve_agent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "agent_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "supabolt_supervisor.routing.RouterConfig.resolve_agent", "name": "resolve_agent", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "agent_name"], "arg_types": ["supabolt_supervisor.routing.RouterConfig", "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resolve_agent of RouterConfig", "ret_type": "supabolt_supervisor.toolsets.ToolsetAgentConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve_toolset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "requested"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "supabolt_supervisor.routing.RouterConfig.resolve_toolset", "name": "resolve_toolset", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "requested"], "arg_types": ["supabolt_supervisor.routing.RouterConfig", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resolve_toolset of RouterConfig", "ret_type": "supabolt_supervisor.toolsets.ToolsetConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "toolsets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "supabolt_supervisor.routing.RouterConfig.toolsets", "name": "toolsets", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "supabolt_supervisor.toolsets.ToolsetConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "supabolt_supervisor.routing.RouterConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "supabolt_supervisor.routing.RouterConfig", "values": [], "variance": 0}, "slots": ["default_toolset", "toolsets"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RunnableLambda": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.base.RunnableLambda", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Send": {".class": "SymbolTableNode", "cross_ref": "langgraph.types.Send", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ToolsetAgentConfig": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.toolsets.ToolsetAgentConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ToolsetConfig": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.toolsets.ToolsetConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.routing.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.routing.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.routing.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.routing.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.routing.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.routing.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "build_router_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["router_cfg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "supabolt_supervisor.routing.build_router_node", "name": "build_router_node", "original_first_arg": "router_cfg", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["router_cfg"], "arg_types": ["supabolt_supervisor.routing.RouterConfig"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_router_node", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.RunnableLambda"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_hidden": true, "module_public": false}, "record_router_fanout": {".class": "SymbolTableNode", "cross_ref": "supabolt_supervisor.utils.metrics.record_router_fanout", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Users/<USER>/code/supabolt/supabolt-supervisor/supabolt_supervisor/routing.py"}