{".class": "MypyFile", "_fullname": "supa<PERSON>_supervisor.mcp_client", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MCPClient": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "fastmcp.MCPClient", "name": "MCPClient", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "MCPServerConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "supa<PERSON>_supervisor.mcp_client.MCPServerConfig", "name": "MCPServerConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "supa<PERSON>_supervisor.mcp_client.MCPServerConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 12, "name": "name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 13, "name": "spawn", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 14, "name": "cwd", "type": "pathlib.Path"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 15, "name": "health_url", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 16, "name": "timeout_seconds", "type": "builtins.float"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "supa<PERSON>_supervisor.mcp_client", "mro": ["supa<PERSON>_supervisor.mcp_client.MCPServerConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "supabolt_supervisor.mcp_client.MCPServerConfig.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "spawn", "cwd", "health_url", "timeout_seconds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "supa<PERSON>_supervisor.mcp_client.MCPServerConfig.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "spawn", "cwd", "health_url", "timeout_seconds"], "arg_types": ["supa<PERSON>_supervisor.mcp_client.MCPServerConfig", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "pathlib.Path", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MCPServerConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "supabolt_supervisor.mcp_client.MCPServerConfig.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "spawn"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cwd"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "health_url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timeout_seconds"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["name", "spawn", "cwd", "health_url", "timeout_seconds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "supa<PERSON>_supervisor.mcp_client.MCPServerConfig.__mypy-replace", "name": "__mypy-replace", "original_first_arg": "name", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["name", "spawn", "cwd", "health_url", "timeout_seconds"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "pathlib.Path", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of MCPServerConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "supa<PERSON>_supervisor.mcp_client.MCPServerConfig.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["name", "spawn", "cwd", "health_url", "timeout_seconds"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "pathlib.Path", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of MCPServerConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "cwd": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "supabolt_supervisor.mcp_client.MCPServerConfig.cwd", "name": "cwd", "setter_type": null, "type": "pathlib.Path"}}, "health_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "supabolt_supervisor.mcp_client.MCPServerConfig.health_url", "name": "health_url", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "supabolt_supervisor.mcp_client.MCPServerConfig.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "spawn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "supabolt_supervisor.mcp_client.MCPServerConfig.spawn", "name": "spawn", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "timeout_seconds": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "supa<PERSON>_supervisor.mcp_client.MCPServerConfig.timeout_seconds", "name": "timeout_seconds", "setter_type": null, "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "supa<PERSON>_supervisor.mcp_client.MCPServerConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "supa<PERSON>_supervisor.mcp_client.MCPServerConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MCPToolsetManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "supa<PERSON>_supervisor.mcp_client.MCPToolsetManager", "name": "MCPToolsetManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "supa<PERSON>_supervisor.mcp_client.MCPToolsetManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "supa<PERSON>_supervisor.mcp_client", "mro": ["supa<PERSON>_supervisor.mcp_client.MCPToolsetManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "servers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "supa<PERSON>_supervisor.mcp_client.MCPToolsetManager.__init__", "name": "__init__", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "servers"], "arg_types": ["supa<PERSON>_supervisor.mcp_client.MCPToolsetManager", {".class": "Instance", "args": ["builtins.str", "supa<PERSON>_supervisor.mcp_client.MCPServerConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MCPToolsetManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_clients": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "supa<PERSON>_supervisor.mcp_client.MCPToolsetManager._clients", "name": "_clients", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_servers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "supa<PERSON>_supervisor.mcp_client.MCPToolsetManager._servers", "name": "_servers", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "supa<PERSON>_supervisor.mcp_client.MCPServerConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ensure_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "supabolt_supervisor.mcp_client.MCPToolsetManager.ensure_client", "name": "ensure_client", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["supa<PERSON>_supervisor.mcp_client.MCPToolsetManager", "builtins.str"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ensure_client of MCPToolsetManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "supa<PERSON>_supervisor.mcp_client.MCPToolsetManager.shutdown", "name": "shutdown", "original_first_arg": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["supa<PERSON>_supervisor.mcp_client.MCPToolsetManager"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "shutdown of MCPToolsetManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "supa<PERSON>_supervisor.mcp_client.MCPToolsetManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "supa<PERSON>_supervisor.mcp_client.MCPToolsetManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.mcp_client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supa<PERSON>_supervisor.mcp_client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.mcp_client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supa<PERSON>_supervisor.mcp_client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.mcp_client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "supabolt_supervisor.mcp_client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_hidden": true, "module_public": false}, "load_mcp_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "supabolt_supervisor.mcp_client.load_mcp_config", "name": "load_mcp_config", "original_first_arg": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["pathlib.Path"], "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load_mcp_config", "ret_type": {".class": "Instance", "args": ["builtins.str", "supa<PERSON>_supervisor.mcp_client.MCPServerConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "yaml": {".class": "SymbolTableNode", "cross_ref": "yaml", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "supabolt_supervisor/mcp_client.py"}