{"data_mtime": 1762142165, "dep_lines": [2, 8, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 20, 10, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["email.message", "collections.abc", "_socket", "email", "io", "socketserver", "sys", "_ssl", "_typeshed", "ssl", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_io", "abc", "os", "socket", "types"], "hash": "32d992b02aa1f34161a6571c3b1fa082c5589b65", "id": "http.server", "ignore_all": true, "interface_hash": "b5bcfb9df82c788daf274af90d3b3bfbed1a5f4a", "mtime": 1762057473, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "fixed_format_cache": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_equality_for_none": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/mypy/typeshed/stdlib/http/server.pyi", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": false, "init_typed": false, "warn_required_dynamic_aliases": false}, null], "size": 5726, "suppressed": [], "version_id": "1.18.2"}