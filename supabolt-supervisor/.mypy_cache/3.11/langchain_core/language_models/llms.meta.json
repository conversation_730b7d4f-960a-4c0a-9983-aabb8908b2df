{"data_mtime": 1762063044, "dep_lines": [45, 57, 14, 24, 35, 36, 44, 50, 51, 54, 55, 56, 6, 8, 9, 10, 11, 12, 13, 15, 16, 22, 23, 24, 33, 60, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.language_models.base", "langchain_core.runnables.config", "collections.abc", "tenacity.retry", "langchain_core.caches", "langchain_core.callbacks", "langchain_core.globals", "langchain_core.load", "langchain_core.messages", "langchain_core.outputs", "langchain_core.prompt_values", "langchain_core.runnables", "__future__", "asyncio", "functools", "inspect", "json", "logging", "abc", "pathlib", "typing", "yaml", "pydantic", "tenacity", "typing_extensions", "uuid", "builtins", "_frozen_importlib", "langchain_core.callbacks.base", "langchain_core.callbacks.manager", "langchain_core.load.serializable", "langchain_core.messages.base", "langchain_core.outputs.generation", "langchain_core.outputs.llm_result", "langchain_core.runnables.base", "os", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.main", "re"], "hash": "8820ec0490c991ee8512c243446863259021933f", "id": "langchain_core.language_models.llms", "ignore_all": true, "interface_hash": "9fb4e30aa0614ec352ee1d51f9a3b1569e3151d0", "mtime": 1762056095, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "fixed_format_cache": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_equality_for_none": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/langchain_core/language_models/llms.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": false, "init_typed": false, "warn_required_dynamic_aliases": false}, null], "size": 53942, "suppressed": [], "version_id": "1.18.2"}