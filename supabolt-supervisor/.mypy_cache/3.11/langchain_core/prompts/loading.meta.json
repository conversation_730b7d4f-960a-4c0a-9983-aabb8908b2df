{"data_mtime": 1762063044, "dep_lines": [10, 11, 12, 13, 14, 5, 3, 4, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.output_parsers.string", "langchain_core.prompts.base", "langchain_core.prompts.chat", "langchain_core.prompts.few_shot", "langchain_core.prompts.prompt", "collections.abc", "json", "logging", "pathlib", "yaml", "builtins", "_frozen_importlib", "abc", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.prompts.string", "langchain_core.runnables", "langchain_core.runnables.base", "os", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "typing"], "hash": "b106fa21d0fdfd96b224b04acbc4d6c4b0a624d4", "id": "langchain_core.prompts.loading", "ignore_all": true, "interface_hash": "e09230a6c7a7e90c093c88e2c789af9557b33529", "mtime": 1762056095, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "fixed_format_cache": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_equality_for_none": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/langchain_core/prompts/loading.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": false, "init_typed": false, "warn_required_dynamic_aliases": false}, null], "size": 6889, "suppressed": [], "version_id": "1.18.2"}