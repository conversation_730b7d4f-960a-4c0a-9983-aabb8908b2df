{"data_mtime": 1762063044, "dep_lines": [25, 32, 33, 9, 23, 24, 26, 31, 36, 3, 5, 6, 7, 8, 10, 11, 19, 20, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.output_parsers.base", "langchain_core.runnables.config", "langchain_core.utils.pydantic", "collections.abc", "langchain_core.exceptions", "langchain_core.load", "langchain_core.prompt_values", "langchain_core.runnables", "langchain_core.documents", "__future__", "contextlib", "json", "typing", "abc", "functools", "pathlib", "yaml", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "annotated_types", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.documents.base", "langchain_core.load.serializable", "langchain_core.output_parsers", "langchain_core.runnables.base", "os", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.functional_validators", "pydantic.main", "pydantic.types", "pydantic_core", "pydantic_core.core_schema", "re", "types", "uuid"], "hash": "21a5256376f1082aae94f6c92da6ad45e0a41f7c", "id": "langchain_core.prompts.base", "ignore_all": true, "interface_hash": "ed5c6c76a8648a00d787a8df4196702d89e6e116", "mtime": 1762056095, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "fixed_format_cache": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_equality_for_none": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/supabolt-supervisor-_VEfLlkt-py3.11/lib/python3.11/site-packages/langchain_core/prompts/base.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": false, "init_typed": false, "warn_required_dynamic_aliases": false}, null], "size": 15725, "suppressed": [], "version_id": "1.18.2"}