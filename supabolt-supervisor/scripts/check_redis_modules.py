#!/usr/bin/env python3
"""Verify required Redis modules are available."""

import asyncio

from supabolt_supervisor.redis_client import RedisClientFactory
from supabolt_supervisor.settings import load_settings
from supabolt_supervisor.utils.redis import verify_redis_modules


async def main() -> None:
    settings = load_settings()
    redis = RedisClientFactory.build(settings)
    try:
        await verify_redis_modules(redis)
    finally:
        await redis.close()
    print("Redis modules ok")


if __name__ == "__main__":
    asyncio.run(main())
