#!/usr/bin/env bash

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"
ENV_FILE="${ENV_FILE:-${ROOT_DIR}/../.env}"
HOST="${HOST:-0.0.0.0}"
PORT="${PORT:-8080}"
RELOAD="${SUPABOLT_SUPERVISOR_RELOAD:-false}"

cd "${ROOT_DIR}"

poetry install >/dev/null

args=("api.server:app" "--host" "${HOST}" "--port" "${PORT}" "--env-file" "${ENV_FILE}")
[[ "${RELOAD}" == "true" ]] && args+=("--reload")
args+=("$@")

exec poetry run uvicorn "${args[@]}"
