#!/usr/bin/env bash

set -euo pipefail

if [[ ${DEBUG:-0} == 1 ]]; then
  set -x
fi

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "${SCRIPT_DIR}")"
ROOT_DIR="$(dirname "${PROJECT_ROOT}")"

if [[ ${SKIP_SUPERVISOR_INSTALL:-0} == 1 ]]; then
  echo "Skipping supabolt-supervisor install (SKIP_SUPERVISOR_INSTALL=1)."
  exit 0
fi

load_env() {
  local env_file="${ROOT_DIR}/.env"
  if [[ -f "${env_file}" ]]; then
    set -a
    # shellcheck disable=SC1091
    source "${env_file}"
    set +a
  fi
}

load_env

if ! command -v poetry >/dev/null 2>&1; then
  echo "Poetry is required to install supabolt-supervisor dependencies." >&2
  echo "Install Poetry from https://python-poetry.org/docs/#installation and re-run the installer." >&2
  exit 1
fi

# Allow callers to append custom Poetry arguments via POETRY_INSTALL_ARGS.
POETRY_ARGS=(--no-interaction)
if [[ -n "${POETRY_INSTALL_ARGS:-}" ]]; then
  # shellcheck disable=SC2206 # intentional word splitting to preserve extra args
  EXTRA_ARGS=(${POETRY_INSTALL_ARGS})
  POETRY_ARGS+=("${EXTRA_ARGS[@]}")
fi

pushd "${PROJECT_ROOT}" >/dev/null
echo "Installing supabolt-supervisor dependencies with Poetry"
poetry install "${POETRY_ARGS[@]}"
popd >/dev/null
