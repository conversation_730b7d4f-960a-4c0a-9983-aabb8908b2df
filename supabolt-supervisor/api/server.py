from __future__ import annotations

from typing import Any, cast

from fastapi import Depends, FastAPI, HTTPException, Response, status
from prometheus_client import CONTENT_TYPE_LATEST, REGISTRY, generate_latest
from pydantic import BaseModel, Field

from auth.supabase import require_tenant
from supabolt_supervisor.fact_sheet import build_fact_sheet
from supabolt_supervisor.graph import create_graph
from supabolt_supervisor.graph_state import GraphState
from supabolt_supervisor.mcp import MC<PERSON><PERSON><PERSON>setManager
from supabolt_supervisor.redis_client import RedisClientFactory
from supabolt_supervisor.settings import SupaboltSettings, load_settings
from supabolt_supervisor.utils.interrupts import LGInterrupt
from supabolt_supervisor.utils.redis import verify_redis_modules

app = FastAPI(title="Supabolt Supervisor API")

settings: SupaboltSettings | None = None
graph: Any | None = None
manager: MCPToolsetManager | None = None


@app.get("/health", tags=["system"])
async def health() -> dict[str, str]:
    return {"status": "ok"}


class StartWorkflowRequest(BaseModel):
    workflow_id: str
    entity: dict[str, Any]
    runtime_config: dict[str, Any] | None = None
    messages: list[dict[str, Any]] | None = None


class StartWorkflowResponse(BaseModel):
    workflow_id: str = Field(alias="workflowId")
    fact_sheet: dict[str, Any] = Field(alias="factSheet")
    tool_results: dict[str, Any] = Field(default_factory=dict, alias="toolResults")
    tool_errors: dict[str, str] = Field(default_factory=dict, alias="toolErrors")


@app.post("/workflows", tags=["workflows"])
async def start_workflow(
    payload: StartWorkflowRequest, tenant_id: str = Depends(require_tenant)
) -> StartWorkflowResponse:
    if graph is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Graph not ready",
        )
    if not payload.workflow_id:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="workflow_id required"
        )

    initial_state: GraphState = {
        "factual_observations": [],
        "regulatory_patterns": [],
        "data_availability": [],
        "tool_results": {},
        "tool_errors": {},
        "requires_hitl": False,
        "hitl_reason": None,
        "runtime": {
            "workflow_id": payload.workflow_id,
            "tenant_id": tenant_id,
            "entity": payload.entity,
            "runtime_config": payload.runtime_config or {},
            "messages": payload.messages or [],
        },
    }

    try:
        compiled_graph = cast(Any, graph)
        result_state = cast(
            GraphState,
            await compiled_graph.ainvoke(
                initial_state,
                config={
                    "configurable": {
                        "thread_id": payload.workflow_id,
                        "checkpoint_ns": tenant_id,
                    }
                },
            ),
        )
    except LGInterrupt as interrupt:
        payload_obj: dict[str, Any] | None = None
        if interrupt.args:
            first_arg = interrupt.args[0]
            if isinstance(first_arg, dict):
                payload_obj = first_arg
        if payload_obj and isinstance(payload_obj.get("state"), dict):
            result_state = cast(GraphState, payload_obj["state"])
        else:
            result_state = initial_state

    if not isinstance(result_state, dict):  # pragma: no cover - defensive
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Supervisor graph returned unexpected state",
        )

    fact_sheet = build_fact_sheet(result_state)
    tool_results = dict(result_state.get("tool_results", {}))
    tool_errors = dict(result_state.get("tool_errors", {}))

    runtime_state = result_state.get("runtime")
    workflow_id_override: str | None = None
    if isinstance(runtime_state, dict):
        candidate = runtime_state.get("workflow_id")
        if isinstance(candidate, str) and candidate:
            workflow_id_override = candidate
    workflow_identifier = workflow_id_override or payload.workflow_id

    return StartWorkflowResponse(
        workflowId=workflow_identifier,
        factSheet=fact_sheet,
        toolResults=tool_results,
        toolErrors=tool_errors,
    )


@app.get("/metrics", tags=["system"])
async def metrics() -> Response:
    payload = generate_latest(REGISTRY)
    return Response(payload, media_type=CONTENT_TYPE_LATEST)


@app.on_event("startup")
async def ensure_redis_modules() -> None:
    global settings, graph, manager
    settings = load_settings()
    redis_client = RedisClientFactory.build(settings)
    try:
        await verify_redis_modules(redis_client)
    finally:
        await redis_client.close()
    graph, manager = create_graph(settings, include_manager=True)


@app.on_event("shutdown")
async def shutdown_agents() -> None:
    global manager
    if manager is None:
        return
    try:
        await manager.shutdown()
    finally:
        manager = None
