name: CI

on:
  push:
    branches: [main]
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: Install Poetry
        uses: abatilo/actions-poetry@v2
        with:
          poetry-version: '1.8.3'
      - name: Install dependencies
        run: |
          poetry install
      - name: Lint
        run: |
          poetry run ruff check .
          poetry run black --check .
      - name: Type check
        run: poetry run mypy supabolt_supervisor api auth
      - name: Security (bandit)
        run: poetry run bandit -q -r supabolt_supervisor api auth
      - name: Dependency audit
        run: poetry run pip-audit
      - name: Tests
        run: poetry run pytest --cov=supabolt_supervisor --cov=api --cov=auth --cov-report=term-missing
