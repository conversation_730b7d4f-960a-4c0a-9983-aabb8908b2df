    [tool.poetry]
    name = "supabolt-supervisor"
    version = "0.1.0"
    description = "Supabolt LangGraph 1.x compliance supervisor with FastAPI, FastMCP, and Supabase authentication."
    authors = ["Supabolt Engineering <<EMAIL>>"]
    readme = "README.md"
    packages = [{ include = "supabolt_supervisor" }, { include = "api" }, { include = "auth" }]
    package-mode = false

    [tool.poetry.dependencies]
    python = "^3.11"
    langgraph = "1.0.2"
    langchain-core = "1.0.2"
    fastapi = "0.120.4"
    uvicorn = { version = "^0.31.1", extras = ["standard"] }
    redis = "^5.0.0"
    pydantic = "^2.8.0"
    pyyaml = "^6.0.0"
    python-jose = { version = "^3.3.0", extras = ["cryptography"] }
    structlog = "^24.1.0"
    prometheus-client = "^0.20.0"
    httpx = "^0.28.1"

    [tool.poetry.group.dev.dependencies]
    pytest = "^8.3.0"
    pytest-asyncio = "^0.23.0"
    coverage = "^7.6.0"
    ruff = "^0.4.0"
    black = "^24.4"
    mypy = "^1.10.0"
    bandit = "^1.7.0"
    pip-audit = "^2.7.0"
    types-redis = "^4.6.0.20240321"
    types-python-jose = "^3.3.4.20240106"
    types-PyYAML = "^6.0.12"

    [tool.poetry.scripts]
    supabolt-supervisor = "supabolt_supervisor.cli:main"

    [tool.black]
    line-length = 100
    target-version = ["py311"]

    [tool.ruff]
    line-length = 100
    target-version = "py311"

    [tool.ruff.lint]
    select = ["E", "F", "I", "UP", "ASYNC"]
    ignore = ["E203"]

    [tool.mypy]
    python_version = "3.11"
    warn_return_any = true
    warn_unused_configs = true
    strict = true
    plugins = ["pydantic.mypy"]

    [tool.pytest.ini_options]
    minversion = "8.0"
    addopts = "--strict-markers --strict-config --disable-warnings"
    testpaths = ["tests"]

    [build-system]
    requires = ["poetry-core>=1.8.0"]
    build-backend = "poetry.core.masonry.api"
