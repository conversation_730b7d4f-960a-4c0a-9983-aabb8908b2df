from __future__ import annotations

from typing import Any, cast

import pytest

from supa<PERSON>_supervisor.fact_sheet import build_fact_sheet
from supa<PERSON>_supervisor.graph import create_graph
from supabolt_supervisor.graph_state import GraphState
from supabolt_supervisor.mcp import MCPToolsetManager
from supabolt_supervisor.settings import SupaboltSettings
from supabolt_supervisor.utils.interrupts import LGInterrupt

GOLDEN_CASES = [
    {
        "id": "clean-bitcoin-address",
        "entity": {
            "entityId": "******************************************",
            "entityType": "bitcoin_address",
        },
        "runtime_config": {"toolset": "sanctions_screening"},
        "expected_agents": ["ofac-agent"],
        "expected_patterns": [],
        "requires_human_review": False,
    },
    {
        "id": "sanctioned-bitcoin-address",
        "entity": {
            "entityId": "**********************************",
            "entityType": "bitcoin_address",
        },
        "runtime_config": {"toolset": "sanctions_screening"},
        "expected_agents": ["ofac-agent"],
        "expected_patterns": ["SANCTIONS_MATCH"],
        "requires_human_review": True,
    },
    {
        "id": "clean-lightning-channel",
        "entity": {
            "channelId": "850000:2:1",
            "entityType": "lightning_node",
        },
        "runtime_config": {"toolset": "lightning_compliance"},
        "expected_agents": [
            "lightning-agent",
            "bitcoin-aws-agent",
            "ofac-agent",
            "analytics-agent",
        ],
        "expected_patterns": [],
        "requires_human_review": False,
    },
    {
        "id": "sanctioned-lightning-counterparty",
        "entity": {
            "channelId": "850001:3:2",
            "entityType": "lightning_node",
        },
        "runtime_config": {
            "toolset": "lightning_compliance",
            "agents": ["lightning-agent", "ofac-agent"],
        },
        "expected_agents": ["lightning-agent", "ofac-agent"],
        "expected_patterns": ["SANCTIONS_MATCH"],
        "requires_human_review": True,
    },
    {
        "id": "usdc-whale-address",
        "entity": {
            "entityId": "******************************************",
            "entityType": "usdc_address",
        },
        "runtime_config": {"toolset": "stablecoin_screening"},
        "expected_agents": ["usdc-agent", "ofac-agent", "analytics-agent"],
        "expected_patterns": ["HIGH_VALUE_CONCENTRATION"],
        "requires_human_review": True,
    },
    {
        "id": "high-velocity-pattern",
        "entity": {
            "entityId": "bc1qhighvelocity123456789",
            "entityType": "bitcoin_address",
        },
        "runtime_config": {"toolset": "standard_screening"},
        "expected_agents": ["bitcoin-aws-agent", "ofac-agent", "analytics-agent"],
        "expected_patterns": ["HIGH_VELOCITY"],
        "requires_human_review": True,
    },
    {
        "id": "mixing-service-usage",
        "entity": {
            "entityId": "bc1qmixingservice987654321",
            "entityType": "bitcoin_address",
        },
        "runtime_config": {"toolset": "standard_screening"},
        "expected_agents": ["bitcoin-aws-agent", "ofac-agent", "analytics-agent"],
        "expected_patterns": ["MIXING_SERVICE_INTERACTION"],
        "requires_human_review": True,
    },
    {
        "id": "cross-chain-movement",
        "entity": {
            "entityId": "0xcrosschainaddress123",
            "entityType": "usdc_address",
        },
        "runtime_config": {
            "toolset": "stablecoin_screening",
            "agents": ["usdc-agent", "analytics-agent"],
        },
        "expected_agents": ["usdc-agent", "analytics-agent"],
        "expected_patterns": ["CROSS_CHAIN_MOVEMENT"],
        "requires_human_review": True,
    },
    {
        "id": "clean-transaction",
        "entity": {
            "entityId": "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b",
            "entityType": "transaction",
        },
        "runtime_config": {"toolset": "standard_screening"},
        "expected_agents": ["bitcoin-aws-agent", "ofac-agent", "analytics-agent"],
        "expected_patterns": [],
        "requires_human_review": False,
    },
    {
        "id": "complex-investigation",
        "entity": {
            "entityId": "bc1qcomplex123investigation456",
            "entityType": "bitcoin_address",
        },
        "runtime_config": {"toolset": "standard_screening"},
        "expected_agents": ["bitcoin-aws-agent", "ofac-agent", "analytics-agent"],
        "expected_patterns": ["INDIRECT_EXPOSURE", "HIGH_VOLUME"],
        "requires_human_review": True,
    },
]


GOLDEN_RESPONSES = {
    "clean-bitcoin-address": {
        "ofac-agent": {
            "factualObservations": [
                {"metric": "sanctions.status", "value": "no_match", "source": "ofac-agent"},
                {"metric": "entity.type", "value": "bitcoin_address", "source": "ofac-agent"},
            ],
            "patterns": [],
            "requiresHumanReview": False,
        }
    },
    "sanctioned-bitcoin-address": {
        "ofac-agent": {
            "factualObservations": [
                {"metric": "sanctions.status", "value": "matched", "source": "ofac-agent"},
                {"metric": "sanctions.confidence", "value": "0.95", "source": "ofac-agent"},
            ],
            "patterns": [
                {
                    "pattern": "SANCTIONS_MATCH",
                    "reference": "OFAC",
                    "details": {"body": "U.S. Department of the Treasury"},
                }
            ],
            "requiresHumanReview": True,
            "hitlReason": "Positive sanctions match",
        }
    },
    "clean-lightning-channel": {
        "lightning-agent": {
            "factualObservations": [
                {"metric": "channel.capacity", "value": "0.04 BTC", "source": "lightning-agent"}
            ],
            "patterns": [],
        },
        "bitcoin-aws-agent": {
            "factualObservations": [
                {
                    "metric": "funding.transaction.status",
                    "value": "clean",
                    "source": "bitcoin-aws-agent",
                }
            ],
            "patterns": [],
        },
        "ofac-agent": {
            "factualObservations": [
                {"metric": "sanctions.status", "value": "no_match", "source": "ofac-agent"}
            ],
            "patterns": [],
        },
        "analytics-agent": {
            "factualObservations": [
                {
                    "metric": "risk.assessment",
                    "value": "no_structuring_detected",
                    "source": "analytics-agent",
                }
            ],
            "patterns": [],
        },
    },
    "sanctioned-lightning-counterparty": {
        "lightning-agent": {
            "factualObservations": [
                {
                    "metric": "remote.pubkey",
                    "value": "matches_sanctions_list",
                    "source": "lightning-agent",
                }
            ],
            "patterns": [],
        },
        "ofac-agent": {
            "factualObservations": [
                {"metric": "sanctions.status", "value": "match", "source": "ofac-agent"},
                {"metric": "sanctions.confidence", "value": "0.90", "source": "ofac-agent"},
            ],
            "patterns": [
                {
                    "pattern": "SANCTIONS_MATCH",
                    "reference": "FATF",
                    "details": {"body": "Financial Action Task Force"},
                }
            ],
            "requiresHumanReview": True,
            "hitlReason": "Lightning counterparty match",
        },
    },
    "usdc-whale-address": {
        "usdc-agent": {
            "factualObservations": [
                {"metric": "usdc.balance", "value": "4912468498.36", "source": "usdc-agent"}
            ],
            "patterns": [],
        },
        "ofac-agent": {
            "factualObservations": [
                {"metric": "sanctions.status", "value": "no_match", "source": "ofac-agent"}
            ],
            "patterns": [],
        },
        "analytics-agent": {
            "factualObservations": [
                {"metric": "entity.whale", "value": "true", "source": "analytics-agent"}
            ],
            "patterns": [
                {
                    "pattern": "HIGH_VALUE_CONCENTRATION",
                    "reference": "MiCA",
                    "details": {"body": "European Union"},
                }
            ],
            "requiresHumanReview": True,
            "hitlReason": "Stablecoin whale detected",
        },
    },
    "high-velocity-pattern": {
        "bitcoin-aws-agent": {
            "factualObservations": [
                {"metric": "transactions.last_24h", "value": "150", "source": "bitcoin-aws-agent"}
            ],
            "patterns": [],
        },
        "ofac-agent": {
            "factualObservations": [
                {"metric": "sanctions.status", "value": "no_match", "source": "ofac-agent"}
            ],
            "patterns": [],
        },
        "analytics-agent": {
            "factualObservations": [
                {"metric": "velocity.per_hour", "value": "150", "source": "analytics-agent"}
            ],
            "patterns": [
                {
                    "pattern": "HIGH_VELOCITY",
                    "reference": "FATF Recommendation 16",
                    "details": {"body": "Financial Action Task Force"},
                }
            ],
            "requiresHumanReview": True,
            "hitlReason": "High transaction velocity",
        },
    },
    "mixing-service-usage": {
        "bitcoin-aws-agent": {
            "factualObservations": [
                {
                    "metric": "counterparty.service",
                    "value": "tornado.cash",
                    "source": "bitcoin-aws-agent",
                }
            ],
            "patterns": [],
        },
        "ofac-agent": {
            "factualObservations": [
                {"metric": "sanctions.status", "value": "no_match", "source": "ofac-agent"}
            ],
            "patterns": [],
        },
        "analytics-agent": {
            "factualObservations": [
                {
                    "metric": "mixing.recent_interaction",
                    "value": "2 days",
                    "source": "analytics-agent",
                }
            ],
            "patterns": [
                {
                    "pattern": "MIXING_SERVICE_INTERACTION",
                    "reference": "FATF Recommendation 15",
                    "details": {"body": "Financial Action Task Force"},
                }
            ],
            "requiresHumanReview": True,
            "hitlReason": "Mixing service interaction",
        },
    },
    "cross-chain-movement": {
        "usdc-agent": {
            "factualObservations": [
                {
                    "metric": "chains.count",
                    "value": "3",
                    "source": "usdc-agent",
                }
            ],
            "patterns": [],
        },
        "analytics-agent": {
            "factualObservations": [
                {
                    "metric": "volume.24h",
                    "value": "$2500000",
                    "source": "analytics-agent",
                }
            ],
            "patterns": [
                {
                    "pattern": "CROSS_CHAIN_MOVEMENT",
                    "reference": "MiCA Article 59",
                    "details": {"body": "European Union"},
                }
            ],
            "requiresHumanReview": True,
            "hitlReason": "Cross-chain fund movement",
        },
    },
    "clean-transaction": {
        "bitcoin-aws-agent": {
            "factualObservations": [
                {"metric": "transaction.amount", "value": "50 BTC", "source": "bitcoin-aws-agent"}
            ],
            "patterns": [],
        },
        "ofac-agent": {
            "factualObservations": [
                {"metric": "sanctions.status", "value": "no_match", "source": "ofac-agent"}
            ],
            "patterns": [],
        },
        "analytics-agent": {
            "factualObservations": [
                {
                    "metric": "anomalies.detected",
                    "value": "none",
                    "source": "analytics-agent",
                }
            ],
            "patterns": [],
        },
    },
    "complex-investigation": {
        "bitcoin-aws-agent": {
            "factualObservations": [
                {
                    "metric": "exposure.degrees",
                    "value": "3",
                    "source": "bitcoin-aws-agent",
                }
            ],
            "patterns": [],
        },
        "ofac-agent": {
            "factualObservations": [
                {
                    "metric": "indirect.sanctions_links",
                    "value": "true",
                    "source": "ofac-agent",
                }
            ],
            "patterns": [],
        },
        "analytics-agent": {
            "factualObservations": [
                {"metric": "volume.total", "value": "500 BTC", "source": "analytics-agent"}
            ],
            "patterns": [
                {
                    "pattern": "INDIRECT_EXPOSURE",
                    "reference": "FATF Travel Rule",
                    "details": {"body": "Financial Action Task Force"},
                },
                {
                    "pattern": "HIGH_VOLUME",
                    "reference": "FinCEN",
                    "details": {"body": "U.S. Treasury"},
                },
            ],
            "requiresHumanReview": True,
            "hitlReason": "Complex multi-hop exposure",
        },
    },
}


@pytest.mark.asyncio
@pytest.mark.parametrize("case", GOLDEN_CASES, ids=[case["id"] for case in GOLDEN_CASES])
async def test_golden_dataset_interactions(
    monkeypatch: pytest.MonkeyPatch,
    case: dict[str, object],
) -> None:
    monkeypatch.setattr("supabolt_supervisor.graph.build_redis_saver", lambda _settings: None)

    async def fake_call(
        self: MCPToolsetManager,
        agent: str,
        tool: str,
        payload: dict[str, object],
    ):
        responses = GOLDEN_RESPONSES[payload["workflowId"]]  # type: ignore[index]
        if agent not in responses:
            raise AssertionError(f"Unexpected agent '{agent}' for scenario {payload['workflowId']}")
        return responses[agent]

    monkeypatch.setattr(MCPToolsetManager, "call", fake_call, raising=False)

    graph = create_graph(
        settings=SupaboltSettings(
            redis_url="redis://localhost:6379/0",
            langsmith_project=None,
            langsmith_tracing=False,
            supabase_jwt_secret="test",
            agent_max_concurrency=8,
        )
    )

    runtime_config = dict(case["runtime_config"])  # type: ignore[arg-type]
    initial_state: GraphState = {
        "factual_observations": [],
        "regulatory_patterns": [],
        "data_availability": [],
        "tool_results": {},
        "tool_errors": {},
        "requires_hitl": False,
        "hitl_reason": None,
        "runtime": {
            "workflow_id": case["id"],  # type: ignore[index]
            "tenant_id": "tenant-test",
            "entity": case["entity"],  # type: ignore[index]
            "runtime_config": runtime_config,
            "messages": [],
        },
    }

    try:
        result_state = cast(
            GraphState,
            await graph.ainvoke(
                initial_state,
                config={
                    "configurable": {
                        "thread_id": case["id"],  # type: ignore[index]
                        "checkpoint_ns": "tenant-test",
                    }
                },
            ),
        )
    except LGInterrupt as interrupt:
        payload_obj: dict[str, Any] | None = None
        if interrupt.args:
            first_arg = interrupt.args[0]
            if isinstance(first_arg, dict):
                payload_obj = first_arg
        if payload_obj and isinstance(payload_obj.get("state"), dict):
            result_state = cast(GraphState, payload_obj["state"])
        else:
            result_state = initial_state

    fact_sheet = build_fact_sheet(result_state)

    observed_agents = set((result_state.get("tool_results") or {}).keys())
    expected_agents = set(case["expected_agents"])  # type: ignore[index]
    assert observed_agents == expected_agents, (
        f"Agent mismatch for {case['id']}: "  # type: ignore[index]
        f"observed={observed_agents}, expected={expected_agents}"
    )

    pattern_names = {pattern["pattern"] for pattern in fact_sheet["patterns"]}
    expected_patterns = set(case["expected_patterns"])  # type: ignore[index]
    assert pattern_names == expected_patterns, (
        f"Pattern mismatch for {case['id']}: "  # type: ignore[index]
        f"observed={pattern_names}, expected={expected_patterns}"
    )

    expected_hitl = case["requires_human_review"]  # type: ignore[index]
    assert fact_sheet["requiresHumanReview"] == expected_hitl, (
        f"HITL mismatch for {case['id']}: "  # type: ignore[index]
        f"observed={fact_sheet['requiresHumanReview']}, expected={expected_hitl}"
    )
