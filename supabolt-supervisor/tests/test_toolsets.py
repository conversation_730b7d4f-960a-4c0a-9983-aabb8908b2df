from pathlib import Path

from supa<PERSON>_supervisor.toolsets import load_toolsets


def test_load_toolsets(tmp_path: Path) -> None:
    manifest = tmp_path / "toolsets.yaml"
    manifest.write_text(
        """
        alpha:
          description: Example set
          agents:
            - agent-a
            - agent-b
        """
    )

    toolsets = load_toolsets(manifest)

    assert "alpha" in toolsets
    assert [agent.name for agent in toolsets["alpha"].agents] == ["agent-a", "agent-b"]
