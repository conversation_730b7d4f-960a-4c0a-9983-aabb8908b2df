from supabolt_supervisor.utils.redis import _extract_module_names


def test_extract_module_names_from_dict_entries() -> None:
    reply = [{"name": b"ReJSON"}, {"name": "search"}]

    names = _extract_module_names(reply)

    assert {"rejson", "search"} <= names


def test_extract_module_names_from_array_pairs() -> None:
    reply = [[b"name", b"ReJSON"], ["name", "search"]]

    names = _extract_module_names(reply)

    assert {"rejson", "search"} <= names
