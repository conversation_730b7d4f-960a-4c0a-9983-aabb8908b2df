from supabolt_supervisor.mcp.process_client import MCPProcessClient


def test_normalize_tool_result_lifts_json_payload() -> None:
    result = {
        "content": [
            {"type": "text", "text": "ignored"},
            {
                "type": "json",
                "json": {
                    "factualObservations": [{"metric": "m1", "value": "v", "source": "agent"}],
                    "patterns": [{"pattern": "p1", "reference": "ref", "details": {}}],
                },
            },
        ]
    }

    normalized = MCPProcessClient._normalize_tool_result(result)

    assert normalized.get("factualObservations")
    assert normalized.get("patterns")
