# ADR 0001: High-Level Supervisor Architecture

- **Status:** Accepted
- **Date:** 2025-11-27 (updated 2025-12-XX)

## Context

Supabolt requires a Python supervisor that can orchestrate the TypeScript MCP agents without relying on
LangGraph 0.x patterns or third-party clients that we cannot audit. The system must honour FACTS ONLY,
support manifest-driven workflows, and expose a secure multi-tenant API surface.

## Decision

Build `supabolt-supervisor` as a Poetry-managed Python 3.11 project with the following pillars:

- **Custom MCP stdio client** – launch each agent process via a hardened JSON-RPC transport and enforce
  bounded timeouts/circuit-breakers inside our codebase.
- **Manifest-driven router** – `RouterConfig` loads toolsets, emits `Send[]` fan-out plans, and powers a
  LangGraph 1.x `StateGraph` that raises native interrupts when human review is required.
- **Structured compliance state** – All agent outputs are validated into Pydantic v2 models and merged into
  canonical fact sheets; no subjective scoring is allowed.
- **FastAPI control plane** – `/workflows` runs the orchestrator, `/metrics` exposes Prometheus counters,
  and Supabase JWT validation guards tenant isolation.
- **Redis checks** – Startup verifies Redis Stack modules (JSON + Search) so production deployments keep
  durability parity; LangGraph checkpointing remains an optional integration via `build_redis_saver`.

### Reducer semantics

- `reduce_hitl_reason` is intentionally order-dependent: when multiple branches request human review, the
  final merged state keeps the most recent non-empty reason to preserve operator context.

## Consequences

- We own the MCP orchestration surface end-to-end, enabling rigorous audits and eliminating dependency on
  unavailable packages.
- Redis remains the durability layer; missing modules surface as actionable startup errors.
- Tests can exercise the supervisor deterministically (golden dataset harness) without external services.
- Future ADRs will cover deployment hardening, observability, and optional checkpoint resumption semantics.
