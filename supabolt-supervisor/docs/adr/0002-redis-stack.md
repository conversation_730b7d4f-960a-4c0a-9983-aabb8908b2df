# ADR 0002: Redis Stack Modules Requirement

- **Status:** Accepted
- **Date:** 2025-11-27

## Context

`supabolt-supervisor` validates Redis Stack availability at startup so production deployments can persist
workflow evidence and (optionally) enable LangGraph checkpoint resumption. To support JSON documents and
search/index operations used by Supabolt workflows, Redis must provide the RedisJSON and RediSearch modules.
Vanilla Redis distributions do not include these modules.

## Decision

- Use the `redis/redis-stack-server` image for local development and integration testing. This image ships
  with RedisJSON and RediSearch enabled by default.
- At application startup, verify required modules by executing `JSON.INFO` and `FT._LIST`. Fail fast if either
  command is unavailable.
- Update `make redis-test` to execute the module check so developers catch misconfigured Redis instances
  before running the supervisor.

## Consequences

- Developers and CI pipelines run against Redis Stack, ensuring parity with production deployments.
- The supervisor reports explicit errors when modules are missing, preventing silent corruption of
  compliance evidence.
- Operations teams must provision Redis clusters (e.g., AWS ElastiCache or Supabase Redis) with RedisJSON
  and RediSearch enabled.
