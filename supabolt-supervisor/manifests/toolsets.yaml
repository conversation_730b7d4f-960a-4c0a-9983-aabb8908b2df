standard_screening:
  description: Baseline workflow for Bitcoin addresses and entities.
  agents:
    - name: bitcoin-aws-agent
      tool: investigateEntity
      parameters:
        entityType: address
    - name: ofac-agent
      tool: checkOFACStatus
      parameters:
        entityType: other
    - name: analytics-agent
      tool: analyzeComplianceData
sanctions_screening:
  description: Focused sanctions screening for clean addresses.
  agents:
    - name: ofac-agent
      tool: checkOFACStatus
      parameters:
        entityType: other
quick_sanctions_check:
  description: Parallel sanctions providers for rapid screening.
  agents:
    - name: sanctions-agent
      tool: checkSanctionsStatus
      parameters:
        entityType: other
    - name: ofac-agent
      tool: checkOFACStatus
lightning_compliance:
  description: Lightning Network channel and counterparty screening.
  agents:
    - name: lightning-agent
      tool: getNodeInfo
    - name: bitcoin-aws-agent
      tool: investigateEntity
      parameters:
        entityType: address
    - name: ofac-agent
      tool: checkOFACStatus
      parameters:
        entityType: other
    - name: analytics-agent
      tool: analyzeComplianceData
stablecoin_screening:
  description: Stablecoin entity screening with sanctions + analytics follow-up.
  agents:
    - name: usdc-agent
      tool: getUsdcBalance
    - name: ofac-agent
      tool: checkOFACStatus
      parameters:
        entityType: other
    - name: analytics-agent
      tool: analyzeComplianceData
