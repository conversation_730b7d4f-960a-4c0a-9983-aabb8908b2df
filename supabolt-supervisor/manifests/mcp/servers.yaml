servers:
  - name: bitcoin-aws-agent
    spawn:
      command:
        - npm
        - run
        - start
      cwd: ../bitcoin-aws-agent
    health:
      url: http://localhost:8001/health
    timeouts:
      request_seconds: 30
  - name: sanctions-agent
    spawn:
      command:
        - npm
        - run
        - start
      cwd: ../sanctions-agent
    health:
      url: http://localhost:8002/health
    timeouts:
      request_seconds: 30
  - name: analytics-agent
    spawn:
      command:
        - npm
        - run
        - start
      cwd: ../analytics-agent
    health:
      url: http://localhost:8003/health
    timeouts:
      request_seconds: 45
  - name: ofac-agent
    spawn:
      command:
        - npm
        - run
        - start
      cwd: ../ofac-agent
    health:
      url: http://localhost:8004/health
    timeouts:
      request_seconds: 45
  - name: lightning-agent
    spawn:
      command:
        - npm
        - run
        - start
      cwd: ../lightning-agent
    health:
      url: http://localhost:8005/health
    timeouts:
      request_seconds: 45
  - name: usdc-agent
    spawn:
      command:
        - npm
        - run
        - start
      cwd: ../usdc-agent
    health:
      url: http://localhost:8006/health
    timeouts:
      request_seconds: 45
