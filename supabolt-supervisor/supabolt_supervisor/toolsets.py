from __future__ import annotations

import json
from dataclasses import dataclass
from pathlib import Path
from typing import Any

import yaml


@dataclass(frozen=True)
class ToolsetAgentConfig:
    name: str
    tool: str
    parameters: dict[str, Any]
    tags: tuple[str, ...] = ()


@dataclass(frozen=True)
class ToolsetConfig:
    name: str
    agents: list[ToolsetAgentConfig]
    description: str | None = None


def load_toolsets(path: Path) -> dict[str, ToolsetConfig]:
    raw = yaml.safe_load(path.read_text())
    toolsets: dict[str, ToolsetConfig] = {}
    for key, value in raw.items():
        agents = value.get("agents")
        if not isinstance(agents, list) or not agents:
            raise ValueError(f"Toolset '{key}' must define a non-empty agent list.")

        parsed_agents: list[ToolsetAgentConfig] = []
        for entry in agents:
            if isinstance(entry, str):
                parsed_agents.append(ToolsetAgentConfig(name=entry, tool="analyzeCase", parameters={}))
                continue
            if not isinstance(entry, dict):
                raise ValueError(f"Invalid agent entry in toolset '{key}': {entry!r}")
            name = entry.get("name")
            tool = entry.get("tool")
            if not isinstance(name, str) or not name:
                raise ValueError(f"Agent entry in toolset '{key}' missing 'name'")
            if not isinstance(tool, str) or not tool:
                raise ValueError(f"Agent '{name}' in toolset '{key}' missing 'tool'")
            tags = entry.get("tags") or []
            if not isinstance(tags, list) or not all(isinstance(tag, str) for tag in tags):
                raise ValueError(f"Agent '{name}' tags must be a list of strings")
            parameters = entry.get("parameters") or {}
            parsed_agents.append(
                ToolsetAgentConfig(name=name, tool=tool, parameters=parameters, tags=tuple(tags)),
            )

        toolsets[key] = ToolsetConfig(
            name=key,
            agents=parsed_agents,
            description=value.get("description"),
        )
    return toolsets


def manifest_to_json(toolsets: dict[str, ToolsetConfig]) -> str:
    payload: dict[str, Any] = {
        key: {
            "agents": [
                {"name": agent.name, "tool": agent.tool, "tags": list(agent.tags)}
                for agent in cfg.agents
            ],
            "description": cfg.description,
        }
        for key, cfg in toolsets.items()
    }
    return json.dumps(payload, indent=2, sort_keys=True)
