from __future__ import annotations

import os
from dataclasses import dataclass


@dataclass(frozen=True)
class SupaboltSettings:
    """Runtime configuration sourced from process environment."""

    redis_url: str
    langsmith_project: str | None
    langsmith_tracing: bool
    supabase_jwt_secret: str | None
    agent_max_concurrency: int


def load_settings() -> SupaboltSettings:
    redis_url = os.getenv("REDIS_URL") or os.getenv("ELASTICACHE_URL")
    if not redis_url:
        raise RuntimeError("REDIS_URL or ELASTICACHE_URL must be configured for durability.")

    langsmith_project = os.getenv("LANGCHAIN_PROJECT")
    langsmith_tracing = os.getenv("LANGCHAIN_TRACING_V2", "false").lower() == "true"
    supabase_jwt_secret = os.getenv("SUPABASE_JWT_SECRET")

    concurrency_raw = (
        os.getenv("SUPABOLT_MAX_CONCURRENCY") or os.getenv("AGENT_MAX_CONCURRENCY") or "8"
    )
    try:
        agent_max_concurrency = int(concurrency_raw)
    except ValueError:
        agent_max_concurrency = 8
    if agent_max_concurrency < 1:
        agent_max_concurrency = 1

    return SupaboltSettings(
        redis_url=redis_url,
        langsmith_project=langsmith_project,
        langsmith_tracing=langsmith_tracing,
        supabase_jwt_secret=supabase_jwt_secret,
        agent_max_concurrency=agent_max_concurrency,
    )
