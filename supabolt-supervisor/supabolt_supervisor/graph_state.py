from __future__ import annotations

from typing import Annotated, Any, TypedDict

from langgraph.types import Send

from .state import (
    DataAvailability,
    FactualObservation,
    RegulatoryPattern,
    append_items,
    merge_dict,
    reduce_hitl_reason,
    reduce_requires_hitl,
)


class GraphState(TypedDict, total=False):
    """LangGraph mergeable state keyed by reducers for deterministic joins."""

    sends: list[Send]

    factual_observations: Annotated[list[FactualObservation], append_items]
    regulatory_patterns: Annotated[list[RegulatoryPattern], append_items]
    data_availability: Annotated[list[DataAvailability], append_items]

    tool_results: Annotated[dict[str, Any], merge_dict]
    tool_errors: Annotated[dict[str, str], merge_dict]

    requires_hitl: Annotated[bool, reduce_requires_hitl]
    hitl_reason: Annotated[str | None, reduce_hitl_reason]

    runtime: Annotated[dict[str, Any], merge_dict]


PartialGraphState = GraphState
