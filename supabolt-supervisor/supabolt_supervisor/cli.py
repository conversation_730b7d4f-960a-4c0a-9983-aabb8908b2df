from __future__ import annotations

import asyncio

from supa<PERSON>_supervisor.redis_client import RedisClientFactory
from supabolt_supervisor.utils.redis import verify_redis_modules

from .graph import create_graph
from .graph_state import GraphState
from .settings import load_settings


def main() -> None:
    settings = load_settings()
    redis_client = RedisClientFactory.build(settings)

    async def validate_modules() -> None:
        try:
            await verify_redis_modules(redis_client)
        finally:
            await redis_client.close()

    asyncio.run(validate_modules())
    graph = create_graph(settings)

    async def run() -> None:
        state: GraphState = {
            "factual_observations": [],
            "regulatory_patterns": [],
            "data_availability": [],
            "tool_results": {},
            "tool_errors": {},
            "requires_hitl": False,
            "hitl_reason": None,
            "runtime": {
                "workflow_id": "dry_run",
                "tenant_id": "cli",
                "entity": {},
                "runtime_config": {},
                "messages": [],
            },
        }

        await graph.ainvoke(
            state,
            config={
                "configurable": {
                    "thread_id": "dry_run",
                    "checkpoint_ns": "cli",
                }
            },
        )

    asyncio.run(run())


if __name__ == "__main__":
    main()
