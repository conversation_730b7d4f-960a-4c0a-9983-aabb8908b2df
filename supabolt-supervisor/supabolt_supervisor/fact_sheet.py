from __future__ import annotations

from collections.abc import Mapping
from datetime import UTC, datetime
from typing import Any

from .state import DataAvailability, FactualObservation, RegulatoryPattern


def _serialize_observation(observation: FactualObservation) -> dict[str, Any]:
    return {
        "metric": observation.metric,
        "value": observation.value,
        "source": observation.source,
    }


def _serialize_pattern(pattern: RegulatoryPattern) -> dict[str, Any]:
    return {
        "pattern": pattern.pattern,
        "reference": pattern.reference,
        "details": dict(pattern.details),
    }


def _serialize_availability(availability: DataAvailability) -> dict[str, Any]:
    payload: dict[str, Any] = {"agent": availability.agent, "status": availability.status}
    if availability.reason:
        payload["reason"] = availability.reason
    return payload


def build_fact_sheet(state: Mapping[str, Any]) -> dict[str, Any]:
    """Render fact sheet payload from dict-like GraphState."""

    runtime_raw = state.get("runtime")
    runtime = runtime_raw if isinstance(runtime_raw, Mapping) else {}
    factual_observations = state.get("factual_observations", []) or []
    regulatory_patterns = state.get("regulatory_patterns", []) or []
    data_availability = state.get("data_availability", []) or []

    return {
        "workflowId": runtime.get("workflow_id"),
        "entity": runtime.get("entity", {}),
        "analysisTimestamp": datetime.now(UTC).isoformat(),
        "factualObservations": [_serialize_observation(obs) for obs in factual_observations],
        "patterns": [_serialize_pattern(pattern) for pattern in regulatory_patterns],
        "dataAvailability": [_serialize_availability(entry) for entry in data_availability],
        "requiresHumanReview": bool(state.get("requires_hitl")),
        "hitlReason": state.get("hitl_reason"),
    }
