from __future__ import annotations

from collections.abc import Iterable
from typing import TypeVar

from pydantic import BaseModel, ConfigDict, Field


class FactualObservation(BaseModel):
    """Objective metric reported by an MCP tool."""

    model_config = ConfigDict(extra="forbid")

    metric: str
    value: str
    source: str


class RegulatoryPattern(BaseModel):
    """Detected compliance pattern with supporting citation."""

    model_config = ConfigDict(extra="forbid")

    pattern: str
    reference: str
    details: dict[str, str] = Field(default_factory=dict)


class DataAvailability(BaseModel):
    """Availability status for each agent response."""

    model_config = ConfigDict(extra="forbid")

    agent: str
    status: str
    reason: str | None = None


T = TypeVar("T")


# ----------------------------
# Pure reducers (functional)
# ----------------------------


def append_items(existing: Iterable[T] | None, updates: Iterable[T] | None) -> list[T]:
    """Reducer that appends items from the update onto existing values."""
    snapshot = list(existing or [])
    if updates:
        snapshot.extend(list(updates))
    return snapshot


def merge_dict(existing: dict[str, T] | None, updates: dict[str, T] | None) -> dict[str, T]:
    """Reducer that merges dictionaries while preferring the most recent values."""
    snapshot: dict[str, T] = dict(existing or {})
    if updates:
        snapshot.update(updates)
    return snapshot


def reduce_requires_hitl(existing: bool | None, update: bool | None) -> bool:
    """Reducer that flags human review if any update requires it."""
    return bool(existing) or bool(update)


def reduce_hitl_reason(existing: str | None, update: str | None) -> str | None:
    """Reducer that keeps the latest non-empty human-review reason (order-dependent)."""
    return update or existing
