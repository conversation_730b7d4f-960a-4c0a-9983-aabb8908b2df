from __future__ import annotations

from collections.abc import Callable
from dataclasses import dataclass
from typing import Any

from langgraph.types import Send
from langchain_core.runnables import Runnable<PERSON>ambda

from .graph_state import GraphState
from .toolsets import ToolsetAgentConfig, ToolsetConfig
from .utils.metrics import record_router_fanout


@dataclass(slots=True, frozen=True)
class RouterConfig:
    """Configuration for deterministic manifest-driven routing."""

    toolsets: dict[str, ToolsetConfig]
    default_toolset: str = "standard_screening"

    def resolve_toolset(self, requested: str | None) -> ToolsetConfig:
        name = requested or self.default_toolset
        if name not in self.toolsets:
            available = ", ".join(sorted(self.toolsets))
            raise ValueError(f"Unknown toolset '{name}'. Available: {available}")
        return self.toolsets[name]

    def resolve_agent(self, agent_name: str) -> ToolsetAgentConfig:
        for config in self.toolsets.values():
            for agent in config.agents:
                if agent.name == agent_name:
                    return agent
        available = ", ".join(
            sorted({agent.name for cfg in self.toolsets.values() for agent in cfg.agents})
        )
        raise ValueError(f"Unknown agent '{agent_name}'. Available: {available}")


def build_router_node(router_cfg: RouterConfig) -> RunnableLambda:
    """Pure planner that returns Send[] directives for LangGraph fan-out."""

    def router_node(state: GraphState) -> dict[str, list[Send]]:
        runtime: dict[str, Any] = state.get("runtime", {})
        runtime_config = runtime.get("runtime_config", {})

        requested_toolset = runtime_config.get("toolset")
        toolset = router_cfg.resolve_toolset(
            requested_toolset if isinstance(requested_toolset, str) else None
        )

        override_agents = runtime_config.get("agents")
        if override_agents and isinstance(override_agents, list):
            agents: list[ToolsetAgentConfig] = []
            for name in override_agents:
                if not isinstance(name, str):
                    raise ValueError("runtime_config.agents must contain string agent names")
                agents.append(router_cfg.resolve_agent(name))
        else:
            agents = toolset.agents

        sends: list[Send] = []
        for agent in agents:
            branch_runtime = dict(runtime)
            branch_state: GraphState = {
                "runtime": branch_runtime,
            }
            sends.append(
                Send(
                    f"agent__{agent.name}",
                    branch_state,
                )
            )

        if not sends:
            raise ValueError(f"Toolset '{toolset.name}' did not produce any agent targets")
        record_router_fanout(len(sends))
        return {"sends": sends}

    return RunnableLambda(router_node)
