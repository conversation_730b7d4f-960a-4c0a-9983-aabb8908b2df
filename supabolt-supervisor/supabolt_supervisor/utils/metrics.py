from __future__ import annotations

import time
from collections.abc import Generator
from contextlib import contextmanager

from prometheus_client import Counter, Histogram

AGENT_CALLS = Counter(
    "supabolt_agent_calls_total",
    "Total MCP agent calls executed by the supervisor",
    ("agent", "status"),
)

AGENT_LATENCY = Histogram(
    "supabolt_agent_latency_seconds",
    "Duration of MCP agent calls executed by the supervisor",
    ("agent",),
)

NODE_FAILURES = Counter(
    "supabolt_node_failures_total",
    "Supervisor node exceptions grouped by node name",
    ("node",),
)

INTERRUPTS = Counter(
    "supabolt_interrupts_total",
    "Total human-review interrupts raised by the supervisor (bounded reasons)",
    ("reason",),
)

ROUTER_FANOUT_SIZE = Histogram(
    "supabolt_router_fanout_size",
    "Number of agent branches emitted by the router per run",
    buckets=(1, 2, 3, 4, 6, 8, 12, 16),
)


def record_agent_call(agent: str, success: bool, duration_seconds: float) -> None:
    AGENT_CALLS.labels(agent=agent, status="success" if success else "error").inc()
    AGENT_LATENCY.labels(agent=agent).observe(max(duration_seconds, 0.0))


def record_node_failure(node: str) -> None:
    NODE_FAILURES.labels(node=node).inc()


def record_interrupt(reason: str) -> None:
    INTERRUPTS.labels(reason=reason).inc()


def record_router_fanout(size: int) -> None:
    ROUTER_FANOUT_SIZE.observe(max(0, size))


@contextmanager
def time_agent_call(agent: str) -> Generator[None, None, None]:
    start = time.perf_counter()
    try:
        yield
    finally:
        duration = time.perf_counter() - start
        AGENT_LATENCY.labels(agent=agent).observe(max(duration, 0.0))


_REASON_MAP: tuple[tuple[str, str], ...] = (
    ("sanction", "sanctions_match"),
    ("whale", "whale"),
    ("mix", "mixing_service"),
    ("tornado", "mixing_service"),
    ("velocity", "high_velocity"),
    ("indirect", "indirect_exposure"),
    ("exposure", "indirect_exposure"),
    ("link", "indirect_exposure"),
    ("volume", "high_volume"),
    ("manual", "manual_review"),
    ("review", "manual_review"),
)


def normalize_hitl_reason(raw: str | None) -> str:
    if not raw:
        return "manual_review"
    lowered = raw.lower()
    for needle, label in _REASON_MAP:
        if needle in lowered:
            return label
    return "other"
