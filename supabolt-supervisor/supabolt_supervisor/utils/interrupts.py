from __future__ import annotations

# ruff: noqa: I001

from typing import TYPE_CHECKING

# LangGraph 1.x renamed the interrupt class a couple of times; alias defensively.
if TYPE_CHECKING:  # pragma: no cover - import for typing only
    from langgraph.errors import GraphInterrupt as LGInterrupt
else:  # pragma: no cover - runtime resolution
    try:
        from langgraph.errors import GraphInterrupt as LGInterrupt
    except Exception:
        try:
            from langgraph.errors import Interrupt as LGInterrupt
        except Exception:

            class LGInterrupt(Exception):
                """Fallback interrupt when LangGraph is unavailable at import time."""

                pass


__all__ = ["LGInterrupt"]
