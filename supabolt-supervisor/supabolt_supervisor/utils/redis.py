from __future__ import annotations

from collections.abc import Iterable
from typing import Any

from redis.asyncio import Redis

_REQUIRED_MODULES = {"rejson", "search"}


def _extract_module_names(module_reply: Iterable[Any]) -> set[str]:
    names: set[str] = set()
    for entry in module_reply:
        if isinstance(entry, dict):
            name = entry.get("name")
            if isinstance(name, bytes):
                names.add(name.decode().lower())
            elif isinstance(name, str):
                names.add(name.lower())
        # Using a tuple keeps isinstance() compatible across Python runtimes.
        elif isinstance(entry, (list, tuple)):  # noqa: UP038
            # Response alternates between field/value pairs
            for idx, value in enumerate(entry):
                if idx + 1 >= len(entry):
                    continue
                if not isinstance(value, (bytes, str)):  # noqa: UP038
                    continue
                if value in {b"name", "name"}:
                    next_value = entry[idx + 1]
                    if isinstance(next_value, bytes):
                        names.add(next_value.decode().lower())
                    elif isinstance(next_value, str):
                        names.add(next_value.lower())
    return names


async def verify_redis_modules(client: Redis[Any]) -> None:
    """Ensure RedisJSON and RediSearch modules are available."""

    try:
        module_reply = await client.execute_command("MODULE", "LIST")  # type: ignore[no-untyped-call]
    except Exception as exc:  # pragma: no cover - network errors bubbled up to caller
        raise RuntimeError("Unable to query Redis modules") from exc

    names = _extract_module_names(module_reply)
    missing = sorted(_REQUIRED_MODULES.difference(names))
    if missing:
        raise RuntimeError("Missing required Redis modules: " + ", ".join(missing))

    # Ensure FT command namespace responds
    try:
        await client.execute_command("FT._LIST")  # type: ignore[no-untyped-call]
    except Exception as exc:  # pragma: no cover - indicates RediSearch missing
        raise RuntimeError("RediSearch module unavailable or unresponsive") from exc

    # Sanity check JSON functionality
    try:
        await client.execute_command("JSON.DEBUG", "MEMORY", "__modules_probe__")  # type: ignore[no-untyped-call]
    except Exception:
        # JSON.DEBUG fails if key missing but command exists; that's acceptable
        pass
