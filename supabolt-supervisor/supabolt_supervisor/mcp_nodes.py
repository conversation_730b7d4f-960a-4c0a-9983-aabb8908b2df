from __future__ import annotations

import time
from collections.abc import Awaitable, Callable
from typing import Any, cast

import structlog
from pydantic import ValidationError

from .graph_state import GraphState, PartialGraphState
from .mcp import MCPToolsetManager
from .state import DataAvailability, FactualObservation, RegulatoryPattern
from .toolsets import ToolsetAgentConfig
from .utils.metrics import record_agent_call, record_node_failure

LOGGER = structlog.get_logger("supabolt.supervisor.agent")


def build_agent_node(
    manager: MCPToolsetManager, agent: str, tool: str, agent_cfg: ToolsetAgentConfig
) -> Callable[[GraphState, dict[str, Any] | None], Awaitable[PartialGraphState]]:
    """Create a LangGraph node callable for a given agent/tool pair."""

    async def agent_node(
        state: GraphState, payload: dict[str, Any] | None = None
    ) -> PartialGraphState:
        runtime = state.get("runtime", {})
        workflow_id = runtime.get("workflow_id")
        LOGGER.info("agent_node.start", agent=agent, tool=tool, workflow_id=workflow_id)
        started = time.perf_counter()
        delta: PartialGraphState = {}

        try:
            selected_tool = (payload or {}).get("tool", tool)
            entity_data = runtime.get("entity", {}) or {}
            runtime_config = runtime.get("runtime_config", {})

            # --- 🧩 Normalize entity fields (LangSmith preferred format) ---
            # Use explicit None checks instead of 'or' to preserve falsy valid values
            entity_id = entity_data.get("entityId")
            if entity_id is None:
                entity_id = entity_data.get("value")
            if entity_id is None:
                entity_id = entity_data.get("channelId")

            entity_type = entity_data.get("entityType")
            if entity_type is None:
                entity_type = entity_data.get("type")

            if not entity_id:
                raise ValueError(
                    "Missing required entity field: expected 'entityId', 'value', or 'channelId'."
                )

            # Extract entityType from manifest params or fallback to runtime entity type
            agent_entity_type = agent_cfg.parameters.get("entityType", entity_type)

            # Base tool parameters (common to all)
            tool_params: dict[str, Any] = {
                "tenantId": runtime.get("tenant_id"),
                "workflowId": workflow_id,
                "runtimeConfig": runtime_config,
            }

            # --- 🧠 Tool‑specific parameter mapping ---
            if agent == "bitcoin-aws-agent" and selected_tool == "investigateEntity":
                # Bitcoin investigateEntity expects: entity{entityId, entityType}, network, options
                # Include workflowId for tracking (Zod strips unknown fields)
                network = runtime_config.get("network")
                if network is None:
                    network = "bitcoin"

                tool_params = {
                    "workflowId": workflow_id,  # For tracking/mocking
                    "entity": {
                        "entityId": entity_id,
                        "entityType": agent_entity_type,
                    },
                    "network": network,
                }
                if "options" in agent_cfg.parameters:
                    tool_params["options"] = agent_cfg.parameters["options"]

            elif agent == "sanctions-agent" and selected_tool == "checkSanctionsStatus":
                # Sanctions checkSanctionsStatus expects: entityId, entityType (optional)
                tool_params.update(
                    {
                        "entityId": entity_id,
                    }
                )
                if agent_entity_type:
                    tool_params["entityType"] = agent_entity_type

            elif agent == "ofac-agent" and selected_tool == "checkOFACStatus":
                # OFAC checkOFACStatus expects: entityId, entityType (optional)
                tool_params.update(
                    {
                        "entityId": entity_id,
                    }
                )
                if agent_entity_type:
                    tool_params["entityType"] = agent_entity_type

            elif agent == "analytics-agent" and selected_tool == "analyzeComplianceData":
                # Analytics analyzeComplianceData expects: entityId
                tool_params.update(
                    {
                        "entityId": entity_id,
                    }
                )

            else:
                # Fallback: generic parameter mapping for unknown tools
                tool_params.update(
                    {
                        "entityId": entity_id,
                        "entityType": agent_entity_type,
                        "entity": entity_id,
                        "network": runtime_config.get("network"),
                    }
                )

            # Log for debugging / LangSmith trace visibility
            LOGGER.debug(
                "agent_node.call",
                agent=agent,
                tool=selected_tool,
                tool_params=tool_params,
            )

            # --- Execute MCP Tool Call ---
            response = await manager.call(agent, selected_tool, tool_params)
            delta["tool_results"] = {agent: response}

        except Exception as exc:  # pragma: no cover - bubbled to HITL/availability handling
            duration = time.perf_counter() - started
            record_agent_call(agent, success=False, duration_seconds=duration)
            record_node_failure(f"agent__{agent}")
            message = str(exc)
            LOGGER.warning("agent_node.error", agent=agent, error=message)
            delta["tool_errors"] = {agent: message}
            delta["data_availability"] = [
                DataAvailability(agent=agent, status="unavailable", reason=message)
            ]
            return delta

        # --- 🧩 Process Observations ---
        observations_raw = response.get("factualObservations", []) or []
        patterns_raw = response.get("patterns", []) or response.get("regulatoryPatterns", []) or []

        observations: list[FactualObservation] = []
        for item in observations_raw:
            try:
                observation = FactualObservation.model_validate(item)
            except ValidationError as exc:
                LOGGER.warning(
                    "agent_node.observation_invalid",
                    agent=agent,
                    error=str(exc),
                    payload=item,
                )
                continue
            observations.append(observation)
        if observations:
            delta["factual_observations"] = observations

        patterns: list[RegulatoryPattern] = []
        for item in patterns_raw:
            try:
                pattern = RegulatoryPattern.model_validate(item)
            except ValidationError as exc:
                LOGGER.warning(
                    "agent_node.pattern_invalid",
                    agent=agent,
                    error=str(exc),
                    payload=item,
                )
                continue
            patterns.append(pattern)
        if patterns:
            delta["regulatory_patterns"] = patterns

        # --- 🧩 Handle HITL (Human‑in‑the‑loop) ---
        requires_hitl = bool(
            response.get("requiresHumanReview")
            or response.get("requires_hitl")
            or response.get("requiresHitl")
        )
        if requires_hitl:
            delta["requires_hitl"] = True
            reason = response.get("hitlReason")
            delta["hitl_reason"] = reason or f"{agent} requested review"

        # --- ✅ Success metrics ---
        duration = time.perf_counter() - started
        record_agent_call(agent, success=True, duration_seconds=duration)
        delta.setdefault("data_availability", []).append(
            DataAvailability(agent=agent, status="available")
        )
        return delta

    return agent_node