from __future__ import annotations

from typing import Any

from .settings import SupaboltSettings

DEFAULT_TTL_MINUTES = 60 * 24 * 7

RedisSaverType = Any


def build_redis_saver(settings: SupaboltSettings) -> RedisSaverType:
    """Create a RedisSaver using mission-critical defaults."""

    try:
        from langgraph.checkpoint.redis import RedisSaver  # type: ignore[import-not-found]
    except ImportError as exc:  # pragma: no cover - surfaced during startup
        raise RuntimeError(
            "langgraph.checkpoint.redis not available. Ensure langgraph extras are installed"
        ) from exc

    return RedisSaver.from_url(
        settings.redis_url,
        default_ttl=DEFAULT_TTL_MINUTES,
        refresh_on_read=True,
    )
