from __future__ import annotations

import asyncio
from dataclasses import dataclass
from pathlib import Path
from typing import Any

import yaml

from .process_client import MCPProcessClient, MCPProcessConfig


@dataclass(slots=True, frozen=True)
class MCPServerConfig:
    name: str
    command: list[str]
    cwd: Path | None
    health_url: str | None
    timeout_seconds: float
    env: dict[str, str]


def load_mcp_config(path: Path) -> dict[str, MCPServerConfig]:
    raw = yaml.safe_load(path.read_text())
    servers: dict[str, MCPServerConfig] = {}
    for entry in raw.get("servers", []):
        name = entry["name"]
        spawn = entry["spawn"]
        command = spawn.get("command")
        if not isinstance(command, list) or not command:
            raise ValueError(f"Server '{name}' spawn.command must be a non-empty list")
        cwd = spawn.get("cwd")
        env = spawn.get("env") or {}
        if not isinstance(env, dict):
            raise ValueError(f"Server '{name}' spawn.env must be a mapping")
        health_url = (entry.get("health") or {}).get("url")
        timeout = float((entry.get("timeouts") or {}).get("request_seconds", 30))
        servers[name] = MCPServerConfig(
            name=name,
            command=command,
            cwd=Path(cwd).resolve() if cwd else None,
            health_url=health_url,
            timeout_seconds=timeout,
            env={str(key): str(value) for key, value in env.items()},
        )
    return servers


class MCPToolsetManager:
    def __init__(self, servers: dict[str, MCPServerConfig]):
        self._servers = servers
        self._clients: dict[str, MCPProcessClient] = {}
        self._client_lock = asyncio.Lock()

    async def ensure_client(self, name: str) -> MCPProcessClient:
        async with self._client_lock:
            client = self._clients.get(name)
            if client is not None:
                return client
            config = self._servers.get(name)
            if config is None:
                raise KeyError(f"Unknown MCP server '{name}'")
            process_config = MCPProcessConfig(
                name=config.name,
                command=config.command,
                cwd=str(config.cwd) if config.cwd else None,
                env=config.env,
                request_timeout=config.timeout_seconds,
            )
            client = MCPProcessClient(process_config)
            self._clients[name] = client
            return client

    async def call(self, agent: str, tool: str, payload: dict[str, Any]) -> dict[str, Any]:
        client = await self.ensure_client(agent)
        return await client.call_tool(tool, payload)

    async def shutdown(self) -> None:
        tasks = [client.stop() for client in self._clients.values()]
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        self._clients.clear()
