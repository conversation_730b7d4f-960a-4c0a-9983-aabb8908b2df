from __future__ import annotations

import asyncio
import json
import os
from asyncio.subprocess import Process
from dataclasses import dataclass
from typing import Any

import structlog

LATEST_PROTOCOL_VERSION = os.getenv("MCP_PROTOCOL_VERSION", "2025-06-18")

logger = structlog.get_logger("supabolt.mcp.process_client")


class MCPProcessError(RuntimeError):
    """Raised when the MCP process encounters an unrecoverable error."""


@dataclass(slots=True, frozen=True)
class MCPProcessConfig:
    name: str
    command: list[str]
    cwd: str | None
    env: dict[str, str]
    request_timeout: float


class MCPProcessClient:
    """Minimal MCP stdio client that speaks JSON-RPC 2.0 to Supabolt agents."""

    def __init__(self, config: MCPProcessConfig) -> None:
        self._config = config
        self._process: Process | None = None
        self._reader_task: asyncio.Task[None] | None = None
        self._stderr_task: asyncio.Task[None] | None = None
        self._write_lock = asyncio.Lock()
        self._pending: dict[int, asyncio.Future[Any]] = {}
        self._next_id = 1
        self._shutdown = False
        self._server_capabilities: dict[str, Any] | None = None
        self._start_lock = asyncio.Lock()

    async def start(self) -> None:
        async with self._start_lock:
            if self._process is not None and self._process.returncode is None:
                return

            if self._process is not None and self._process.returncode is not None:
                self._cleanup_process()

            logger.info(
                "mcp_process.start",
                agent=self._config.name,
                command=self._config.command,
                cwd=self._config.cwd,
            )
            self._process = await asyncio.create_subprocess_exec(
                *self._config.command,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self._config.cwd,
                env={**os.environ, **self._config.env},
            )

            assert self._process.stdout and self._process.stdin

            self._shutdown = False
            self._reader_task = asyncio.create_task(
                self._read_loop(), name=f"mcp-reader-{self._config.name}"
            )
            if self._process.stderr is not None:
                self._stderr_task = asyncio.create_task(
                    self._stderr_loop(), name=f"mcp-stderr-{self._config.name}"
                )

            await self._initialize()

    async def call_tool(self, tool: str, arguments: dict[str, Any]) -> dict[str, Any]:
        await self.start()
        response = await self._request(
            method="tools/call",
            params={"name": tool, "arguments": arguments},
            timeout=self._config.request_timeout,
        )
        if not isinstance(response, dict):
            raise MCPProcessError(
                f"Invalid response payload from {self._config.name}: {response!r}"
            )
        return self._normalize_tool_result(response)

    async def stop(self) -> None:
        self._shutdown = True
        if self._reader_task:
            self._reader_task.cancel()
        if self._stderr_task:
            self._stderr_task.cancel()
        if self._process and self._process.returncode is None:
            self._process.terminate()
            try:
                await asyncio.wait_for(self._process.wait(), timeout=5)
            except TimeoutError:
                self._process.kill()
        self._cleanup_process()
        self._shutdown = False

    # ------------------------------------------------------------------
    async def _initialize(self) -> None:
        params = {
            "protocolVersion": LATEST_PROTOCOL_VERSION,
            "capabilities": {"tools": {}},
            "clientInfo": {"name": "supabolt-supervisor", "version": "1.0.0"},
        }
        result = await self._request("initialize", params, timeout=self._config.request_timeout)
        if not isinstance(result, dict):
            raise MCPProcessError(
                f"Invalid initialize response from {self._config.name}: {result!r}"
            )
        self._server_capabilities = result.get("capabilities", {})
        await self._send_notification("notifications/initialized")

    async def _read_loop(self) -> None:
        assert self._process and self._process.stdout
        try:
            while not self._process.stdout.at_eof():
                raw = await self._process.stdout.readline()
                if not raw:
                    break
                raw = raw.strip()
                if not raw:
                    continue
                try:
                    message = json.loads(raw)
                except json.JSONDecodeError:  # pragma: no cover - logged for diagnosis
                    logger.warning(
                        "mcp_process.invalid_json",
                        agent=self._config.name,
                        payload=raw.decode("utf-8", "ignore"),
                    )
                    continue
                await self._handle_message(message)
        except asyncio.CancelledError:
            pass
        except Exception as exc:  # pragma: no cover
            logger.exception(
                "mcp_process.reader_error",
                agent=self._config.name,
                error=str(exc),
            )
        finally:
            if not self._shutdown:
                await self._fail_pending("Process closed")
            self._cleanup_process()

    async def _stderr_loop(self) -> None:
        assert self._process and self._process.stderr
        try:
            while not self._process.stderr.at_eof():
                line = await self._process.stderr.readline()
                if not line:
                    break
                logger.warning(
                    "mcp_process.stderr",
                    agent=self._config.name,
                    message=line.decode("utf-8", "ignore").rstrip(),
                )
        except asyncio.CancelledError:
            pass

    async def _handle_message(self, message: dict[str, Any]) -> None:
        if "id" in message:
            await self._handle_response(message)
        elif "method" in message:
            logger.debug(
                "mcp_process.notification",
                agent=self._config.name,
                method=message.get("method"),
            )
        else:
            logger.debug(
                "mcp_process.unknown_message",
                agent=self._config.name,
                payload=message,
            )

    async def _handle_response(self, message: dict[str, Any]) -> None:
        message_id = message.get("id")
        if not isinstance(message_id, int):
            logger.warning(
                "mcp_process.invalid_id",
                agent=self._config.name,
                payload=message,
            )
            return
        future = self._pending.pop(message_id, None)
        if not future:
            logger.warning(
                "mcp_process.unmatched_response",
                agent=self._config.name,
                payload=message,
            )
            return
        if "error" in message:
            error = message["error"]
            future.set_exception(MCPProcessError(json.dumps(error)))
        else:
            future.set_result(message.get("result"))

    async def _request(self, method: str, params: dict[str, Any], timeout: float) -> Any:
        if self._process is None or self._process.stdin is None:
            raise MCPProcessError("Process not started")

        message_id = self._next_id
        self._next_id += 1
        future: asyncio.Future[Any] = asyncio.get_event_loop().create_future()
        self._pending[message_id] = future

        payload: dict[str, Any] = {
            "jsonrpc": "2.0",
            "id": message_id,
            "method": method,
            "params": params,
        }

        data = json.dumps(payload, separators=(",", ":")) + "\n"
        async with self._write_lock:
            self._process.stdin.write(data.encode("utf-8"))
            await self._process.stdin.drain()

        try:
            return await asyncio.wait_for(future, timeout=timeout)
        finally:
            self._pending.pop(message_id, None)

    async def _send_notification(self, method: str, params: dict[str, Any] | None = None) -> None:
        if self._process is None or self._process.stdin is None:
            return
        payload: dict[str, Any] = {
            "jsonrpc": "2.0",
            "method": method,
        }
        if params:
            payload["params"] = params
        data = json.dumps(payload, separators=(",", ":")) + "\n"
        async with self._write_lock:
            self._process.stdin.write(data.encode("utf-8"))
            await self._process.stdin.drain()

    async def _fail_pending(self, message: str) -> None:
        for future in self._pending.values():
            if not future.done():
                future.set_exception(MCPProcessError(message))
        self._pending.clear()
        self._process = None

    def _cleanup_process(self) -> None:
        self._reader_task = None
        self._stderr_task = None
        self._process = None

    @staticmethod
    def _normalize_tool_result(result: dict[str, Any]) -> dict[str, Any]:
        """Convert MCP result payload into flattened dict expected by orchestrator."""

        if result.get("isError"):
            error = result.get("content") or result.get("structuredContent") or {}
            raise MCPProcessError(json.dumps(error))

        if "structuredContent" in result and isinstance(result["structuredContent"], dict):
            return result["structuredContent"]

        # Some tools return standard content array [{type,text/json}]
        if "content" in result:
            content = result["content"]
            if isinstance(content, list):
                for item in content:
                    if isinstance(item, dict) and item.get("type") == "json":
                        json_payload = item.get("json")
                        if isinstance(json_payload, dict):
                            return json_payload
            return {"content": content}

        return result
