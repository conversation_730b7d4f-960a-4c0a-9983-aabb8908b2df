from __future__ import annotations

from pathlib import Path
from typing import Any, Literal, cast, overload

import structlog
from langgraph.graph import END, START, StateGraph

from .graph_state import GraphState
from .mcp import MCPToolsetManager, load_mcp_config
from .mcp_nodes import build_agent_node
from .redis_manager import build_redis_saver
from .routing import RouterConfig, build_router_node
from .settings import SupaboltSettings, load_settings
from .toolsets import ToolsetAgentConfig, load_toolsets
from .utils.interrupts import LGInterrupt
from .utils.metrics import normalize_hitl_reason, record_interrupt

PROJECT_ROOT = Path(__file__).resolve().parents[1]
TOOLSET_PATH = PROJECT_ROOT / "manifests" / "toolsets.yaml"
SERVER_MANIFEST_PATH = PROJECT_ROOT / "manifests" / "mcp" / "servers.yaml"

LOGGER = structlog.get_logger("supabolt.graph")


def _collect_agent_configs(toolsets: RouterConfig) -> dict[str, ToolsetAgentConfig]:
    registry: dict[str, ToolsetAgentConfig] = {}
    for cfg in toolsets.toolsets.values():
        for agent in cfg.agents:
            registry[agent.name] = agent
    return registry


@overload
def create_graph(
    settings: SupaboltSettings | None = None, *, include_manager: Literal[True]
) -> tuple[Any, MCPToolsetManager]: ...


@overload
def create_graph(
    settings: SupaboltSettings | None = None, *, include_manager: Literal[False] = False
) -> Any: ...


def create_graph(
    settings: SupaboltSettings | None = None, *, include_manager: bool = False
) -> Any | tuple[Any, MCPToolsetManager]:
    """Construct the LangGraph 1.x StateGraph for the supervisor workflow."""

    runtime_settings = settings or load_settings()

    router_cfg = RouterConfig(toolsets=load_toolsets(TOOLSET_PATH))
    agent_registry = _collect_agent_configs(router_cfg)
    manager = MCPToolsetManager(load_mcp_config(SERVER_MANIFEST_PATH))

    graph = StateGraph(GraphState)

    graph.add_node("start", lambda state: state)

    # Dynamic fan-out via conditional edges (LangGraph 1.x pattern)
    # build_router_node() returns a callable that examines state and returns Send[]
    # LangGraph automatically routes to target agents specified in Send objects
    router_runnable = build_router_node(router_cfg)
    graph.add_node("router", router_runnable)

    # Register agent nodes
    for agent_name, agent_cfg in agent_registry.items():
        graph.add_node(
            f"agent__{agent_name}",  # Nodes are unique to each agent/tool pair.
            cast(Any, build_agent_node(manager, agent_name, agent_cfg.tool, agent_cfg)),
        )

    graph.add_node("join", lambda state: state)

    async def hitl_node(state: GraphState) -> GraphState:
        if state.get("requires_hitl"):
            reason = state.get("hitl_reason") or "Human review required"
            record_interrupt(normalize_hitl_reason(reason))
            raise cast(
                Exception,
                LGInterrupt({"reason": reason, "state": state}),  # type: ignore[arg-type]
            )
        return state

    graph.add_node("hitl", hitl_node)

    async def fact_sheet_node(state: GraphState) -> GraphState:
        return state

    graph.add_node("fact_sheet", fact_sheet_node)

    graph.add_edge(START, "start")
    graph.add_edge("start", "router")

    # Fan-out triggered from 'router' node by returning Send() objects
    graph.add_conditional_edges("router", lambda state: state["sends"])

    # All agent nodes converge to join
    for agent_name in agent_registry:
        graph.add_edge(f"agent__{agent_name}", "join")

    def route_post_join(state: GraphState) -> str:
        return "hitl" if state.get("requires_hitl") else "fact_sheet"

    graph.add_conditional_edges(
        "join",
        route_post_join,
        {"hitl": "hitl", "fact_sheet": "fact_sheet"},
    )
    graph.add_edge("hitl", END)
    graph.add_edge("fact_sheet", END)

    saver = None
    try:
        saver = build_redis_saver(runtime_settings)
    except RuntimeError as exc:  # pragma: no cover - local dev without redis extras
        LOGGER.warning("redis.checkpointer_unavailable", error=str(exc))

    compiled: Any = graph.compile(checkpointer=saver)

    try:
        limiter_value = max(1, runtime_settings.agent_max_concurrency)
        for agent_name in agent_registry:
            compiled.add_concurrency_limiter(f"agent__{agent_name}", limiter_value)
    except Exception as exc:  # pragma: no cover - API drift across LangGraph versions
        LOGGER.warning("graph.concurrency_limiter_unavailable", error=str(exc))

    if include_manager:
        return compiled, manager

    return compiled
