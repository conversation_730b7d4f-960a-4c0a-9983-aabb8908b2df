["tests/test_golden_dataset.py::test_golden_dataset_interactions[clean-bitcoin-address]", "tests/test_golden_dataset.py::test_golden_dataset_interactions[clean-lightning-channel]", "tests/test_golden_dataset.py::test_golden_dataset_interactions[clean-transaction]", "tests/test_golden_dataset.py::test_golden_dataset_interactions[complex-investigation]", "tests/test_golden_dataset.py::test_golden_dataset_interactions[cross-chain-movement]", "tests/test_golden_dataset.py::test_golden_dataset_interactions[high-velocity-pattern]", "tests/test_golden_dataset.py::test_golden_dataset_interactions[mixing-service-usage]", "tests/test_golden_dataset.py::test_golden_dataset_interactions[sanctioned-bitcoin-address]", "tests/test_golden_dataset.py::test_golden_dataset_interactions[sanctioned-lightning-counterparty]", "tests/test_golden_dataset.py::test_golden_dataset_interactions[usdc-whale-address]", "tests/test_process_client_normalize.py::test_normalize_tool_result_lifts_json_payload", "tests/test_redis_modules.py::test_extract_module_names_from_array_pairs", "tests/test_redis_modules.py::test_extract_module_names_from_dict_entries", "tests/test_toolsets.py::test_load_toolsets"]